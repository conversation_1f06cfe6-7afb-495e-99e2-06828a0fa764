"""
AI Video Detection - Emergency Interface
Fallback interface when main application fails to start
"""

import tkinter as tk
from tkinter import messagebox
import sys
import os

def create_emergency_interface():
    """Emergency interface when everything fails"""
    try:
        root = tk.Tk()
        root.title("AI Video Detection - Emergency Mode")
        root.geometry("500x400")
        root.configure(bg='#F8F9FA')
        
        # Center window
        root.update_idletasks()
        x = (root.winfo_screenwidth() // 2) - (500 // 2)
        y = (root.winfo_screenheight() // 2) - (400 // 2)
        root.geometry(f"500x400+{x}+{y}")
        
        # Main frame
        main_frame = tk.Frame(root, bg='#F8F9FA')
        main_frame.pack(fill='both', expand=True, padx=20, pady=20)
        
        # Header
        header_frame = tk.Frame(main_frame, bg='#DC3545', height=80)
        header_frame.pack(fill='x', pady=(0, 20))
        header_frame.pack_propagate(False)
        
        header_content = tk.Frame(header_frame, bg='#DC3545')
        header_content.pack(expand=True)
        
        title_label = tk.Label(header_content,
                              text="⚠️ AI Video Detection - Emergency Mode",
                              font=('Arial', 16, 'bold'),
                              bg='#DC3545',
                              fg='white')
        title_label.pack(expand=True)
        
        # Content
        content_frame = tk.Frame(main_frame, bg='white', relief='solid', bd=2)
        content_frame.pack(fill='both', expand=True)
        
        content_title = tk.Label(content_frame,
                                text="🚨 System Error Detected",
                                font=('Arial', 14, 'bold'),
                                bg='white',
                                fg='#DC3545')
        content_title.pack(pady=20)
        
        error_text = """
The main application failed to start properly.
This emergency interface provides basic troubleshooting options.

Common Issues:
• Missing Python dependencies
• Corrupted installation files
• Permission problems
• Camera access issues
• Import path conflicts

Recommended Actions:
1. Check Python installation
2. Reinstall required packages
3. Verify file permissions
4. Restart the application
        """
        
        error_label = tk.Label(content_frame,
                              text=error_text,
                              font=('Arial', 10),
                              bg='white',
                              fg='#2C3E50',
                              justify='left')
        error_label.pack(pady=20, padx=20)
        
        # Buttons
        button_frame = tk.Frame(content_frame, bg='white')
        button_frame.pack(pady=20)
        
        def test_camera():
            try:
                import cv2
                cap = cv2.VideoCapture(0)
                if cap.isOpened():
                    messagebox.showinfo("Camera Test", "✅ Camera is working!")
                    cap.release()
                else:
                    messagebox.showerror("Camera Test", "❌ No camera detected")
            except ImportError:
                messagebox.showerror("Camera Test", "❌ OpenCV not available")
            except Exception as e:
                messagebox.showerror("Camera Test", f"❌ Error: {str(e)}")
        
        def check_dependencies():
            deps = ['cv2', 'numpy', 'PIL', 'tkinter']
            available = []
            missing = []
            
            for dep in deps:
                try:
                    __import__(dep)
                    available.append(dep)
                except ImportError:
                    missing.append(dep)
            
            result = f"Available: {', '.join(available)}\n"
            if missing:
                result += f"Missing: {', '.join(missing)}"
            else:
                result += "All dependencies available!"
            
            messagebox.showinfo("Dependency Check", result)
        
        def restart_app():
            root.destroy()
            try:
                # Try to restart main app
                import subprocess
                subprocess.Popen([sys.executable, "main.py"])
            except:
                messagebox.showerror("Restart", "Failed to restart application")
        
        # Test buttons
        test_btn = tk.Button(button_frame,
                            text="📷 Test Camera",
                            font=('Arial', 10, 'bold'),
                            bg='#007BFF',
                            fg='white',
                            command=test_camera,
                            padx=15, pady=5)
        test_btn.pack(side='left', padx=5)
        
        deps_btn = tk.Button(button_frame,
                            text="🔍 Check Dependencies",
                            font=('Arial', 10, 'bold'),
                            bg='#28A745',
                            fg='white',
                            command=check_dependencies,
                            padx=15, pady=5)
        deps_btn.pack(side='left', padx=5)
        
        restart_btn = tk.Button(button_frame,
                               text="🔄 Restart App",
                               font=('Arial', 10, 'bold'),
                               bg='#FFC107',
                               fg='black',
                               command=restart_app,
                               padx=15, pady=5)
        restart_btn.pack(side='left', padx=5)
        
        exit_btn = tk.Button(button_frame,
                            text="🚪 Exit",
                            font=('Arial', 10, 'bold'),
                            bg='#DC3545',
                            fg='white',
                            command=root.destroy,
                            padx=15, pady=5)
        exit_btn.pack(side='left', padx=5)
        
        # Footer
        footer_label = tk.Label(content_frame,
                               text="For technical support, check the documentation or logs",
                               font=('Arial', 8),
                               bg='white',
                               fg='#6C757D')
        footer_label.pack(pady=(10, 20))
        
        root.mainloop()
        
    except Exception as e:
        print("❌ Critical error: Cannot create emergency interface")
        print(f"Error: {e}")
        print("Please check your Python and tkinter installation")

if __name__ == "__main__":
    create_emergency_interface()
