"""
Anomaly Detection Configuration
Comprehensive settings for automated anomaly detection and recording system
"""

import os
from dataclasses import dataclass
from typing import List, Dict, Tuple, Set
from datetime import datetime, timedelta

@dataclass
class AnomalyConfig:
    """Configuration for anomaly detection and recording system"""
    
    def __init__(self):
        # Directories
        self.base_dir = "anomaly_recordings"
        self.reports_dir = "anomaly_reports"
        self.models_dir = "models"
        self.temp_dir = "temp_recordings"
        
        # YOLO Model Configuration
        self.yolo_weights = "yolov3.weights"
        self.yolo_config = "yolov3.cfg"
        self.yolo_classes = "coco.names"
        self.yolo_input_size = (416, 416)
        self.yolo_confidence_threshold = 0.5
        self.yolo_nms_threshold = 0.4
        
        # Anomaly Detection Settings
        self.anomaly_confidence_threshold = 0.3  # Lower threshold for anomalies
        self.unknown_object_threshold = 0.2      # Very low confidence = unknown
        self.anomaly_sensitivity = 0.7           # Overall sensitivity (0.0-1.0)
        
        # Recording Settings
        self.recording_duration = 10             # seconds
        self.recording_fps = 30
        self.recording_format = 'mp4v'
        self.pre_recording_buffer = 2            # seconds before anomaly
        self.post_recording_buffer = 2           # seconds after anomaly
        
        # File Management
        self.max_recordings = 100                # Maximum number of recordings to keep
        self.cleanup_days = 30                   # Delete files older than 30 days
        self.auto_cleanup_enabled = True
        
        # Performance Settings
        self.max_concurrent_recordings = 3
        self.detection_interval = 3              # Process every 3rd frame for performance
        self.min_fps_threshold = 3.0            # Minimum FPS to maintain
        
        # Report Settings
        self.generate_text_reports = True
        self.generate_json_reports = True
        self.generate_pdf_reports = True
        self.include_screenshots = True
        self.screenshot_quality = 95            # JPEG quality
        
        # Define anomaly categories
        self.setup_anomaly_categories()
        
        # Restricted areas (can be configured per scene)
        self.restricted_areas = []  # List of (x1, y1, x2, y2) rectangles
        
        # UI Settings
        self.show_anomaly_overlay = True
        self.anomaly_box_color = (0, 0, 255)    # Red for anomalies
        self.normal_box_color = (0, 255, 0)     # Green for normal objects
        self.unknown_box_color = (255, 0, 255)  # Magenta for unknown objects
        
    def setup_anomaly_categories(self):
        """Define what constitutes an anomaly"""
        
        # Expected normal objects (from COCO dataset) - using 'human' instead of 'person'
        self.normal_objects = {
            'human', 'bicycle', 'car', 'motorcycle', 'airplane', 'bus', 'train', 'truck',
            'boat', 'traffic light', 'fire hydrant', 'stop sign', 'parking meter', 'bench',
            'bird', 'cat', 'dog', 'horse', 'sheep', 'cow', 'elephant', 'bear', 'zebra',
            'giraffe', 'backpack', 'umbrella', 'handbag', 'tie', 'suitcase', 'frisbee',
            'skis', 'snowboard', 'sports ball', 'kite', 'baseball bat', 'baseball glove',
            'skateboard', 'surfboard', 'tennis racket', 'bottle', 'wine glass', 'cup',
            'fork', 'knife', 'spoon', 'bowl', 'banana', 'apple', 'sandwich', 'orange',
            'broccoli', 'carrot', 'hot dog', 'pizza', 'donut', 'cake', 'chair', 'couch',
            'potted plant', 'bed', 'dining table', 'toilet', 'tv', 'laptop', 'mouse',
            'remote', 'keyboard', 'cell phone', 'microwave', 'oven', 'toaster', 'sink',
            'refrigerator', 'book', 'clock', 'vase', 'scissors', 'teddy bear', 'hair drier',
            'toothbrush'
        }
        
        # Objects that should trigger anomaly alerts
        self.suspicious_objects = {
            'knife', 'scissors', 'baseball bat', 'sports ball'  # Potentially dangerous items
        }
        
        # Objects that are unusual in most indoor settings
        self.unusual_indoor_objects = {
            'airplane', 'boat', 'train', 'truck', 'bus', 'motorcycle', 'horse', 'cow',
            'elephant', 'bear', 'zebra', 'giraffe', 'surfboard', 'skis', 'snowboard'
        }
        
        # Objects that might indicate security concerns
        self.security_concern_objects = {
            'suitcase', 'backpack'  # Unattended bags
        }
        
        # Combine all anomaly categories
        self.anomaly_objects = (
            self.suspicious_objects | 
            self.unusual_indoor_objects | 
            self.security_concern_objects
        )
    
    def get_model_path(self, filename: str) -> str:
        """Get full path to model file"""
        return os.path.join(self.models_dir, filename)
    
    def get_recording_path(self, timestamp: str) -> str:
        """Get path for anomaly recording"""
        filename = f"anomaly_{timestamp}.mp4"
        return os.path.join(self.base_dir, filename)
    
    def get_report_path(self, timestamp: str, format: str) -> str:
        """Get path for anomaly report"""
        filename = f"anomaly_report_{timestamp}.{format}"
        return os.path.join(self.reports_dir, filename)
    
    def get_screenshot_path(self, timestamp: str) -> str:
        """Get path for anomaly screenshot"""
        filename = f"anomaly_screenshot_{timestamp}.jpg"
        return os.path.join(self.reports_dir, filename)
    
    def is_anomaly(self, class_name: str, confidence: float, bbox: Tuple[int, int, int, int] = None) -> Tuple[bool, str]:
        """
        Determine if detected object is an anomaly
        Returns: (is_anomaly, anomaly_type)
        """
        
        # Check for unknown objects (very low confidence)
        if confidence < self.unknown_object_threshold:
            return True, "unknown_object"
        
        # Check for suspicious objects
        if class_name in self.suspicious_objects:
            return True, "suspicious_object"
        
        # Check for unusual indoor objects
        if class_name in self.unusual_indoor_objects:
            return True, "unusual_object"
        
        # Check for security concern objects
        if class_name in self.security_concern_objects:
            return True, "security_concern"
        
        # Check for objects in restricted areas
        if bbox and self.restricted_areas:
            x1, y1, x2, y2 = bbox
            center_x = (x1 + x2) // 2
            center_y = (y1 + y2) // 2
            
            for rx1, ry1, rx2, ry2 in self.restricted_areas:
                if rx1 <= center_x <= rx2 and ry1 <= center_y <= ry2:
                    return True, "restricted_area"
        
        # Check for low confidence on normal objects
        if class_name in self.normal_objects and confidence < self.anomaly_confidence_threshold:
            return True, "low_confidence"
        
        return False, "normal"
    
    def create_directories(self):
        """Create necessary directories"""
        directories = [
            self.base_dir,
            self.reports_dir,
            self.temp_dir
        ]
        
        for directory in directories:
            os.makedirs(directory, exist_ok=True)
    
    def get_cleanup_date(self) -> datetime:
        """Get the cutoff date for file cleanup"""
        return datetime.now() - timedelta(days=self.cleanup_days)
    
    def should_record_anomaly(self, anomaly_type: str) -> bool:
        """Determine if this type of anomaly should trigger recording"""
        # Record all anomalies by default
        return True
    
    def get_anomaly_priority(self, anomaly_type: str) -> int:
        """Get priority level for anomaly type (1=highest, 5=lowest)"""
        priority_map = {
            "suspicious_object": 1,
            "security_concern": 2,
            "restricted_area": 2,
            "unusual_object": 3,
            "unknown_object": 4,
            "low_confidence": 5
        }
        return priority_map.get(anomaly_type, 3)
