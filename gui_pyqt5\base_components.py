"""
PyQt5 Base Components

This module contains PyQt5 equivalents of custom Tkinter components
used throughout the AI Video Detection application.
"""

import sys
from PyQt5.QtWidgets import (
    QWidget, QPushButton, QLabel, QFrame, QVBoxLayout, QHBoxLayout,
    QGridLayout, QScrollArea, QApplication, QSizePolicy
)
from PyQt5.QtCore import Qt, QTimer, pyqtSignal, QRect
from PyQt5.QtGui import QPainter, QPen, QBrush, QColor, QFont, QPalette


class RoundedButton(QPushButton):
    """
    PyQt5 equivalent of the custom Tkinter RoundedButton class.
    Creates a button with rounded corners and hover effects.
    """
    
    def __init__(self, text="", parent=None, bg_color='#3498DB', fg_color='white', 
                 width=120, height=40, corner_radius=10, font_family='Arial', 
                 font_size=10, font_weight='bold'):
        super().__init__(text, parent)
        
        # Store properties
        self.bg_color = QColor(bg_color)
        self.fg_color = QColor(fg_color)
        self.corner_radius = corner_radius
        self.enabled_state = True
        self.disabled_bg = QColor('#BDC3C7')
        self.disabled_fg = QColor('#7F8C8D')
        self.hover_bg = self.lighten_color(self.bg_color)
        
        # Set size
        self.setFixedSize(width, height)
        
        # Set font
        font = QFont(font_family, font_size)
        if font_weight == 'bold':
            font.setBold(True)
        self.setFont(font)
        
        # Set cursor
        self.setCursor(Qt.PointingHandCursor)
        
        # Apply initial styling
        self.update_style()
        
        # Enable hover events
        self.setAttribute(Qt.WA_Hover, True)
    
    def lighten_color(self, color, factor=30):
        """Lighten a QColor by a given factor"""
        r = min(255, color.red() + factor)
        g = min(255, color.green() + factor)
        b = min(255, color.blue() + factor)
        return QColor(r, g, b)
    
    def update_style(self):
        """Update button styling based on current state"""
        if self.enabled_state:
            bg = self.bg_color
            fg = self.fg_color
        else:
            bg = self.disabled_bg
            fg = self.disabled_fg
        
        style = f"""
            QPushButton {{
                background-color: {bg.name()};
                color: {fg.name()};
                border: none;
                border-radius: {self.corner_radius}px;
                padding: 8px 15px;
            }}
            QPushButton:hover {{
                background-color: {self.hover_bg.name()};
            }}
            QPushButton:pressed {{
                background-color: {self.lighten_color(self.bg_color, -20).name()};
            }}
            QPushButton:disabled {{
                background-color: {self.disabled_bg.name()};
                color: {self.disabled_fg.name()};
            }}
        """
        self.setStyleSheet(style)
    
    def setEnabled(self, enabled):
        """Override setEnabled to update styling"""
        super().setEnabled(enabled)
        self.enabled_state = enabled
        self.update_style()
    
    def config(self, **kwargs):
        """Configuration method similar to Tkinter"""
        if 'text' in kwargs:
            self.setText(kwargs['text'])
        if 'bg' in kwargs:
            self.bg_color = QColor(kwargs['bg'])
            self.hover_bg = self.lighten_color(self.bg_color)
            self.update_style()
        if 'state' in kwargs:
            enabled = kwargs['state'] != 'disabled'
            self.setEnabled(enabled)


class ColoredButton(QPushButton):
    """
    PyQt5 equivalent of the ColoredButton class from dashboard.
    Provides styled buttons with hover effects and color management.
    """
    
    def __init__(self, text="", parent=None, bg_color='#3498DB', fg_color='white',
                 width=15, height=1, font_family='Arial', font_size=10, font_weight='bold'):
        super().__init__(text, parent)
        
        # Store colors
        self.normal_bg = QColor(bg_color)
        self.fg_color = QColor(fg_color)
        self.hover_bg = self.lighten_color(self.normal_bg)
        
        # Set font
        font = QFont(font_family, font_size)
        if font_weight == 'bold':
            font.setBold(True)
        self.setFont(font)
        
        # Set size (convert from character-based to pixels)
        char_width = 8  # Approximate character width
        char_height = 20  # Approximate character height
        self.setFixedSize(width * char_width, height * char_height + 16)
        
        # Set cursor
        self.setCursor(Qt.PointingHandCursor)
        
        # Apply styling
        self.update_style()
        
        # Enable hover events
        self.setAttribute(Qt.WA_Hover, True)
    
    def lighten_color(self, color, factor=30):
        """Lighten a QColor by a given factor"""
        r = min(255, color.red() + factor)
        g = min(255, color.green() + factor)
        b = min(255, color.blue() + factor)
        return QColor(r, g, b)
    
    def update_style(self):
        """Update button styling"""
        style = f"""
            QPushButton {{
                background-color: {self.normal_bg.name()};
                color: {self.fg_color.name()};
                border: none;
                border-radius: 4px;
                padding: 8px 12px;
                font-weight: bold;
            }}
            QPushButton:hover {{
                background-color: {self.hover_bg.name()};
            }}
            QPushButton:pressed {{
                background-color: {self.lighten_color(self.normal_bg, -20).name()};
            }}
        """
        self.setStyleSheet(style)


class StyledFrame(QFrame):
    """
    PyQt5 equivalent for styled frames used throughout the application.
    Provides consistent styling and layout management.
    """
    
    def __init__(self, parent=None, bg_color='white', border_color=None, 
                 border_width=1, border_radius=0):
        super().__init__(parent)
        
        # Apply styling
        style = f"background-color: {bg_color};"
        
        if border_color:
            style += f"border: {border_width}px solid {border_color};"
        
        if border_radius > 0:
            style += f"border-radius: {border_radius}px;"
        
        self.setStyleSheet(style)


class StyledLabel(QLabel):
    """
    PyQt5 equivalent for styled labels with consistent formatting.
    """
    
    def __init__(self, text="", parent=None, font_family='Arial', font_size=12,
                 font_weight='normal', color='#2C3E50', bg_color=None):
        super().__init__(text, parent)
        
        # Set font
        font = QFont(font_family, font_size)
        if font_weight == 'bold':
            font.setBold(True)
        self.setFont(font)
        
        # Apply styling
        style = f"color: {color};"
        if bg_color:
            style += f"background-color: {bg_color};"
        
        self.setStyleSheet(style)


# Utility functions for layout management
def create_hbox_layout(*widgets, spacing=5, margins=(0, 0, 0, 0)):
    """Create a horizontal box layout with widgets"""
    layout = QHBoxLayout()
    layout.setSpacing(spacing)
    layout.setContentsMargins(*margins)
    
    for widget in widgets:
        if widget is None:
            layout.addStretch()
        else:
            layout.addWidget(widget)
    
    return layout


def create_vbox_layout(*widgets, spacing=5, margins=(0, 0, 0, 0)):
    """Create a vertical box layout with widgets"""
    layout = QVBoxLayout()
    layout.setSpacing(spacing)
    layout.setContentsMargins(*margins)
    
    for widget in widgets:
        if widget is None:
            layout.addStretch()
        else:
            layout.addWidget(widget)
    
    return layout


def create_grid_layout(widget_grid, spacing=5, margins=(0, 0, 0, 0)):
    """Create a grid layout from a 2D list of widgets"""
    layout = QGridLayout()
    layout.setSpacing(spacing)
    layout.setContentsMargins(*margins)
    
    for row, row_widgets in enumerate(widget_grid):
        for col, widget in enumerate(row_widgets):
            if widget is not None:
                layout.addWidget(widget, row, col)
    
    return layout


# Color constants matching the original Tkinter theme
class Colors:
    PRIMARY = '#3498DB'
    SUCCESS = '#27AE60'
    WARNING = '#F39C12'
    DANGER = '#E74C3C'
    INFO = '#5DADE2'
    LIGHT = '#ECF0F1'
    DARK = '#2C3E50'
    SECONDARY = '#95A5A6'
    BACKGROUND = '#E8F4FD'
    WHITE = '#FFFFFF'
    BLACK = '#000000'
    GRAY_LIGHT = '#BDC3C7'
    GRAY_DARK = '#7F8C8D'
