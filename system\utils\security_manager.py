"""
Security Configuration and Management Module
"""

import hashlib
import secrets
import json
import os
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple

class SecurityManager:
    """Centralized security management for the application"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.security_file = "security_data.json"
        self.config_file = "security_config.json"
        
        # Default security settings
        self.default_settings = {
            "password_policy": {
                "min_length": 8,
                "max_length": 128,
                "require_uppercase": True,
                "require_lowercase": True,
                "require_numbers": True,
                "require_special_chars": True,
                "special_chars": "!@#$%^&*()_+-=[]{}|;:,.<>?",
                "password_history_count": 5,
                "password_expiry_days": 90
            },
            "account_lockout": {
                "max_attempts": 3,
                "lockout_duration_minutes": 5,
                "progressive_lockout": True,
                "progressive_multiplier": 2
            },
            "session_management": {
                "session_timeout_minutes": 30,
                "idle_timeout_minutes": 15,
                "max_concurrent_sessions": 1,
                "require_reauth_for_sensitive": True
            },
            "audit_logging": {
                "log_login_attempts": True,
                "log_password_changes": True,
                "log_security_events": True,
                "retention_days": 90
            },
            "two_factor_auth": {
                "enabled": False,
                "methods": ["totp", "sms", "email"],
                "backup_codes_count": 10
            }
        }
        
        self.settings = self.load_security_config()
        self.user_data = self.load_user_data()
    
    def load_security_config(self) -> Dict:
        """Load security configuration"""
        try:
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r') as f:
                    config = json.load(f)
                    # Merge with defaults
                    return self._merge_configs(self.default_settings, config)
            else:
                self.save_security_config(self.default_settings)
                return self.default_settings.copy()
        except Exception as e:
            self.logger.error(f"Error loading security config: {e}")
            return self.default_settings.copy()
    
    def save_security_config(self, config: Dict):
        """Save security configuration"""
        try:
            with open(self.config_file, 'w') as f:
                json.dump(config, f, indent=2)
        except Exception as e:
            self.logger.error(f"Error saving security config: {e}")
    
    def load_user_data(self) -> Dict:
        """Load user security data"""
        try:
            if os.path.exists(self.security_file):
                with open(self.security_file, 'r') as f:
                    return json.load(f)
            else:
                return {
                    'users': {},
                    'sessions': {},
                    'audit_log': []
                }
        except Exception as e:
            self.logger.error(f"Error loading user data: {e}")
            return {'users': {}, 'sessions': {}, 'audit_log': []}
    
    def save_user_data(self):
        """Save user security data"""
        try:
            with open(self.security_file, 'w') as f:
                json.dump(self.user_data, f, indent=2)
        except Exception as e:
            self.logger.error(f"Error saving user data: {e}")
    
    def _merge_configs(self, default: Dict, custom: Dict) -> Dict:
        """Merge custom config with defaults"""
        result = default.copy()
        for key, value in custom.items():
            if isinstance(value, dict) and key in result:
                result[key] = self._merge_configs(result[key], value)
            else:
                result[key] = value
        return result
    
    def create_user(self, username: str, password: str, role: str = "user") -> Tuple[bool, str]:
        """Create a new user with secure password storage"""
        try:
            # Check if user already exists
            if username in self.user_data['users']:
                return False, "User already exists"
            
            # Validate password
            is_valid, message = self.validate_password(password)
            if not is_valid:
                return False, message
            
            # Generate salt and hash password
            salt = secrets.token_hex(32)
            password_hash = self._hash_password(password, salt)
            
            # Create user record
            self.user_data['users'][username] = {
                'password_hash': password_hash,
                'salt': salt,
                'role': role,
                'created_at': datetime.now().isoformat(),
                'last_login': None,
                'failed_attempts': 0,
                'locked_until': None,
                'password_history': [password_hash],
                'password_changed_at': datetime.now().isoformat(),
                'two_factor_enabled': False,
                'security_questions': []
            }
            
            self.save_user_data()
            self.log_security_event('user_created', username, {'role': role})
            return True, "User created successfully"
            
        except Exception as e:
            self.logger.error(f"Error creating user: {e}")
            return False, f"Error creating user: {str(e)}"
    
    def authenticate_user(self, username: str, password: str) -> Tuple[bool, str, Dict]:
        """Authenticate user with comprehensive security checks"""
        try:
            # Check if user exists
            if username not in self.user_data['users']:
                self.log_security_event('login_attempt_invalid_user', username)
                return False, "Invalid credentials", {}
            
            user = self.user_data['users'][username]
            
            # Check if account is locked
            if self._is_account_locked(username):
                remaining_time = self._get_lockout_remaining_time(username)
                self.log_security_event('login_attempt_locked_account', username)
                return False, f"Account locked. Try again in {remaining_time} minutes.", {}
            
            # Verify password
            if self._verify_password(password, user['password_hash'], user['salt']):
                # Reset failed attempts and update last login
                user['failed_attempts'] = 0
                user['locked_until'] = None
                user['last_login'] = datetime.now().isoformat()
                
                self.save_user_data()
                self.log_security_event('login_success', username)
                
                # Return user info (excluding sensitive data)
                user_info = {
                    'username': username,
                    'role': user['role'],
                    'last_login': user['last_login'],
                    'two_factor_enabled': user['two_factor_enabled']
                }
                
                return True, "Authentication successful", user_info
            else:
                # Record failed attempt
                user['failed_attempts'] += 1
                
                # Check if account should be locked
                max_attempts = self.settings['account_lockout']['max_attempts']
                if user['failed_attempts'] >= max_attempts:
                    self._lock_account(username)
                    self.log_security_event('account_locked', username, 
                                          {'attempts': user['failed_attempts']})
                    return False, f"Account locked after {max_attempts} failed attempts", {}
                
                self.save_user_data()
                self.log_security_event('login_failed', username, 
                                      {'attempts': user['failed_attempts']})
                
                remaining = max_attempts - user['failed_attempts']
                return False, f"Invalid credentials. {remaining} attempts remaining.", {}
                
        except Exception as e:
            self.logger.error(f"Error during authentication: {e}")
            return False, "Authentication error", {}
    
    def validate_password(self, password: str, username: str = None) -> Tuple[bool, str]:
        """Validate password against security policy"""
        policy = self.settings['password_policy']
        errors = []
        
        # Length check
        if len(password) < policy['min_length']:
            errors.append(f"Password must be at least {policy['min_length']} characters")
        
        if len(password) > policy['max_length']:
            errors.append(f"Password must not exceed {policy['max_length']} characters")
        
        # Character requirements
        if policy['require_uppercase'] and not any(c.isupper() for c in password):
            errors.append("Password must contain at least one uppercase letter")
        
        if policy['require_lowercase'] and not any(c.islower() for c in password):
            errors.append("Password must contain at least one lowercase letter")
        
        if policy['require_numbers'] and not any(c.isdigit() for c in password):
            errors.append("Password must contain at least one number")
        
        if policy['require_special_chars']:
            special_chars = policy['special_chars']
            if not any(c in special_chars for c in password):
                errors.append(f"Password must contain at least one special character: {special_chars}")
        
        # Check password history if username provided
        if username and username in self.user_data['users']:
            user = self.user_data['users'][username]
            password_hash = self._hash_password(password, user['salt'])
            history_count = policy['password_history_count']
            
            if password_hash in user.get('password_history', [])[-history_count:]:
                errors.append(f"Password cannot be one of your last {history_count} passwords")
        
        # Common password check (basic implementation)
        common_passwords = ['password', '123456', 'password123', 'admin', 'qwerty']
        if password.lower() in common_passwords:
            errors.append("Password is too common")
        
        if errors:
            return False, "; ".join(errors)
        
        return True, "Password is valid"
    
    def change_password(self, username: str, old_password: str, new_password: str) -> Tuple[bool, str]:
        """Change user password with validation"""
        try:
            if username not in self.user_data['users']:
                return False, "User not found"
            
            user = self.user_data['users'][username]
            
            # Verify old password
            if not self._verify_password(old_password, user['password_hash'], user['salt']):
                self.log_security_event('password_change_failed', username, {'reason': 'invalid_old_password'})
                return False, "Current password is incorrect"
            
            # Validate new password
            is_valid, message = self.validate_password(new_password, username)
            if not is_valid:
                return False, message
            
            # Update password
            new_hash = self._hash_password(new_password, user['salt'])
            
            # Update password history
            history = user.get('password_history', [])
            history.append(new_hash)
            history_count = self.settings['password_policy']['password_history_count']
            user['password_history'] = history[-history_count:]
            
            # Update user record
            user['password_hash'] = new_hash
            user['password_changed_at'] = datetime.now().isoformat()
            
            self.save_user_data()
            self.log_security_event('password_changed', username)
            
            return True, "Password changed successfully"
            
        except Exception as e:
            self.logger.error(f"Error changing password: {e}")
            return False, f"Error changing password: {str(e)}"
    
    def _hash_password(self, password: str, salt: str) -> str:
        """Hash password with salt using PBKDF2"""
        return hashlib.pbkdf2_hmac(
            'sha256',
            password.encode('utf-8'),
            salt.encode('utf-8'),
            100000  # 100,000 iterations
        ).hex()
    
    def _verify_password(self, password: str, stored_hash: str, salt: str) -> bool:
        """Verify password against stored hash"""
        return self._hash_password(password, salt) == stored_hash
    
    def _is_account_locked(self, username: str) -> bool:
        """Check if account is currently locked"""
        if username not in self.user_data['users']:
            return False
        
        user = self.user_data['users'][username]
        locked_until = user.get('locked_until')
        
        if locked_until:
            lock_time = datetime.fromisoformat(locked_until)
            if datetime.now() < lock_time:
                return True
            else:
                # Unlock account
                user['locked_until'] = None
                user['failed_attempts'] = 0
                self.save_user_data()
        
        return False
    
    def _lock_account(self, username: str):
        """Lock account due to failed attempts"""
        if username not in self.user_data['users']:
            return
        
        user = self.user_data['users'][username]
        lockout_settings = self.settings['account_lockout']
        
        # Calculate lockout duration
        base_duration = lockout_settings['lockout_duration_minutes']
        
        if lockout_settings['progressive_lockout']:
            # Progressive lockout - increase duration for repeat offenses
            multiplier = lockout_settings['progressive_multiplier']
            lockout_count = user.get('lockout_count', 0) + 1
            duration = base_duration * (multiplier ** (lockout_count - 1))
            user['lockout_count'] = lockout_count
        else:
            duration = base_duration
        
        # Set lockout time
        lockout_until = datetime.now() + timedelta(minutes=duration)
        user['locked_until'] = lockout_until.isoformat()
        user['failed_attempts'] = 0
        
        self.save_user_data()
    
    def _get_lockout_remaining_time(self, username: str) -> int:
        """Get remaining lockout time in minutes"""
        if username not in self.user_data['users']:
            return 0
        
        user = self.user_data['users'][username]
        locked_until = user.get('locked_until')
        
        if locked_until:
            lock_time = datetime.fromisoformat(locked_until)
            remaining = lock_time - datetime.now()
            return max(0, int(remaining.total_seconds() // 60))
        
        return 0
    
    def log_security_event(self, event_type: str, username: str = None, details: Dict = None):
        """Log security events for audit trail"""
        if not self.settings['audit_logging']['log_security_events']:
            return
        
        event = {
            'timestamp': datetime.now().isoformat(),
            'event_type': event_type,
            'username': username,
            'details': details or {},
            'ip_address': 'localhost',  # Could be enhanced to get real IP
            'user_agent': 'AI Video Detection System'
        }
        
        self.user_data['audit_log'].append(event)
        
        # Cleanup old logs
        retention_days = self.settings['audit_logging']['retention_days']
        cutoff_date = datetime.now() - timedelta(days=retention_days)
        
        self.user_data['audit_log'] = [
            log for log in self.user_data['audit_log']
            if datetime.fromisoformat(log['timestamp']) > cutoff_date
        ]
        
        self.save_user_data()
        self.logger.info(f"Security event logged: {event_type} for user {username}")
    
    def get_security_stats(self) -> Dict:
        """Get security statistics"""
        stats = {
            'total_users': len(self.user_data['users']),
            'locked_accounts': sum(1 for user in self.user_data['users'].values() 
                                 if user.get('locked_until')),
            'recent_login_attempts': 0,
            'recent_failed_attempts': 0,
            'password_expiry_warnings': 0
        }
        
        # Count recent events (last 24 hours)
        cutoff = datetime.now() - timedelta(hours=24)
        recent_events = [
            event for event in self.user_data['audit_log']
            if datetime.fromisoformat(event['timestamp']) > cutoff
        ]
        
        for event in recent_events:
            if event['event_type'] in ['login_success', 'login_failed']:
                stats['recent_login_attempts'] += 1
            if event['event_type'] == 'login_failed':
                stats['recent_failed_attempts'] += 1
        
        # Check password expiry
        expiry_days = self.settings['password_policy']['password_expiry_days']
        warning_days = 7  # Warn 7 days before expiry
        warning_cutoff = datetime.now() - timedelta(days=expiry_days - warning_days)
        
        for user in self.user_data['users'].values():
            password_changed = user.get('password_changed_at')
            if password_changed:
                changed_date = datetime.fromisoformat(password_changed)
                if changed_date < warning_cutoff:
                    stats['password_expiry_warnings'] += 1
        
        return stats
    
    def cleanup_expired_sessions(self):
        """Clean up expired sessions"""
        current_time = datetime.now()
        timeout_minutes = self.settings['session_management']['session_timeout_minutes']
        
        expired_sessions = []
        for session_id, session in self.user_data.get('sessions', {}).items():
            last_activity = datetime.fromisoformat(session['last_activity'])
            if current_time - last_activity > timedelta(minutes=timeout_minutes):
                expired_sessions.append(session_id)
        
        for session_id in expired_sessions:
            del self.user_data['sessions'][session_id]
            self.log_security_event('session_expired', session_id)
        
        if expired_sessions:
            self.save_user_data()
    
    def generate_security_report(self) -> str:
        """Generate a security status report"""
        stats = self.get_security_stats()
        
        report = f"""
=== SECURITY STATUS REPORT ===
Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

USER ACCOUNTS:
- Total Users: {stats['total_users']}
- Locked Accounts: {stats['locked_accounts']}

RECENT ACTIVITY (24 hours):
- Login Attempts: {stats['recent_login_attempts']}
- Failed Attempts: {stats['recent_failed_attempts']}
- Success Rate: {((stats['recent_login_attempts'] - stats['recent_failed_attempts']) / max(1, stats['recent_login_attempts']) * 100):.1f}%

PASSWORD SECURITY:
- Passwords Expiring Soon: {stats['password_expiry_warnings']}

SECURITY SETTINGS:
- Max Login Attempts: {self.settings['account_lockout']['max_attempts']}
- Lockout Duration: {self.settings['account_lockout']['lockout_duration_minutes']} minutes
- Session Timeout: {self.settings['session_management']['session_timeout_minutes']} minutes
- Password Min Length: {self.settings['password_policy']['min_length']}

AUDIT LOG:
- Total Events: {len(self.user_data['audit_log'])}
- Retention: {self.settings['audit_logging']['retention_days']} days
"""
        return report


# Example usage and initialization
def initialize_security_system():
    """Initialize the security system with default admin user"""
    security_manager = SecurityManager()
    
    # Create default admin user if it doesn't exist
    if 'admin' not in security_manager.user_data['users']:
        success, message = security_manager.create_user('admin', 'SecurePass123!', 'admin')
        if success:
            print("✅ Default admin user created successfully")
            print("Username: admin")
            print("Password: SecurePass123!")
            print("⚠️  Please change the default password after first login")
        else:
            print(f"❌ Failed to create admin user: {message}")
    
    return security_manager


if __name__ == "__main__":
    # Initialize and test the security system
    security_manager = initialize_security_system()
    
    # Generate security report
    print(security_manager.generate_security_report())