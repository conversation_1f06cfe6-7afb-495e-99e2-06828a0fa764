"""
Database Error Handler for AI Video Detection System
Provides graceful error handling for database operations that won't stop detection processes
"""

import sqlite3
import time
from datetime import datetime
from typing import Optional, Callable, Any
import threading
import os

class DatabaseErrorHandler:
    """Handles database connection errors gracefully without stopping detection processes"""
    
    def __init__(self):
        self.connection_errors = 0
        self.last_error_time = 0
        self.error_cooldown = 30  # Log errors at most once per 30 seconds
        self.max_retries = 3
        self.retry_delay = 1.0  # seconds
        self.lock = threading.Lock()
        
    def safe_database_operation(self, operation: Callable, operation_name: str, *args, **kwargs) -> bool:
        """
        Safely execute a database operation with error handling and retries
        
        Args:
            operation: The database operation function to execute
            operation_name: Name of the operation for logging
            *args, **kwargs: Arguments to pass to the operation
            
        Returns:
            bool: True if operation succeeded, False if failed
        """
        for attempt in range(self.max_retries):
            try:
                # Execute the database operation
                result = operation(*args, **kwargs)
                
                # Reset error counter on success
                if self.connection_errors > 0:
                    with self.lock:
                        self.connection_errors = 0
                    print(f"✅ Database connection restored for {operation_name}")
                
                return result if result is not None else True
                
            except sqlite3.OperationalError as e:
                self._handle_database_error(e, operation_name, attempt + 1)
                if attempt < self.max_retries - 1:
                    time.sleep(self.retry_delay * (attempt + 1))  # Exponential backoff
                    
            except sqlite3.DatabaseError as e:
                self._handle_database_error(e, operation_name, attempt + 1)
                if attempt < self.max_retries - 1:
                    time.sleep(self.retry_delay)
                    
            except Exception as e:
                self._handle_unexpected_error(e, operation_name)
                break  # Don't retry on unexpected errors
        
        return False
    
    def _handle_database_error(self, error: Exception, operation_name: str, attempt: int):
        """Handle database-specific errors"""
        with self.lock:
            self.connection_errors += 1
            current_time = time.time()
            
            # Only log errors if enough time has passed (avoid spam)
            if current_time - self.last_error_time > self.error_cooldown:
                self.last_error_time = current_time
                
                error_msg = str(error)
                if "database is locked" in error_msg.lower():
                    print(f"⚠️ Database locked during {operation_name} (attempt {attempt}/{self.max_retries})")
                    print("   Detection will continue, database logging temporarily unavailable")
                elif "no such table" in error_msg.lower():
                    print(f"⚠️ Database table missing for {operation_name}")
                    print("   Please run database migration to fix schema issues")
                elif "no such column" in error_msg.lower():
                    print(f"⚠️ Database column missing for {operation_name}")
                    print("   Please run database migration to update schema")
                else:
                    print(f"⚠️ Database error during {operation_name}: {error}")
                    print("   Detection will continue, database logging temporarily unavailable")
                
                # Log to file if possible
                self._log_error_to_file(error, operation_name, attempt)
    
    def _handle_unexpected_error(self, error: Exception, operation_name: str):
        """Handle unexpected errors"""
        with self.lock:
            current_time = time.time()
            
            if current_time - self.last_error_time > self.error_cooldown:
                self.last_error_time = current_time
                print(f"❌ Unexpected error during {operation_name}: {error}")
                print("   Detection will continue, but this error should be investigated")
                
                # Log to file if possible
                self._log_error_to_file(error, operation_name, 1)
    
    def _log_error_to_file(self, error: Exception, operation_name: str, attempt: int):
        """Log error to file for debugging"""
        try:
            os.makedirs("logs", exist_ok=True)
            log_file = "logs/database_errors.log"
            
            timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            log_entry = f"[{timestamp}] {operation_name} (attempt {attempt}): {type(error).__name__}: {error}\n"
            
            with open(log_file, "a", encoding="utf-8") as f:
                f.write(log_entry)
                
        except Exception:
            # If we can't log to file, just continue silently
            pass
    
    def get_error_stats(self) -> dict:
        """Get error statistics"""
        with self.lock:
            return {
                'total_connection_errors': self.connection_errors,
                'last_error_time': self.last_error_time,
                'error_cooldown': self.error_cooldown,
                'max_retries': self.max_retries
            }
    
    def reset_error_counter(self):
        """Reset error counter (useful for testing)"""
        with self.lock:
            self.connection_errors = 0
            self.last_error_time = 0

# Global error handler instance
_error_handler = None

def get_error_handler() -> DatabaseErrorHandler:
    """Get global error handler instance"""
    global _error_handler
    if _error_handler is None:
        _error_handler = DatabaseErrorHandler()
    return _error_handler

def safe_log_detection(detection_type: str, log_function: Callable, **kwargs) -> bool:
    """
    Safely log a detection with error handling
    
    Args:
        detection_type: Type of detection (age, object, expression, anomaly)
        log_function: The database logging function to call
        **kwargs: Arguments to pass to the logging function
        
    Returns:
        bool: True if logging succeeded, False if failed
    """
    error_handler = get_error_handler()
    operation_name = f"{detection_type}_detection_logging"
    
    return error_handler.safe_database_operation(
        log_function,
        operation_name,
        **kwargs
    )

def safe_database_query(query_function: Callable, operation_name: str, *args, **kwargs) -> Any:
    """
    Safely execute a database query with error handling
    
    Args:
        query_function: The database query function to call
        operation_name: Name of the operation for logging
        *args, **kwargs: Arguments to pass to the query function
        
    Returns:
        Query result or None if failed
    """
    error_handler = get_error_handler()
    
    def wrapper():
        return query_function(*args, **kwargs)
    
    try:
        return error_handler.safe_database_operation(wrapper, operation_name)
    except Exception:
        return None

# Decorator for database operations
def database_operation(operation_name: str):
    """Decorator to add error handling to database operations"""
    def decorator(func):
        def wrapper(*args, **kwargs):
            error_handler = get_error_handler()
            return error_handler.safe_database_operation(func, operation_name, *args, **kwargs)
        return wrapper
    return decorator
