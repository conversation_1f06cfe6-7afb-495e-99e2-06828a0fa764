"""
PyQt5 GUI Components for AI Video Detection Tool

This module contains PyQt5 implementations of all GUI components,
migrated from the original Tkinter implementation while preserving
all functionality and user experience.
"""

__version__ = "1.0.0"
__author__ = "AI Video Detection Team"

# Import main components for easy access
from .base_components import RoundedButton, ColoredButton
from .login_window import LoginWindow
from .main_window import MainWindow

__all__ = [
    'RoundedButton',
    'ColoredButton', 
    'LoginWindow',
    'MainWindow'
]
