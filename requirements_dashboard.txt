# Enhanced AI Detection Dashboard Requirements
# Install with: pip install -r requirements_dashboard.txt

# Core dependencies (already included in main project)
numpy>=1.21.0
opencv-python>=4.5.0
Pillow>=8.0.0

# Dashboard-specific dependencies
reportlab>=3.6.0          # Professional PDF generation
matplotlib>=3.5.0         # Charts and graphs for analytics

# Optional but recommended
pandas>=1.3.0             # Data analysis and manipulation
seaborn>=0.11.0           # Enhanced statistical visualizations

# Database (already included)
# sqlite3 is built into Python

# GUI (already included)
# tkinter is built into Python
