"""
Advanced Anomaly Detection Engine
Integrates with YOLO object detection for automated anomaly detection and recording
"""

import cv2
import numpy as np
import time
import threading
from typing import List, Dict, Tuple, Optional
from datetime import datetime
import logging
from collections import deque

from utils.anomaly_config import AnomalyConfig

class AnomalyDetector:
    """Advanced anomaly detection using YOLO object detection"""
    
    def __init__(self):
        self.config = AnomalyConfig()
        self.logger = logging.getLogger(__name__)
        
        # YOLO model components
        self.net = None
        self.output_layers = []
        self.class_names = []
        
        # Detection state
        self.is_initialized = False
        self.last_detection_time = 0
        self.detection_history = deque(maxlen=10)  # Keep last 10 detections
        
        # Performance tracking
        self.fps_counter = 0
        self.fps_start_time = time.time()
        self.current_fps = 0
        
        # Thread safety
        self.detection_lock = threading.Lock()
        
        # Initialize the detector
        self.initialize()
    
    def initialize(self):
        """Initialize YOLO model and class names"""
        try:
            print("🔍 Initializing Anomaly Detection System...")
            
            # Create directories
            self.config.create_directories()
            
            # Load YOLO model
            self._load_yolo_model()
            
            # Load class names
            self._load_class_names()
            
            self.is_initialized = True
            print("✅ Anomaly Detection System initialized successfully!")
            print(f"📊 Monitoring for {len(self.config.anomaly_objects)} types of anomalies")
            print(f"🎯 Confidence threshold: {self.config.anomaly_confidence_threshold}")
            print(f"📹 Recording duration: {self.config.recording_duration} seconds")
            
        except Exception as e:
            self.logger.error(f"Failed to initialize anomaly detector: {e}")
            print(f"❌ Anomaly detection initialization failed: {e}")
            self.is_initialized = False
    
    def _load_yolo_model(self):
        """Load YOLO model from weights and config files"""
        try:
            weights_path = self.config.get_model_path(self.config.yolo_weights)
            config_path = self.config.get_model_path(self.config.yolo_config)
            
            print(f"📁 Loading YOLO weights: {weights_path}")
            print(f"📁 Loading YOLO config: {config_path}")
            
            # Load the network
            self.net = cv2.dnn.readNet(weights_path, config_path)
            
            # Get output layer names
            layer_names = self.net.getLayerNames()
            unconnected_layers = self.net.getUnconnectedOutLayers()
            
            # Handle different OpenCV versions
            if len(unconnected_layers.shape) == 1:
                self.output_layers = [layer_names[i - 1] for i in unconnected_layers]
            else:
                self.output_layers = [layer_names[i[0] - 1] for i in unconnected_layers]
            
            print(f"✅ YOLO model loaded with {len(self.output_layers)} output layers")
            
        except Exception as e:
            raise Exception(f"Failed to load YOLO model: {e}")
    
    def _load_class_names(self):
        """Load COCO class names and replace 'person' with 'human'"""
        try:
            classes_path = self.config.get_model_path(self.config.yolo_classes)
            print(f"📁 Loading class names: {classes_path}")

            with open(classes_path, 'r') as f:
                self.class_names = [line.strip() for line in f.readlines()]

            # Replace 'person' with 'human' in class names
            for i, class_name in enumerate(self.class_names):
                if class_name.lower() == 'person':
                    self.class_names[i] = 'human'
                    print(f"✅ Changed 'person' to 'human' in class {i}")

            print(f"✅ Loaded {len(self.class_names)} object classes with human detection")

        except Exception as e:
            raise Exception(f"Failed to load class names: {e}")
    
    def detect_objects(self, frame: np.ndarray) -> Tuple[List[Dict], bool]:
        """
        Detect objects in frame and identify anomalies
        Returns: (detections, anomaly_detected)
        """
        if not self.is_initialized or self.net is None:
            return [], False
        
        with self.detection_lock:
            try:
                start_time = time.time()
                
                # Prepare frame for YOLO
                height, width = frame.shape[:2]
                blob = cv2.dnn.blobFromImage(
                    frame, 
                    1/255.0, 
                    self.config.yolo_input_size, 
                    (0, 0, 0), 
                    True, 
                    crop=False
                )
                
                # Run detection
                self.net.setInput(blob)
                outputs = self.net.forward(self.output_layers)
                
                # Parse detections
                detections = self._parse_detections(outputs, width, height)
                
                # Apply non-maximum suppression
                filtered_detections = self._apply_nms(detections)
                
                # Check for anomalies
                anomaly_detected = self._check_for_anomalies(filtered_detections)
                
                # Update performance metrics
                self._update_fps()
                
                # Store detection in history
                detection_info = {
                    'timestamp': datetime.now(),
                    'detections': filtered_detections,
                    'anomaly_detected': anomaly_detected,
                    'processing_time': time.time() - start_time
                }
                self.detection_history.append(detection_info)
                
                return filtered_detections, anomaly_detected
                
            except Exception as e:
                self.logger.error(f"Error in object detection: {e}")
                return [], False
    
    def _parse_detections(self, outputs: List, width: int, height: int) -> List[Dict]:
        """Parse YOLO output into detection objects"""
        detections = []
        
        for output in outputs:
            for detection in output:
                scores = detection[5:]
                class_id = np.argmax(scores)
                confidence = scores[class_id]
                
                if confidence > self.config.unknown_object_threshold:
                    # Get bounding box coordinates
                    center_x = int(detection[0] * width)
                    center_y = int(detection[1] * height)
                    w = int(detection[2] * width)
                    h = int(detection[3] * height)
                    
                    # Calculate corner coordinates
                    x1 = int(center_x - w / 2)
                    y1 = int(center_y - h / 2)
                    x2 = int(center_x + w / 2)
                    y2 = int(center_y + h / 2)
                    
                    # Get class name
                    class_name = self.class_names[class_id] if class_id < len(self.class_names) else "unknown"
                    
                    detection_obj = {
                        'class_id': class_id,
                        'class_name': class_name,
                        'confidence': float(confidence),
                        'bbox': (x1, y1, x2, y2),
                        'center': (center_x, center_y),
                        'size': (w, h)
                    }
                    
                    detections.append(detection_obj)
        
        return detections
    
    def _apply_nms(self, detections: List[Dict]) -> List[Dict]:
        """Apply non-maximum suppression to remove duplicate detections"""
        if not detections:
            return []
        
        # Prepare data for NMS
        boxes = []
        confidences = []
        class_ids = []
        
        for detection in detections:
            x1, y1, x2, y2 = detection['bbox']
            boxes.append([x1, y1, x2 - x1, y2 - y1])
            confidences.append(detection['confidence'])
            class_ids.append(detection['class_id'])
        
        # Apply NMS
        indices = cv2.dnn.NMSBoxes(
            boxes, 
            confidences, 
            self.config.unknown_object_threshold,
            self.config.yolo_nms_threshold
        )
        
        # Filter detections based on NMS results
        filtered_detections = []
        if len(indices) > 0:
            for i in indices.flatten():
                filtered_detections.append(detections[i])
        
        return filtered_detections
    
    def _check_for_anomalies(self, detections: List[Dict]) -> bool:
        """Check if any detections are anomalies"""
        anomaly_detected = False
        
        for detection in detections:
            is_anomaly, anomaly_type = self.config.is_anomaly(
                detection['class_name'],
                detection['confidence'],
                detection['bbox']
            )
            
            if is_anomaly:
                detection['is_anomaly'] = True
                detection['anomaly_type'] = anomaly_type
                anomaly_detected = True
                
                print(f"🚨 Anomaly detected: {detection['class_name']} ({anomaly_type}) - Confidence: {detection['confidence']:.2f}")
            else:
                detection['is_anomaly'] = False
                detection['anomaly_type'] = 'normal'
        
        return anomaly_detected
    
    def _update_fps(self):
        """Update FPS counter"""
        self.fps_counter += 1
        current_time = time.time()
        
        if current_time - self.fps_start_time >= 1.0:
            self.current_fps = self.fps_counter / (current_time - self.fps_start_time)
            self.fps_counter = 0
            self.fps_start_time = current_time
    
    def get_fps(self) -> float:
        """Get current detection FPS"""
        return self.current_fps
    
    def get_detection_history(self) -> List[Dict]:
        """Get recent detection history"""
        return list(self.detection_history)
    
    def is_ready(self) -> bool:
        """Check if detector is ready for use"""
        return self.is_initialized and self.net is not None
    
    def get_status(self) -> Dict:
        """Get detector status information"""
        return {
            'initialized': self.is_initialized,
            'fps': self.current_fps,
            'total_classes': len(self.class_names),
            'anomaly_types': len(self.config.anomaly_objects),
            'detection_history_size': len(self.detection_history),
            'confidence_threshold': self.config.anomaly_confidence_threshold
        }
