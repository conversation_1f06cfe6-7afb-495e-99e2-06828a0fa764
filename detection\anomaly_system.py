"""
Integrated Anomaly Detection and Recording System
Main system that coordinates detection, recording, and reporting
"""

import cv2
import numpy as np
import threading
import time
import os
from datetime import datetime
from typing import Dict, List, Optional, Tuple
import logging

from detection.anomaly_detector import AnomalyDetector
from recording.anomaly_recorder import AnomalyRecorder
from reporting.anomaly_reporter import AnomalyReporter
from utils.anomaly_config import AnomalyConfig

class AnomalyDetectionSystem:
    """Integrated anomaly detection, recording, and reporting system"""
    
    def __init__(self):
        self.config = AnomalyConfig()
        self.logger = logging.getLogger(__name__)
        
        # Initialize components
        self.detector = AnomalyDetector()
        self.recorder = AnomalyRecorder()
        self.reporter = AnomalyReporter()
        
        # System state
        self.enabled = False
        self.processing = False
        self.last_anomaly_time = 0
        self.anomaly_cooldown = 5.0  # seconds between anomaly triggers
        
        # Performance tracking
        self.total_frames_processed = 0
        self.total_anomalies_detected = 0
        self.system_start_time = datetime.now()
        
        # Thread safety
        self.system_lock = threading.Lock()
        
        print("🚨 Integrated Anomaly Detection System initialized")
        print(f"🎯 System ready: Detector={self.detector.is_ready()}")
    
    def enable(self):
        """Enable anomaly detection system"""
        with self.system_lock:
            self.enabled = True
            print("✅ Anomaly Detection System ENABLED")
    
    def disable(self):
        """Disable anomaly detection system"""
        with self.system_lock:
            self.enabled = False
            # Stop any ongoing recording
            if self.recorder.is_recording:
                self.recorder.stop_recording()
            print("⏹️ Anomaly Detection System DISABLED")
    
    def process_frame(self, frame: np.ndarray, camera_info: Optional[Dict] = None) -> Tuple[np.ndarray, Dict]:
        """
        Process frame for anomaly detection
        Returns: (annotated_frame, detection_info)
        """
        if not self.enabled or not self.detector.is_ready():
            return frame, {'enabled': False}
        
        try:
            with self.system_lock:
                self.processing = True
                start_time = time.time()
                
                # Add frame to recording buffer
                self.recorder.add_frame_to_buffer(frame)
                
                # Detect objects and anomalies
                detections, anomaly_detected = self.detector.detect_objects(frame)
                
                # Create annotated frame
                annotated_frame = self._draw_detections(frame.copy(), detections)
                
                # Process anomaly if detected
                anomaly_info = None
                if anomaly_detected and self._should_trigger_anomaly():
                    anomaly_info = self._handle_anomaly_detection(detections, frame, camera_info)
                
                # Write frame to current recording if active
                if self.recorder.is_recording:
                    self.recorder.write_frame(annotated_frame)

                    # Debug output for recording
                    if hasattr(self.recorder, 'current_recording') and self.recorder.current_recording:
                        frame_count = self.recorder.current_recording.get('frame_count', 0)
                        if frame_count % 60 == 0:  # Every 2 seconds at 30 FPS
                            print(f"🎬 Anomaly system: {frame_count} frames written to recording")
                
                # Update statistics
                self.total_frames_processed += 1
                if anomaly_detected:
                    self.total_anomalies_detected += 1
                
                processing_time = time.time() - start_time
                
                # Prepare detection info
                detection_info = {
                    'enabled': True,
                    'anomaly_detected': anomaly_detected,
                    'detections': detections,
                    'processing_time': processing_time,
                    'recorder_status': self.recorder.get_status(),
                    'detector_fps': self.detector.get_fps(),
                    'anomaly_info': anomaly_info
                }
                
                self.processing = False
                return annotated_frame, detection_info
                
        except Exception as e:
            self.logger.error(f"Error processing frame: {e}")
            self.processing = False
            return frame, {'enabled': True, 'error': str(e)}
    
    def _should_trigger_anomaly(self) -> bool:
        """Check if enough time has passed since last anomaly trigger"""
        current_time = time.time()
        if current_time - self.last_anomaly_time > self.anomaly_cooldown:
            self.last_anomaly_time = current_time
            return True
        return False
    
    def _handle_anomaly_detection(self, detections: List[Dict], frame: np.ndarray, camera_info: Optional[Dict]) -> Dict:
        """Handle detected anomaly - start recording, generate report, and log to database"""
        try:
            print("🚨 ANOMALY DETECTED - Initiating response...")

            # Prepare anomaly data
            anomaly_data = {
                'timestamp': datetime.now().isoformat(),
                'detections': detections,
                'processing_time': 0,  # Will be updated
                'camera_info': camera_info or {},
                'system_info': {
                    'detector_fps': self.detector.get_fps(),
                    'total_frames_processed': self.total_frames_processed,
                    'total_anomalies': self.total_anomalies_detected
                }
            }

            # Log to unified database immediately
            self._log_anomaly_to_database(detections, anomaly_data)
            
            # Start recording
            recording_started = self.recorder.start_anomaly_recording(anomaly_data, frame)
            
            if recording_started:
                print("📹 Anomaly recording started")
                anomaly_data['recording_info'] = {
                    'started': True,
                    'filename': os.path.basename(self.recorder.current_recording['path']) if self.recorder.current_recording else None
                }
            else:
                print("⚠️ Recording could not be started (may be queued)")
                anomaly_data['recording_info'] = {'started': False}
            
            # Generate report in background thread
            report_thread = threading.Thread(
                target=self._generate_anomaly_report,
                args=(anomaly_data, frame),
                daemon=True
            )
            report_thread.start()
            
            return anomaly_data
            
        except Exception as e:
            self.logger.error(f"Error handling anomaly detection: {e}")
            return {'error': str(e)}

    def _log_anomaly_to_database(self, detections: List[Dict], anomaly_data: Dict):
        """Log anomaly detection to unified database in real-time"""
        try:
            # Import database integration
            from utils.database_integration import get_database

            database = get_database()
            if database:
                # Determine anomaly type and threat level from detections
                anomaly_types = []
                threat_levels = []
                detected_objects = []
                max_confidence = 0.0

                for detection in detections:
                    if detection.get('is_anomaly', False):
                        anomaly_type = detection.get('anomaly_type', 'suspicious_object')
                        anomaly_types.append(anomaly_type)

                        # Determine threat level based on anomaly type
                        if anomaly_type in ['weapon', 'gun', 'knife']:
                            threat_levels.append('HIGH')
                        elif anomaly_type in ['suspicious_object', 'unusual_behavior']:
                            threat_levels.append('MEDIUM')
                        else:
                            threat_levels.append('LOW')

                        detected_objects.append(detection.get('class_name', 'unknown'))
                        max_confidence = max(max_confidence, detection.get('confidence', 0.0))

                # Use the highest threat level detected
                if 'HIGH' in threat_levels:
                    threat_level = 'HIGH'
                elif 'MEDIUM' in threat_levels:
                    threat_level = 'MEDIUM'
                else:
                    threat_level = 'LOW'

                # Combine anomaly types
                anomaly_type = ', '.join(set(anomaly_types)) if anomaly_types else 'general_anomaly'

                # Create description
                description = f"Anomaly detected with {len(detections)} suspicious objects: {', '.join(detected_objects)}"

                # Generate file paths (will be updated when files are created)
                timestamp_str = datetime.now().strftime('%Y%m%d_%H%M%S')
                recording_path = f"recordings/anomaly_{timestamp_str}.mp4"
                report_path = f"reports/anomaly_report_{timestamp_str}.pdf"

                # Log to unified database immediately
                success = database.log_anomaly_detection(
                    anomaly_type=anomaly_type,
                    confidence=max_confidence,
                    threat_level=threat_level,
                    description=description,
                    recording_path=recording_path,
                    report_path=report_path,
                    objects_detected=detected_objects
                )

                if success:
                    print(f"📊 Anomaly detection logged to unified DB: {anomaly_type} (Threat: {threat_level}, Confidence: {max_confidence:.1%})")
                else:
                    print(f"❌ Failed to log anomaly detection to unified database")

        except Exception as e:
            print(f"⚠️ Error logging anomaly detection to database: {e}")
            # Continue processing even if database logging fails
            pass
    
    def _generate_anomaly_report(self, anomaly_data: Dict, screenshot: np.ndarray):
        """Generate anomaly report in background thread"""
        try:
            print("📊 Generating anomaly report...")
            report_paths = self.reporter.generate_anomaly_report(anomaly_data, screenshot)
            
            if report_paths:
                print(f"✅ Anomaly report generated: {len(report_paths)} files")
                for format_type, path in report_paths.items():
                    print(f"   {format_type.upper()}: {os.path.basename(path)}")
            else:
                print("❌ Failed to generate anomaly report")
                
        except Exception as e:
            self.logger.error(f"Error generating anomaly report: {e}")
    
    def _draw_detections(self, frame: np.ndarray, detections: List[Dict]) -> np.ndarray:
        """Draw detection boxes and labels on frame"""
        try:
            for detection in detections:
                bbox = detection['bbox']
                class_name = detection['class_name']
                confidence = detection['confidence']
                is_anomaly = detection.get('is_anomaly', False)
                anomaly_type = detection.get('anomaly_type', 'normal')
                
                # Choose color based on anomaly status
                if is_anomaly:
                    if anomaly_type == 'suspicious_object':
                        color = (0, 0, 255)  # Red for suspicious
                    elif anomaly_type == 'unknown_object':
                        color = (255, 0, 255)  # Magenta for unknown
                    else:
                        color = self.config.anomaly_box_color
                    thickness = 3
                else:
                    color = self.config.normal_box_color
                    thickness = 2
                
                # Draw bounding box
                cv2.rectangle(frame, (bbox[0], bbox[1]), (bbox[2], bbox[3]), color, thickness)
                
                # Prepare label
                if is_anomaly:
                    label = f"ANOMALY: {class_name} ({confidence:.2f})"
                    label_color = (255, 255, 255)  # White text for anomalies
                else:
                    label = f"{class_name} ({confidence:.2f})"
                    label_color = (0, 0, 0)  # Black text for normal objects
                
                # Draw label background
                label_size = cv2.getTextSize(label, cv2.FONT_HERSHEY_SIMPLEX, 0.6, 2)[0]
                cv2.rectangle(
                    frame,
                    (bbox[0], bbox[1] - label_size[1] - 10),
                    (bbox[0] + label_size[0], bbox[1]),
                    color,
                    -1
                )
                
                # Draw label text
                cv2.putText(
                    frame,
                    label,
                    (bbox[0], bbox[1] - 5),
                    cv2.FONT_HERSHEY_SIMPLEX,
                    0.6,
                    label_color,
                    2
                )
            
            # Draw system status
            self._draw_system_status(frame)
            
            return frame
            
        except Exception as e:
            self.logger.error(f"Error drawing detections: {e}")
            return frame
    
    def _draw_system_status(self, frame: np.ndarray):
        """Draw system status information on frame"""
        try:
            height, width = frame.shape[:2]
            
            # Status text
            status_text = f"Anomaly Detection: {'ON' if self.enabled else 'OFF'}"
            recording_text = f"Recording: {'ON' if self.recorder.is_recording else 'OFF'}"
            fps_text = f"Detection FPS: {self.detector.get_fps():.1f}"
            
            # Draw status background
            status_bg_height = 80
            cv2.rectangle(frame, (10, 10), (300, status_bg_height), (0, 0, 0), -1)
            cv2.rectangle(frame, (10, 10), (300, status_bg_height), (255, 255, 255), 2)
            
            # Draw status text
            cv2.putText(frame, status_text, (20, 30), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 255, 0) if self.enabled else (0, 0, 255), 2)
            cv2.putText(frame, recording_text, (20, 50), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 0, 0) if self.recorder.is_recording else (255, 255, 255), 2)
            cv2.putText(frame, fps_text, (20, 70), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 2)
            
        except Exception as e:
            self.logger.error(f"Error drawing system status: {e}")
    
    def get_system_status(self) -> Dict:
        """Get comprehensive system status"""
        return {
            'enabled': self.enabled,
            'processing': self.processing,
            'detector_ready': self.detector.is_ready(),
            'detector_status': self.detector.get_status(),
            'recorder_status': self.recorder.get_status(),
            'reporter_stats': self.reporter.get_statistics(),
            'system_stats': {
                'total_frames_processed': self.total_frames_processed,
                'total_anomalies_detected': self.total_anomalies_detected,
                'uptime_seconds': (datetime.now() - self.system_start_time).total_seconds(),
                'anomaly_rate': self.total_anomalies_detected / max(1, self.total_frames_processed) * 100
            }
        }
    
    def get_recent_anomalies(self, limit: int = 10) -> Dict:
        """Get recent anomaly recordings and reports"""
        return {
            'recordings': self.recorder.get_recent_recordings(limit),
            'reports': self.reporter.get_recent_reports(limit)
        }
    
    def cleanup_old_files(self):
        """Manually trigger cleanup of old files"""
        try:
            self.recorder._cleanup_old_recordings()
            print("🗑️ Old files cleanup completed")
        except Exception as e:
            self.logger.error(f"Error during cleanup: {e}")
    
    def is_enabled(self) -> bool:
        """Check if system is enabled"""
        return self.enabled
    
    def is_ready(self) -> bool:
        """Check if system is ready for operation"""
        return self.detector.is_ready()
