"""
Database Migration System for AI Video Detection Dashboard
Migrates data from old DatabaseManager to unified DetectionDatabase
"""

import sqlite3
import os
import shutil
from datetime import datetime
from typing import Dict, List, Optional
import json

class DatabaseMigration:
    """Handles migration from old dashboard database to unified database"""
    
    def __init__(self):
        self.old_db_path = "detection_results.db"  # Dashboard database
        self.new_db_path = "detection_results_unified.db"  # Unified database
        self.backup_path = f"detection_results_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}.db"
        
    def check_migration_needed(self) -> bool:
        """Check if migration is needed"""
        try:
            if not os.path.exists(self.old_db_path):
                print("📊 No existing database found - no migration needed")
                return False
                
            # Check if old database has data
            conn = sqlite3.connect(self.old_db_path)
            cursor = conn.cursor()
            
            # Check for data in any table
            tables = ['age_detections', 'object_detections', 'expression_detections', 'anomaly_detections']
            total_records = 0
            
            for table in tables:
                try:
                    cursor.execute(f"SELECT COUNT(*) FROM {table}")
                    count = cursor.fetchone()[0]
                    total_records += count
                    print(f"📊 Found {count} records in {table}")
                except sqlite3.OperationalError:
                    # Table doesn't exist
                    continue
            
            conn.close()
            
            if total_records > 0:
                print(f"🔄 Migration needed: {total_records} total records found")
                return True
            else:
                print("📊 No data found - no migration needed")
                return False
                
        except Exception as e:
            print(f"❌ Error checking migration status: {e}")
            return False
    
    def backup_existing_database(self) -> bool:
        """Create backup of existing database"""
        try:
            if os.path.exists(self.old_db_path):
                shutil.copy2(self.old_db_path, self.backup_path)
                print(f"✅ Database backed up to: {self.backup_path}")
                return True
            return False
        except Exception as e:
            print(f"❌ Error creating backup: {e}")
            return False
    
    def migrate_data(self) -> bool:
        """Migrate data from old database to unified database"""
        try:
            if not self.check_migration_needed():
                return True
                
            print("🔄 Starting database migration...")
            
            # Create backup
            if not self.backup_existing_database():
                print("⚠️ Could not create backup - proceeding anyway")
            
            # Initialize unified database
            from utils.database_integration import DetectionDatabase
            unified_db = DetectionDatabase(self.old_db_path)  # Use same path
            
            # Connect to old database structure
            conn = sqlite3.connect(self.old_db_path)
            cursor = conn.cursor()
            
            # Migrate each table
            migration_results = {}
            
            # Migrate age detections
            migration_results['age'] = self._migrate_age_detections(cursor, unified_db)
            
            # Migrate object detections  
            migration_results['object'] = self._migrate_object_detections(cursor, unified_db)
            
            # Migrate expression detections
            migration_results['expression'] = self._migrate_expression_detections(cursor, unified_db)
            
            # Migrate anomaly detections
            migration_results['anomaly'] = self._migrate_anomaly_detections(cursor, unified_db)
            
            conn.close()
            
            # Print migration summary
            total_migrated = sum(migration_results.values())
            print(f"✅ Migration completed: {total_migrated} total records migrated")
            for det_type, count in migration_results.items():
                print(f"   📊 {det_type}: {count} records")
            
            return True
            
        except Exception as e:
            print(f"❌ Error during migration: {e}")
            return False
    
    def _migrate_age_detections(self, cursor, unified_db) -> int:
        """Migrate age detection records"""
        try:
            cursor.execute("SELECT * FROM age_detections ORDER BY timestamp")
            records = cursor.fetchall()
            
            # Get column names
            cursor.execute("PRAGMA table_info(age_detections)")
            columns = [col[1] for col in cursor.fetchall()]
            
            migrated_count = 0
            for record in records:
                data = dict(zip(columns, record))
                
                # Convert to unified format
                age = data.get('age', 30)
                age_range = data.get('age_range', '')
                
                # If no age_range, generate from age
                if not age_range and age:
                    if age < 13:
                        age_range = "Child (0-12)"
                    elif age < 20:
                        age_range = "Teen (13-19)"
                    elif age < 35:
                        age_range = "Young Adult (20-34)"
                    elif age < 55:
                        age_range = "Adult (35-54)"
                    else:
                        age_range = "Senior (55+)"
                
                success = unified_db.log_age_detection(
                    age=age,
                    age_range=age_range,
                    confidence=data.get('confidence', 0.0),
                    model_used=data.get('model_used', 'Unknown'),
                    face_bbox=data.get('face_bbox', ''),
                    processing_time=data.get('processing_time', 0.0)
                )
                
                if success:
                    migrated_count += 1
            
            print(f"✅ Migrated {migrated_count} age detection records")
            return migrated_count
            
        except Exception as e:
            print(f"❌ Error migrating age detections: {e}")
            return 0
    
    def _migrate_object_detections(self, cursor, unified_db) -> int:
        """Migrate object detection records"""
        try:
            cursor.execute("SELECT * FROM object_detections ORDER BY timestamp")
            records = cursor.fetchall()
            
            # Get column names
            cursor.execute("PRAGMA table_info(object_detections)")
            columns = [col[1] for col in cursor.fetchall()]
            
            migrated_count = 0
            for record in records:
                data = dict(zip(columns, record))
                
                # Convert bbox format
                bbox = (
                    data.get('bbox_x', 0),
                    data.get('bbox_y', 0), 
                    data.get('bbox_w', 0),
                    data.get('bbox_h', 0)
                )
                
                success = unified_db.log_object_detection(
                    object_name=data.get('object_name', 'unknown'),
                    confidence=data.get('confidence', 0.0),
                    bbox=bbox,
                    detection_method=data.get('detection_method', 'YOLO'),
                    is_anomaly=bool(data.get('is_anomaly', False))
                )
                
                if success:
                    migrated_count += 1
            
            print(f"✅ Migrated {migrated_count} object detection records")
            return migrated_count
            
        except Exception as e:
            print(f"❌ Error migrating object detections: {e}")
            return 0
    
    def _migrate_expression_detections(self, cursor, unified_db) -> int:
        """Migrate expression detection records"""
        try:
            cursor.execute("SELECT * FROM expression_detections ORDER BY timestamp")
            records = cursor.fetchall()
            
            # Get column names
            cursor.execute("PRAGMA table_info(expression_detections)")
            columns = [col[1] for col in cursor.fetchall()]
            
            migrated_count = 0
            for record in records:
                data = dict(zip(columns, record))
                
                success = unified_db.log_expression_detection(
                    expression=data.get('expression', 'unknown'),
                    confidence=data.get('confidence', 0.0),
                    model_used=data.get('model_used', 'Unknown'),
                    coordinates=data.get('coordinates', ''),
                    face_bbox=data.get('face_bbox', ''),
                    processing_time=data.get('processing_time', 0.0)
                )
                
                if success:
                    migrated_count += 1
            
            print(f"✅ Migrated {migrated_count} expression detection records")
            return migrated_count
            
        except Exception as e:
            print(f"❌ Error migrating expression detections: {e}")
            return 0
    
    def _migrate_anomaly_detections(self, cursor, unified_db) -> int:
        """Migrate anomaly detection records"""
        try:
            cursor.execute("SELECT * FROM anomaly_detections ORDER BY timestamp")
            records = cursor.fetchall()
            
            # Get column names
            cursor.execute("PRAGMA table_info(anomaly_detections)")
            columns = [col[1] for col in cursor.fetchall()]
            
            migrated_count = 0
            for record in records:
                data = dict(zip(columns, record))
                
                success = unified_db.log_anomaly_detection(
                    anomaly_type=data.get('anomaly_type', 'unknown'),
                    confidence=data.get('confidence', 0.0),
                    threat_level=data.get('threat_level', 'LOW'),
                    description=data.get('description', ''),
                    recording_path=data.get('recording_path', ''),
                    report_path=data.get('report_path', '')
                )
                
                if success:
                    migrated_count += 1
            
            print(f"✅ Migrated {migrated_count} anomaly detection records")
            return migrated_count
            
        except Exception as e:
            print(f"❌ Error migrating anomaly detections: {e}")
            return 0

def run_migration():
    """Run database migration if needed"""
    migration = DatabaseMigration()
    return migration.migrate_data()

if __name__ == "__main__":
    run_migration()
