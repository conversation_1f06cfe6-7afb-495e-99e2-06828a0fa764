"""
Download Required Models for Enhanced Face Detection and Expression Analysis
This script downloads all the necessary model files to optimize the AI video detection system
"""

import os
import urllib.request
import urllib.error
import ssl
import zipfile
import gzip
import shutil
from pathlib import Path
import hashlib

def create_ssl_context():
    """Create SSL context that allows downloads"""
    context = ssl.create_default_context()
    context.check_hostname = False
    context.verify_mode = ssl.CERT_NONE
    return context

def download_file(url, destination, description="file"):
    """Download a file with progress indication"""
    try:
        print(f"📥 Downloading {description}...")
        print(f"   URL: {url}")
        print(f"   Destination: {destination}")
        
        # Create SSL context for HTTPS downloads
        context = create_ssl_context()
        
        # Create directory if it doesn't exist
        os.makedirs(os.path.dirname(destination), exist_ok=True)
        
        # Download with progress
        def progress_hook(block_num, block_size, total_size):
            if total_size > 0:
                percent = min(100, (block_num * block_size * 100) // total_size)
                print(f"\r   Progress: {percent}% ({block_num * block_size}/{total_size} bytes)", end="")
        
        urllib.request.urlretrieve(url, destination, progress_hook)
        print(f"\n✅ Successfully downloaded {description}")
        return True
        
    except Exception as e:
        print(f"\n❌ Failed to download {description}: {e}")
        return False

def verify_file_size(filepath, min_size_kb=1):
    """Verify that downloaded file has reasonable size"""
    if os.path.exists(filepath):
        size_kb = os.path.getsize(filepath) / 1024
        if size_kb >= min_size_kb:
            print(f"✅ File size verification passed: {size_kb:.1f} KB")
            return True
        else:
            print(f"❌ File too small: {size_kb:.1f} KB (minimum: {min_size_kb} KB)")
            return False
    return False

def download_opencv_dnn_models():
    """Download OpenCV DNN face detection models"""
    print("\n🔍 DOWNLOADING OPENCV DNN FACE DETECTION MODELS")
    print("=" * 60)
    
    models_dir = "models"
    os.makedirs(models_dir, exist_ok=True)
    
    # OpenCV DNN face detection models
    models = [
        {
            "url": "https://raw.githubusercontent.com/opencv/opencv/master/samples/dnn/face_detector/opencv_face_detector.pbtxt",
            "filename": "opencv_face_detector.pbtxt",
            "description": "OpenCV DNN Face Detector Config",
            "min_size_kb": 1
        },
        {
            "url": "https://raw.githubusercontent.com/opencv/opencv_3rdparty/dnn_samples_face_detector_20170830/opencv_face_detector_uint8.pb",
            "filename": "opencv_face_detector_uint8.pb",
            "description": "OpenCV DNN Face Detector Model",
            "min_size_kb": 2000  # Should be around 2.6 MB
        }
    ]
    
    success_count = 0
    for model in models:
        filepath = os.path.join(models_dir, model["filename"])
        
        if os.path.exists(filepath) and verify_file_size(filepath, model["min_size_kb"]):
            print(f"✅ {model['description']} already exists and verified")
            success_count += 1
            continue
        
        if download_file(model["url"], filepath, model["description"]):
            if verify_file_size(filepath, model["min_size_kb"]):
                success_count += 1
            else:
                print(f"⚠️ Downloaded file failed verification, removing...")
                try:
                    os.remove(filepath)
                except:
                    pass
    
    print(f"\n📊 OpenCV DNN Models: {success_count}/{len(models)} downloaded successfully")
    return success_count == len(models)

def download_additional_cascades():
    """Download additional Haar cascade models for facial features"""
    print("\n👁️ DOWNLOADING ADDITIONAL HAAR CASCADE MODELS")
    print("=" * 60)
    
    models_dir = "models"
    os.makedirs(models_dir, exist_ok=True)
    
    # Additional Haar cascade models
    cascades = [
        {
            "url": "https://raw.githubusercontent.com/opencv/opencv/master/data/haarcascades/haarcascade_eye.xml",
            "filename": "haarcascade_eye.xml",
            "description": "Eye Detection Cascade",
            "min_size_kb": 10
        },
        {
            "url": "https://raw.githubusercontent.com/opencv/opencv/master/data/haarcascades/haarcascade_eye_tree_eyeglasses.xml",
            "filename": "haarcascade_eye_tree_eyeglasses.xml",
            "description": "Eye with Glasses Detection Cascade",
            "min_size_kb": 10
        },
        {
            "url": "https://raw.githubusercontent.com/opencv/opencv/master/data/haarcascades/haarcascade_smile.xml",
            "filename": "haarcascade_smile.xml",
            "description": "Smile Detection Cascade",
            "min_size_kb": 10
        }
    ]
    
    success_count = 0
    for cascade in cascades:
        filepath = os.path.join(models_dir, cascade["filename"])
        
        if os.path.exists(filepath) and verify_file_size(filepath, cascade["min_size_kb"]):
            print(f"✅ {cascade['description']} already exists and verified")
            success_count += 1
            continue
        
        if download_file(cascade["url"], filepath, cascade["description"]):
            if verify_file_size(filepath, cascade["min_size_kb"]):
                success_count += 1
            else:
                print(f"⚠️ Downloaded file failed verification, removing...")
                try:
                    os.remove(filepath)
                except:
                    pass
    
    print(f"\n📊 Additional Cascades: {success_count}/{len(cascades)} downloaded successfully")
    return success_count == len(cascades)

def download_age_detection_models():
    """Download age detection models if not present"""
    print("\n👶 CHECKING AGE DETECTION MODELS")
    print("=" * 60)
    
    models_dir = "models"
    age_models = [
        "age_deploy.prototxt",
        "age_net.caffemodel"
    ]
    
    existing_count = 0
    for model in age_models:
        filepath = os.path.join(models_dir, model)
        if os.path.exists(filepath):
            size_mb = os.path.getsize(filepath) / (1024 * 1024)
            print(f"✅ {model} exists ({size_mb:.1f} MB)")
            existing_count += 1
        else:
            print(f"❌ {model} not found")
    
    if existing_count == len(age_models):
        print("✅ All age detection models are present")
        return True
    else:
        print("⚠️ Some age detection models are missing")
        print("💡 Age detection models need to be obtained separately due to licensing")
        print("   You can find them at: https://github.com/GilLevi/AgeGenderDeepLearning")
        return False

def create_model_info_file():
    """Create a file with information about downloaded models"""
    info_content = """# Downloaded Models Information

## OpenCV DNN Face Detection Models
- opencv_face_detector.pbtxt: Configuration file for OpenCV DNN face detector
- opencv_face_detector_uint8.pb: Pre-trained model weights for face detection
- Purpose: High-accuracy face detection for better expression analysis input

## Additional Haar Cascade Models
- haarcascade_eye.xml: Eye detection for facial feature validation
- haarcascade_eye_tree_eyeglasses.xml: Eye detection including glasses
- haarcascade_smile.xml: Smile detection for expression validation

## Usage
These models enhance the face detection accuracy and provide better input
for the custom YOLOv8 expression detection model (emotion_detection_83.6_percent.pt).

## Integration
The models are automatically loaded by the enhanced face detection system
when available in the models/ directory.
"""
    
    with open("models/MODEL_INFO.md", "w") as f:
        f.write(info_content)
    print("📝 Created MODEL_INFO.md with download information")

def main():
    """Main download function"""
    print("🚀 AI VIDEO DETECTION SYSTEM - MODEL DOWNLOADER")
    print("=" * 80)
    print("This script will download required models to enhance face detection accuracy")
    print("and improve integration with your custom YOLOv8 expression detection model.")
    print()
    
    # Check if models directory exists
    if not os.path.exists("models"):
        os.makedirs("models")
        print("📁 Created models directory")
    
    # Download OpenCV DNN models
    opencv_success = download_opencv_dnn_models()
    
    # Download additional cascades
    cascade_success = download_additional_cascades()
    
    # Check age detection models
    age_success = download_age_detection_models()
    
    # Create info file
    create_model_info_file()
    
    # Summary
    print("\n" + "=" * 80)
    print("📋 DOWNLOAD SUMMARY")
    print("=" * 80)
    
    print(f"🔍 OpenCV DNN Face Detection: {'✅ SUCCESS' if opencv_success else '❌ FAILED'}")
    if opencv_success:
        print("   • High-accuracy face detection enabled")
        print("   • Better input for expression detection")
    
    print(f"👁️ Additional Haar Cascades: {'✅ SUCCESS' if cascade_success else '❌ FAILED'}")
    if cascade_success:
        print("   • Enhanced facial feature validation")
        print("   • Improved eye and smile detection")
    
    print(f"👶 Age Detection Models: {'✅ PRESENT' if age_success else '⚠️ MISSING'}")
    if not age_success:
        print("   • Age detection will use fallback methods")
        print("   • For full accuracy, obtain models separately")
    
    print(f"\n🎯 EXPECTED IMPROVEMENTS:")
    if opencv_success:
        print("✅ Face detection accuracy: Significantly improved")
        print("✅ Expression detection input quality: Enhanced")
        print("✅ False positive reduction: Better filtering")
    
    if cascade_success:
        print("✅ Facial feature validation: More comprehensive")
        print("✅ Eye detection accuracy: Improved")
        print("✅ Expression analysis reliability: Enhanced")
    
    print(f"\n🚀 NEXT STEPS:")
    print("1. Run 'python main.py' to test the enhanced system")
    print("2. Start camera and press SPACE for expression detection")
    print("3. Check console for improved detection logs")
    print("4. Verify enhanced accuracy with real faces")
    
    if opencv_success or cascade_success:
        print("\n🎉 ENHANCED MODELS READY!")
        print("Your face detection accuracy should be significantly improved!")
    else:
        print("\n⚠️ Some downloads failed. Check your internet connection and try again.")

if __name__ == "__main__":
    main()
