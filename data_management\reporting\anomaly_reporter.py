"""
Automated Anomaly Report Generation System
Generates detailed reports for anomaly detection events
"""

import cv2
import numpy as np
import json
import os
from datetime import datetime
from typing import Dict, List, Optional
import logging

# Try to import reportlab for PDF generation
try:
    from reportlab.lib.pagesizes import letter, A4
    from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Image, Table, TableStyle
    from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
    from reportlab.lib.units import inch
    from reportlab.lib import colors
    PDF_AVAILABLE = True
except ImportError:
    PDF_AVAILABLE = False
    print("⚠️ ReportLab not available. PDF reports will be disabled.")
    print("💡 Install with: pip install reportlab")

from utils.anomaly_config import AnomalyConfig

class AnomalyReporter:
    """Automated report generation for anomaly detection events"""
    
    def __init__(self):
        self.config = AnomalyConfig()
        self.logger = logging.getLogger(__name__)
        
        # Create reports directory
        self.config.create_directories()
        
        # Report statistics
        self.total_reports = 0
        self.reports_by_type = {}
        
        print("📊 Anomaly Reporting System initialized")
        if PDF_AVAILABLE:
            print("✅ PDF report generation available")
        else:
            print("⚠️ PDF report generation disabled (install reportlab)")
    
    def generate_anomaly_report(self, anomaly_data: Dict, screenshot: Optional[np.ndarray] = None) -> Dict[str, str]:
        """
        Generate comprehensive anomaly report in multiple formats
        Returns: Dictionary with paths to generated reports
        """
        try:
            timestamp = datetime.now().strftime("%Y-%m-%d_%H-%M-%S")
            report_paths = {}
            
            # Save screenshot if provided
            screenshot_path = None
            if screenshot is not None and self.config.include_screenshots:
                screenshot_path = self._save_screenshot(screenshot, timestamp)
                report_paths['screenshot'] = screenshot_path
            
            # Generate text report
            if self.config.generate_text_reports:
                text_path = self._generate_text_report(anomaly_data, timestamp, screenshot_path)
                report_paths['text'] = text_path
            
            # Generate JSON report
            if self.config.generate_json_reports:
                json_path = self._generate_json_report(anomaly_data, timestamp, screenshot_path)
                report_paths['json'] = json_path
            
            # Generate PDF report
            if self.config.generate_pdf_reports and PDF_AVAILABLE:
                pdf_path = self._generate_pdf_report(anomaly_data, timestamp, screenshot_path)
                report_paths['pdf'] = pdf_path
            
            # Update statistics
            self._update_statistics(anomaly_data)
            
            print(f"📋 Anomaly report generated: {len(report_paths)} files")
            return report_paths
            
        except Exception as e:
            self.logger.error(f"Error generating anomaly report: {e}")
            print(f"❌ Report generation failed: {e}")
            return {}
    
    def _save_screenshot(self, screenshot: np.ndarray, timestamp: str) -> str:
        """Save anomaly screenshot"""
        try:
            screenshot_path = self.config.get_screenshot_path(timestamp)
            
            # Save with high quality
            cv2.imwrite(
                screenshot_path, 
                screenshot, 
                [cv2.IMWRITE_JPEG_QUALITY, self.config.screenshot_quality]
            )
            
            print(f"📸 Screenshot saved: {screenshot_path}")
            return screenshot_path
            
        except Exception as e:
            self.logger.error(f"Error saving screenshot: {e}")
            return None
    
    def _generate_text_report(self, anomaly_data: Dict, timestamp: str, screenshot_path: Optional[str]) -> str:
        """Generate detailed text report"""
        try:
            report_path = self.config.get_report_path(timestamp, 'txt')
            
            with open(report_path, 'w') as f:
                f.write("=" * 60 + "\n")
                f.write("ANOMALY DETECTION REPORT\n")
                f.write("=" * 60 + "\n\n")
                
                # Basic information
                f.write(f"Report Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
                f.write(f"Anomaly Timestamp: {anomaly_data.get('timestamp', 'Unknown')}\n")
                f.write(f"Detection System: YOLO Object Detection\n\n")
                
                # Anomaly details
                f.write("ANOMALY DETAILS:\n")
                f.write("-" * 20 + "\n")
                
                detections = anomaly_data.get('detections', [])
                anomaly_count = 0
                
                for i, detection in enumerate(detections):
                    if detection.get('is_anomaly', False):
                        anomaly_count += 1
                        f.write(f"\nAnomaly #{anomaly_count}:\n")
                        f.write(f"  Object: {detection.get('class_name', 'Unknown')}\n")
                        f.write(f"  Type: {detection.get('anomaly_type', 'Unknown')}\n")
                        f.write(f"  Confidence: {detection.get('confidence', 0):.2f}\n")
                        
                        bbox = detection.get('bbox', (0, 0, 0, 0))
                        f.write(f"  Location: ({bbox[0]}, {bbox[1]}) to ({bbox[2]}, {bbox[3]})\n")
                        
                        size = detection.get('size', (0, 0))
                        f.write(f"  Size: {size[0]} x {size[1]} pixels\n")
                
                # System information
                f.write(f"\nTOTAL ANOMALIES DETECTED: {anomaly_count}\n\n")
                
                f.write("SYSTEM INFORMATION:\n")
                f.write("-" * 20 + "\n")
                f.write(f"Detection Model: YOLOv3\n")
                f.write(f"Confidence Threshold: {self.config.anomaly_confidence_threshold}\n")
                f.write(f"NMS Threshold: {self.config.yolo_nms_threshold}\n")
                f.write(f"Processing Time: {anomaly_data.get('processing_time', 0):.3f} seconds\n")
                
                # Camera settings
                camera_info = anomaly_data.get('camera_info', {})
                if camera_info:
                    f.write(f"Camera Resolution: {camera_info.get('width', 'Unknown')} x {camera_info.get('height', 'Unknown')}\n")
                    f.write(f"Camera FPS: {camera_info.get('fps', 'Unknown')}\n")
                
                # Screenshot reference
                if screenshot_path:
                    f.write(f"\nScreenshot: {os.path.basename(screenshot_path)}\n")
                
                # Recording information
                recording_info = anomaly_data.get('recording_info', {})
                if recording_info:
                    f.write(f"\nRecording: {recording_info.get('filename', 'Not recorded')}\n")
                    f.write(f"Recording Duration: {recording_info.get('duration', 0)} seconds\n")
                
                f.write("\n" + "=" * 60 + "\n")
                f.write("End of Report\n")
            
            print(f"📄 Text report saved: {report_path}")
            return report_path
            
        except Exception as e:
            self.logger.error(f"Error generating text report: {e}")
            return None
    
    def _generate_json_report(self, anomaly_data: Dict, timestamp: str, screenshot_path: Optional[str]) -> str:
        """Generate JSON report for easy parsing"""
        try:
            report_path = self.config.get_report_path(timestamp, 'json')
            
            # Prepare JSON data
            json_data = {
                'report_info': {
                    'generated_at': datetime.now().isoformat(),
                    'anomaly_timestamp': anomaly_data.get('timestamp'),
                    'report_version': '1.0',
                    'detection_system': 'YOLO Object Detection'
                },
                'anomaly_summary': {
                    'total_detections': len(anomaly_data.get('detections', [])),
                    'anomaly_count': len([d for d in anomaly_data.get('detections', []) if d.get('is_anomaly', False)]),
                    'processing_time_seconds': anomaly_data.get('processing_time', 0)
                },
                'anomalies': [],
                'system_info': {
                    'model': 'YOLOv3',
                    'confidence_threshold': self.config.anomaly_confidence_threshold,
                    'nms_threshold': self.config.yolo_nms_threshold,
                    'input_size': self.config.yolo_input_size
                },
                'files': {
                    'screenshot': os.path.basename(screenshot_path) if screenshot_path else None,
                    'recording': anomaly_data.get('recording_info', {}).get('filename')
                }
            }
            
            # Add anomaly details
            for detection in anomaly_data.get('detections', []):
                if detection.get('is_anomaly', False):
                    anomaly_detail = {
                        'object_class': detection.get('class_name'),
                        'anomaly_type': detection.get('anomaly_type'),
                        'confidence': detection.get('confidence'),
                        'bounding_box': {
                            'x1': detection.get('bbox', (0, 0, 0, 0))[0],
                            'y1': detection.get('bbox', (0, 0, 0, 0))[1],
                            'x2': detection.get('bbox', (0, 0, 0, 0))[2],
                            'y2': detection.get('bbox', (0, 0, 0, 0))[3]
                        },
                        'center_point': {
                            'x': detection.get('center', (0, 0))[0],
                            'y': detection.get('center', (0, 0))[1]
                        },
                        'size': {
                            'width': detection.get('size', (0, 0))[0],
                            'height': detection.get('size', (0, 0))[1]
                        }
                    }
                    json_data['anomalies'].append(anomaly_detail)
            
            # Add camera information
            camera_info = anomaly_data.get('camera_info', {})
            if camera_info:
                json_data['camera_info'] = camera_info
            
            # Save JSON file
            with open(report_path, 'w') as f:
                json.dump(json_data, f, indent=2, default=str)
            
            print(f"📊 JSON report saved: {report_path}")
            return report_path
            
        except Exception as e:
            self.logger.error(f"Error generating JSON report: {e}")
            return None
    
    def _generate_pdf_report(self, anomaly_data: Dict, timestamp: str, screenshot_path: Optional[str]) -> str:
        """Generate PDF report (requires reportlab)"""
        if not PDF_AVAILABLE:
            return None
        
        try:
            report_path = self.config.get_report_path(timestamp, 'pdf')
            
            # Create PDF document
            doc = SimpleDocTemplate(report_path, pagesize=letter)
            styles = getSampleStyleSheet()
            story = []
            
            # Title
            title_style = ParagraphStyle(
                'CustomTitle',
                parent=styles['Heading1'],
                fontSize=18,
                spaceAfter=30,
                textColor=colors.darkblue
            )
            story.append(Paragraph("ANOMALY DETECTION REPORT", title_style))
            story.append(Spacer(1, 12))
            
            # Basic information
            info_data = [
                ['Report Generated:', datetime.now().strftime('%Y-%m-%d %H:%M:%S')],
                ['Anomaly Timestamp:', str(anomaly_data.get('timestamp', 'Unknown'))],
                ['Detection System:', 'YOLO Object Detection'],
                ['Processing Time:', f"{anomaly_data.get('processing_time', 0):.3f} seconds"]
            ]
            
            info_table = Table(info_data, colWidths=[2*inch, 3*inch])
            info_table.setStyle(TableStyle([
                ('BACKGROUND', (0, 0), (0, -1), colors.lightgrey),
                ('TEXTCOLOR', (0, 0), (-1, -1), colors.black),
                ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
                ('FONTNAME', (0, 0), (-1, -1), 'Helvetica'),
                ('FONTSIZE', (0, 0), (-1, -1), 10),
                ('BOTTOMPADDING', (0, 0), (-1, -1), 12),
                ('GRID', (0, 0), (-1, -1), 1, colors.black)
            ]))
            story.append(info_table)
            story.append(Spacer(1, 20))
            
            # Screenshot
            if screenshot_path and os.path.exists(screenshot_path):
                story.append(Paragraph("Anomaly Screenshot:", styles['Heading2']))
                story.append(Spacer(1, 12))
                
                # Resize image to fit page
                img = Image(screenshot_path, width=5*inch, height=3.75*inch)
                story.append(img)
                story.append(Spacer(1, 20))
            
            # Anomaly details
            story.append(Paragraph("Anomaly Details:", styles['Heading2']))
            story.append(Spacer(1, 12))
            
            anomaly_data_table = [['#', 'Object', 'Type', 'Confidence', 'Location']]
            
            anomaly_count = 0
            for detection in anomaly_data.get('detections', []):
                if detection.get('is_anomaly', False):
                    anomaly_count += 1
                    bbox = detection.get('bbox', (0, 0, 0, 0))
                    location = f"({bbox[0]}, {bbox[1]})"
                    
                    anomaly_data_table.append([
                        str(anomaly_count),
                        detection.get('class_name', 'Unknown'),
                        detection.get('anomaly_type', 'Unknown'),
                        f"{detection.get('confidence', 0):.2f}",
                        location
                    ])
            
            if anomaly_count > 0:
                anomaly_table = Table(anomaly_data_table, colWidths=[0.5*inch, 1.5*inch, 1.5*inch, 1*inch, 1.5*inch])
                anomaly_table.setStyle(TableStyle([
                    ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
                    ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
                    ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
                    ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
                    ('FONTSIZE', (0, 0), (-1, 0), 12),
                    ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
                    ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
                    ('GRID', (0, 0), (-1, -1), 1, colors.black)
                ]))
                story.append(anomaly_table)
            else:
                story.append(Paragraph("No anomalies detected in this frame.", styles['Normal']))
            
            # Build PDF
            doc.build(story)
            
            print(f"📑 PDF report saved: {report_path}")
            return report_path
            
        except Exception as e:
            self.logger.error(f"Error generating PDF report: {e}")
            return None
    
    def _update_statistics(self, anomaly_data: Dict):
        """Update report statistics"""
        self.total_reports += 1
        
        for detection in anomaly_data.get('detections', []):
            if detection.get('is_anomaly', False):
                anomaly_type = detection.get('anomaly_type', 'unknown')
                self.reports_by_type[anomaly_type] = self.reports_by_type.get(anomaly_type, 0) + 1
    
    def get_statistics(self) -> Dict:
        """Get reporting statistics"""
        return {
            'total_reports': self.total_reports,
            'reports_by_type': self.reports_by_type,
            'pdf_available': PDF_AVAILABLE
        }
    
    def get_recent_reports(self, limit: int = 10) -> List[Dict]:
        """Get list of recent reports"""
        try:
            reports_dir = self.config.reports_dir
            if not os.path.exists(reports_dir):
                return []
            
            reports = []
            for filename in os.listdir(reports_dir):
                if filename.startswith('anomaly_report_') and filename.endswith('.json'):
                    file_path = os.path.join(reports_dir, filename)
                    created = datetime.fromtimestamp(os.path.getctime(file_path))
                    
                    reports.append({
                        'filename': filename,
                        'path': file_path,
                        'created': created,
                        'type': 'json'
                    })
            
            # Sort by creation time (newest first)
            reports.sort(key=lambda x: x['created'], reverse=True)
            return reports[:limit]
            
        except Exception as e:
            self.logger.error(f"Error getting recent reports: {e}")
            return []
