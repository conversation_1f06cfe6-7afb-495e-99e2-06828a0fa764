"""
Installation script for anomaly detection dependencies
Run this script to install required packages for the anomaly detection system
"""

import subprocess
import sys
import os

def install_package(package):
    """Install a package using pip"""
    try:
        print(f"Installing {package}...")
        subprocess.check_call([sys.executable, "-m", "pip", "install", package])
        print(f"✅ {package} installed successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to install {package}: {e}")
        return False

def main():
    """Main installation function"""
    print("🚨 Anomaly Detection System - Dependency Installation")
    print("=" * 60)
    
    # Required packages for anomaly detection
    required_packages = [
        "reportlab",  # For PDF report generation
        "Pillow",     # For image processing (if not already installed)
        "numpy",      # For numerical operations (if not already installed)
        "opencv-python",  # For computer vision (if not already installed)
    ]
    
    # Optional packages
    optional_packages = [
        "matplotlib",  # For plotting in reports
        "seaborn",     # For enhanced visualizations
    ]
    
    print("📦 Installing required packages...")
    success_count = 0
    
    for package in required_packages:
        if install_package(package):
            success_count += 1
    
    print(f"\n📊 Installation Summary:")
    print(f"   Required packages: {success_count}/{len(required_packages)} installed")
    
    if success_count == len(required_packages):
        print("✅ All required packages installed successfully!")
        
        # Ask about optional packages
        print(f"\n🔧 Optional packages available:")
        for package in optional_packages:
            print(f"   - {package}")
        
        install_optional = input("\nInstall optional packages? (y/n): ").lower().strip()
        
        if install_optional in ['y', 'yes']:
            print("\n📦 Installing optional packages...")
            for package in optional_packages:
                install_package(package)
    else:
        print("❌ Some required packages failed to install.")
        print("💡 Try running this script as administrator or check your internet connection.")
    
    print("\n🎯 Anomaly Detection Features:")
    print("   ✅ YOLO object detection")
    print("   ✅ Automated 10-second video recording")
    print("   ✅ PDF, JSON, and text report generation")
    print("   ✅ Automatic cleanup of old files")
    print("   ✅ Real-time anomaly alerts")
    print("   ✅ Integration with existing age and emotion detection")
    
    print("\n📁 Folders that will be created:")
    print("   - anomaly_recordings/  (for video recordings)")
    print("   - anomaly_reports/     (for generated reports)")
    
    print("\n🚀 Ready to use! Start your application and enable anomaly detection.")
    
    input("\nPress Enter to exit...")

if __name__ == "__main__":
    main()
