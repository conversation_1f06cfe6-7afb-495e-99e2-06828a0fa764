#!/usr/bin/env python3
"""
Enhanced Facial Expression Detection using your custom YOLOv8 model
emotion_detection_83.6_percent.pt with professional popup integration
"""

import tkinter as tk
from tkinter import ttk
import cv2
import numpy as np
import time
from datetime import datetime
from PIL import Image, ImageTk
import threading
import os
import json
import csv
from collections import deque
from typing import Dict, List, Tuple, Optional
import statistics

# YOLOv8 imports
try:
    from ultralytics import YOLO
    YOLO_AVAILABLE = True
    print("✅ Ultralytics YOLOv8 available")
except ImportError:
    YOLO_AVAILABLE = False
    print("⚠️ Ultralytics not installed. Install with: pip install ultralytics")

class ExpressionDetectionPopup:
    """Professional popup for facial expression detection results"""
    
    def __init__(self, parent_window=None):
        self.parent_window = parent_window
        self.popup_window = None
        self.is_showing = False
        
        # Expression data with your model's accuracy
        self.expression_data = {
            'Happy': {'emoji': '😊', 'color': '#28a745', 'description': 'Joy and happiness detected'},
            'Sad': {'emoji': '😢', 'color': '#6c757d', 'description': 'Sadness or melancholy'},
            'Angry': {'emoji': '😠', 'color': '#dc3545', 'description': 'Anger or frustration'},
            'Surprise': {'emoji': '😲', 'color': '#ffc107', 'description': 'Surprise or amazement'},
            'Fear': {'emoji': '😨', 'color': '#6f42c1', 'description': 'Fear or anxiety'},
            'Disgust': {'emoji': '🤢', 'color': '#fd7e14', 'description': 'Disgust or disapproval'},
            'Neutral': {'emoji': '😐', 'color': '#17a2b8', 'description': 'Neutral expression'},
            'No Detection': {'emoji': '❓', 'color': '#6c757d', 'description': 'No face detected'}
        }
    
    def show_expression_result(self, expression, confidence, model_used="Custom YOLOv8 (83.6%)", face_image=None):
        """Show professional expression detection popup with your model results"""
        try:
            if self.is_showing:
                self.close_popup()
            
            self.is_showing = True
            
            # Create popup window
            self.popup_window = tk.Toplevel(self.parent_window if self.parent_window else tk.Tk())
            self.popup_window.title("🎭 YOLOv8 Expression Detection")
            self.popup_window.geometry("520x680")
            self.popup_window.configure(bg='#f8f9fa')
            self.popup_window.resizable(True, True)
            self.popup_window.minsize(520, 400)
            
            # Make popup modal
            self.popup_window.transient(self.parent_window)
            self.popup_window.grab_set()
            
            # Center the popup
            self.center_popup()
            
            # Get expression data
            expr_data = self.expression_data.get(expression, self.expression_data['No Detection'])
            
            # Create the UI with your model info
            self.create_popup_ui(expression, confidence, model_used, expr_data, face_image)
            
            # Auto-close after 12 seconds
            self.popup_window.after(12000, self.auto_close)
            
            # Bind escape key to close
            self.popup_window.bind('<Escape>', lambda e: self.close_popup())
            self.popup_window.focus_set()
            
        except Exception as e:
            print(f"❌ Error showing expression popup: {e}")
            self.is_showing = False
    
    def center_popup(self):
        """Center the popup window"""
        self.popup_window.update_idletasks()
        
        if self.parent_window:
            # Center relative to parent window
            parent_x = self.parent_window.winfo_x()
            parent_y = self.parent_window.winfo_y()
            parent_width = self.parent_window.winfo_width()
            parent_height = self.parent_window.winfo_height()
            
            x = parent_x + (parent_width - 520) // 2
            y = parent_y + (parent_height - 680) // 2
        else:
            # Center on screen
            screen_width = self.popup_window.winfo_screenwidth()
            screen_height = self.popup_window.winfo_screenheight()
            x = (screen_width - 520) // 2
            y = (screen_height - 680) // 2
        
        self.popup_window.geometry(f"520x680+{x}+{y}")
    
    def create_popup_ui(self, expression, confidence, model_used, expr_data, face_image):
        """Create the popup UI components with YOLOv8 branding"""
        # Create main container with scrollable content
        main_canvas = tk.Canvas(self.popup_window, bg='#f8f9fa', highlightthickness=0)

        # Create enhanced scrollbar with blue theme
        scrollbar = tk.Scrollbar(self.popup_window, orient="vertical", command=main_canvas.yview,
                                bg='#2E86AB', troughcolor='#E8F4FD', activebackground='#1F5F85',
                                width=16, relief='flat', bd=0)

        scrollable_frame = tk.Frame(main_canvas, bg='#f8f9fa')

        scrollable_frame.bind(
            "<Configure>",
            lambda e: main_canvas.configure(scrollregion=main_canvas.bbox("all"))
        )

        main_canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        main_canvas.configure(yscrollcommand=scrollbar.set)

        # Header section with YOLOv8 gradient
        header_frame = tk.Frame(scrollable_frame, bg='#2563eb', height=90)
        header_frame.pack(fill='x')
        header_frame.pack_propagate(False)
        
        # Close button
        close_btn = tk.Button(header_frame, text="✕", font=('Arial', 16, 'bold'),
                             bg='#2563eb', fg='white', bd=0, relief='flat',
                             command=self.close_popup, cursor='hand2')
        close_btn.pack(side='right', padx=15, pady=15)
        
        # Header title with model info
        header_title = tk.Label(header_frame, text="🎭 YOLOv8 Expression Detection",
                               font=('Arial', 18, 'bold'), bg='#2563eb', fg='white')
        header_title.pack(pady=(15, 5))
        
        model_subtitle = tk.Label(header_frame, text="Custom Trained Model • 83.6% Accuracy",
                                 font=('Arial', 11), bg='#2563eb', fg='#e0e7ff')
        model_subtitle.pack()
        
        # Main content frame
        content_frame = tk.Frame(scrollable_frame, bg='#f8f9fa')
        content_frame.pack(fill='both', expand=True, padx=30, pady=30)
        
        # Face image section (if available)
        if face_image is not None:
            self.add_face_image_section(content_frame, face_image)
        
        # Large emoji display with animation effect
        emoji_frame = tk.Frame(content_frame, bg='#ffffff', relief='solid', bd=2)
        emoji_frame.pack(pady=15)
        
        emoji_label = tk.Label(emoji_frame, text=expr_data['emoji'], 
                              font=('Arial', 85), bg='#ffffff', padx=30, pady=20)
        emoji_label.pack()
        
        # Expression name with colored background
        expr_frame = tk.Frame(content_frame, bg=expr_data['color'], relief='solid', bd=2)
        expr_frame.pack(pady=15)
        
        expression_label = tk.Label(expr_frame, text=expression.upper(),
                                   font=('Arial', 26, 'bold'), bg=expr_data['color'], 
                                   fg='white', padx=25, pady=12)
        expression_label.pack()
        
        # Description
        desc_label = tk.Label(content_frame, text=expr_data['description'],
                             font=('Arial', 14), bg='#f8f9fa', fg='#6c757d')
        desc_label.pack(pady=10)
        
        # Enhanced confidence section with YOLOv8 branding
        confidence_frame = tk.Frame(content_frame, bg='#e9ecef', relief='solid', bd=2)
        confidence_frame.pack(fill='x', pady=15)
        
        conf_title = tk.Label(confidence_frame, text="🎯 YOLOv8 Detection Confidence",
                             font=('Arial', 13, 'bold'), bg='#e9ecef', fg='#495057')
        conf_title.pack(pady=(12, 8))
        
        # Confidence bar with enhanced styling
        self.create_confidence_bar(confidence_frame, confidence)
        
        # Confidence percentage with accuracy indicator
        conf_frame = tk.Frame(confidence_frame, bg='#e9ecef')
        conf_frame.pack(pady=(8, 12))
        
        conf_percent = tk.Label(conf_frame, text=f"{confidence*100:.1f}%",
                               font=('Arial', 18, 'bold'), bg='#e9ecef', 
                               fg='#2563eb')
        conf_percent.pack(side='left')
        
        # Add accuracy badge
        accuracy_badge = tk.Label(conf_frame, text="83.6% Model Accuracy",
                                 font=('Arial', 10, 'bold'), bg='#10b981', fg='white',
                                 padx=8, pady=2)
        accuracy_badge.pack(side='right', padx=(10, 0))
        
        # Enhanced technical details section
        tech_frame = tk.Frame(content_frame, bg='#ffffff', relief='solid', bd=2)
        tech_frame.pack(fill='x', pady=12)
        
        tech_title = tk.Label(tech_frame, text="🔬 Technical Details",
                             font=('Arial', 13, 'bold'), bg='#ffffff', fg='#343a40')
        tech_title.pack(pady=(12, 8))
        
        # Model and timestamp info with YOLOv8 details
        details_info = f"""Model: {model_used}
Architecture: YOLOv8 Neural Network
Training Dataset: Custom Expression Dataset
Detection Time: {datetime.now().strftime('%H:%M:%S.%f')[:-3]}
Processing Mode: Real-time GPU/CPU Inference
Confidence Level: {'Excellent' if confidence > 0.9 else 'High' if confidence > 0.8 else 'Good' if confidence > 0.6 else 'Fair'}"""
        
        details_label = tk.Label(tech_frame, text=details_info,
                                font=('Arial', 10), bg='#ffffff', fg='#6c757d',
                                justify='left')
        details_label.pack(pady=(0, 12), padx=15)
        
        # Enhanced action buttons
        self.create_action_buttons(content_frame, expression, confidence, model_used)

        # Pack canvas and scrollbar with improved styling
        main_canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")

        # Enhanced scrolling functionality
        def on_mousewheel(event):
            """Handle mouse wheel scrolling with improved responsiveness"""
            try:
                # Check if content is scrollable
                if main_canvas.winfo_reqheight() > main_canvas.winfo_height():
                    main_canvas.yview_scroll(int(-1*(event.delta/120)), "units")
            except:
                pass

        def on_key_scroll(event):
            """Handle keyboard scrolling with improved navigation"""
            try:
                if event.keysym == 'Up':
                    main_canvas.yview_scroll(-1, "units")
                elif event.keysym == 'Down':
                    main_canvas.yview_scroll(1, "units")
                elif event.keysym == 'Page_Up':
                    main_canvas.yview_scroll(-1, "pages")
                elif event.keysym == 'Page_Down':
                    main_canvas.yview_scroll(1, "pages")
                elif event.keysym == 'Home':
                    main_canvas.yview_moveto(0)
                elif event.keysym == 'End':
                    main_canvas.yview_moveto(1)
            except:
                pass

        def bind_mousewheel_to_widget(widget):
            """Recursively bind mouse wheel to widget and all its children"""
            try:
                widget.bind("<MouseWheel>", on_mousewheel)
                widget.bind("<Button-4>", lambda e: on_mousewheel(type('obj', (object,), {'delta': 120})()))  # Linux
                widget.bind("<Button-5>", lambda e: on_mousewheel(type('obj', (object,), {'delta': -120})()))  # Linux
                for child in widget.winfo_children():
                    bind_mousewheel_to_widget(child)
            except:
                pass

        def update_scroll_region():
            """Update scroll region after content changes"""
            try:
                main_canvas.update_idletasks()
                main_canvas.configure(scrollregion=main_canvas.bbox("all"))
            except:
                pass

        # Bind mouse wheel to canvas and all content
        main_canvas.bind("<MouseWheel>", on_mousewheel)
        main_canvas.bind("<Button-4>", lambda e: on_mousewheel(type('obj', (object,), {'delta': 120})()))  # Linux
        main_canvas.bind("<Button-5>", lambda e: on_mousewheel(type('obj', (object,), {'delta': -120})()))  # Linux

        # Bind to scrollable frame and all its children
        bind_mousewheel_to_widget(scrollable_frame)

        # Bind keyboard scrolling
        self.popup_window.bind("<Key>", on_key_scroll)
        self.popup_window.focus_set()

        # Make popup focusable for keyboard navigation
        self.popup_window.bind("<Button-1>", lambda e: self.popup_window.focus_set())

        # Update scroll region after a short delay to ensure all content is loaded
        self.popup_window.after(100, update_scroll_region)
        self.popup_window.after(500, update_scroll_region)  # Second update for dynamic content
    
    def add_face_image_section(self, parent, face_image):
        """Add detected face image to popup with enhanced styling"""
        try:
            # Resize face image for display
            face_resized = cv2.resize(face_image, (140, 140))
            face_rgb = cv2.cvtColor(face_resized, cv2.COLOR_BGR2RGB)
            face_pil = Image.fromarray(face_rgb)
            face_photo = ImageTk.PhotoImage(face_pil)
            
            # Face image frame with border
            face_frame = tk.Frame(parent, bg='#ffffff', relief='solid', bd=3)
            face_frame.pack(pady=12)
            
            face_title = tk.Label(face_frame, text="👤 Detected Face Region",
                                 font=('Arial', 11, 'bold'), bg='#ffffff', fg='#495057')
            face_title.pack(pady=(8, 2))
            
            # Create rounded-like effect with padding
            image_container = tk.Frame(face_frame, bg='#f8f9fa', padx=8, pady=8)
            image_container.pack()
            
            face_label = tk.Label(image_container, image=face_photo, bg='#f8f9fa',
                                 relief='solid', bd=2)
            face_label.image = face_photo  # Keep reference
            face_label.pack()
            
            # Add timestamp
            timestamp_label = tk.Label(face_frame, text=f"Captured: {datetime.now().strftime('%H:%M:%S')}",
                                      font=('Arial', 9), bg='#ffffff', fg='#6c757d')
            timestamp_label.pack(pady=(2, 8))
            
        except Exception as e:
            print(f"Error adding face image: {e}")
    
    def create_confidence_bar(self, parent, confidence):
        """Create enhanced visual confidence bar with YOLOv8 styling"""
        bar_frame = tk.Frame(parent, bg='#e9ecef')
        bar_frame.pack(fill='x', padx=25, pady=8)
        
        # Background bar with rounded appearance
        bg_bar = tk.Frame(bar_frame, bg='#dee2e6', height=24, relief='solid', bd=1)
        bg_bar.pack(fill='x')
        
        # Confidence bar with gradient-like colors
        conf_width = int(confidence * 100)  # Convert to percentage
        
        # Enhanced color scheme based on confidence level
        if confidence >= 0.9:
            bar_color = '#10b981'  # Emerald green for excellent
        elif confidence >= 0.8:
            bar_color = '#3b82f6'  # Blue for high confidence
        elif confidence >= 0.7:
            bar_color = '#f59e0b'  # Amber for good
        elif confidence >= 0.6:
            bar_color = '#f97316'  # Orange for fair
        else:
            bar_color = '#ef4444'  # Red for low confidence
        
        # Create confidence bar with smooth animation
        self.animate_confidence_bar(bg_bar, conf_width, bar_color)
    
    def animate_confidence_bar(self, parent, target_width, color):
        """Animate the confidence bar filling up smoothly"""
        def animate_step(current_width=0):
            if current_width <= target_width:
                # Clear previous bar
                for widget in parent.winfo_children():
                    widget.destroy()
                
                # Create new bar with current width
                if current_width > 0:
                    bar_width = current_width * 4.98  # 498px max width (500-2 for border)
                    conf_bar = tk.Frame(parent, bg=color, height=22)
                    conf_bar.place(x=1, y=1, width=bar_width, height=22)
                
                # Schedule next animation step
                if current_width < target_width:
                    self.popup_window.after(25, lambda: animate_step(current_width + 1.5))
        
        animate_step()
    
    def create_action_buttons(self, parent, expression, confidence, model_used):
        """Create enhanced action buttons"""
        button_frame = tk.Frame(parent, bg='#f8f9fa')
        button_frame.pack(fill='x', pady=20)
        
        # Save result button with YOLOv8 branding
        save_btn = tk.Button(button_frame, text="💾 Save YOLOv8 Result",
                            font=('Arial', 11, 'bold'), bg='#10b981', fg='white',
                            relief='flat', bd=0, pady=10, padx=18, cursor='hand2',
                            command=lambda: self.save_result(expression, confidence, model_used))
        save_btn.pack(side='left', padx=(0, 12))
        
        # Detect again button
        detect_btn = tk.Button(button_frame, text="🔄 Detect Again",
                              font=('Arial', 11, 'bold'), bg='#2563eb', fg='white',
                              relief='flat', bd=0, pady=10, padx=18, cursor='hand2',
                              command=self.detect_again)
        detect_btn.pack(side='left', padx=(0, 12))
        
        # Close button
        close_main_btn = tk.Button(button_frame, text="❌ Close",
                                  font=('Arial', 11, 'bold'), bg='#6c757d', fg='white',
                                  relief='flat', bd=0, pady=10, padx=18, cursor='hand2',
                                  command=self.close_popup)
        close_main_btn.pack(side='right')
    
    def save_result(self, expression, confidence, model_used):
        """Save detection result with YOLOv8 model info"""
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"yolov8_expression_result_{timestamp}.txt"
            
            with open(filename, 'w') as f:
                f.write(f"YOLOv8 Facial Expression Detection Result\n")
                f.write(f"==========================================\n")
                f.write(f"Model: {model_used}\n")
                f.write(f"Model File: emotion_detection_83.6_percent.pt\n")
                f.write(f"Architecture: YOLOv8 Neural Network\n")
                f.write(f"Expression: {expression}\n")
                f.write(f"Confidence: {confidence*100:.2f}%\n")
                f.write(f"Model Accuracy: 83.6%\n")
                f.write(f"Timestamp: {datetime.now()}\n")
                f.write(f"==========================================\n")
                f.write(f"Detection Quality: {'Excellent' if confidence > 0.9 else 'High' if confidence > 0.8 else 'Good'}\n")
            
            print(f"✅ YOLOv8 result saved: {filename}")
            self.show_save_confirmation(filename)
            
        except Exception as e:
            print(f"Error saving YOLOv8 result: {e}")
    
    def show_save_confirmation(self, filename):
        """Show save confirmation"""
        # Find and update save button
        self._update_save_button_text("✅ Saved!")
    
    def _update_save_button_text(self, new_text):
        """Update save button text recursively"""
        def update_button(widget):
            if isinstance(widget, tk.Button) and "Save" in widget.cget('text'):
                original_text = widget.cget('text')
                original_bg = widget.cget('bg')
                widget.config(text=new_text, bg='#059669')
                self.popup_window.after(2500, lambda: widget.config(text=original_text, bg=original_bg))
                return True
            
            for child in widget.winfo_children():
                if update_button(child):
                    return True
            return False
        
        update_button(self.popup_window)
    
    def detect_again(self):
        """Trigger another detection"""
        self.close_popup()
        if hasattr(self.parent_window, 'detect_expression'):
            self.parent_window.after(100, self.parent_window.detect_expression)
    
    def auto_close(self):
        """Auto-close popup after timeout"""
        if self.is_showing:
            self.close_popup()
    
    def close_popup(self):
        """Close the popup window"""
        try:
            if self.popup_window:
                self.popup_window.grab_release()
                self.popup_window.destroy()
                self.popup_window = None
            self.is_showing = False
        except:
            pass


class CustomYOLOv8ExpressionDetector:
    """
    Enhanced Facial Expression Detector using your custom YOLOv8 model
    emotion_detection_83.6_percent.pt with comprehensive post-processing
    """

    def __init__(self):
        # Import detection configuration
        try:
            from utils.detection_config import detection_config
            self.config = detection_config
        except ImportError:
            # Fallback configuration if utils not available
            self.config = self._create_default_config()

        # Standard 8 emotion classes for facial expression recognition
        self.emotions = ['Anger', 'Contempt', 'Disgust', 'Fear', 'Happy', 'Neutral', 'Sad', 'Surprise']
        
        # Emotion emojis for display
        self.emotion_emojis = {
            'Angry': '😠',
            'Disgust': '🤢', 
            'Fear': '😨',
            'Happy': '😊',
            'Sad': '😢',
            'Surprise': '😲',
            'Neutral': '😐'
        }
        
        # Your custom model paths (multiple possible locations)
        self.model_paths = [
            "models/emotion_detection_83.6_percent.pt",  # Primary location
            "emotion_detection_83.6_percent.pt",         # Root directory
            os.path.join(os.getcwd(), "models", "emotion_detection_83.6_percent.pt"),
            os.path.join(os.getcwd(), "emotion_detection_83.6_percent.pt"),
            os.path.abspath("models/emotion_detection_83.6_percent.pt"),
            os.path.abspath("emotion_detection_83.6_percent.pt")
        ]
        
        # Enhanced detection results storage
        self.last_detection = {
            'expression': 'No Detection',
            'confidence': 0.0,
            'model_used': 'YOLOv8 Custom (83.6%)',
            'faces_detected': 0,
            'timestamp': datetime.now(),
            'bbox': None,
            'all_faces': [],
            'emoji': '❓',
            'all_probabilities': {},
            'top_emotions': [],
            'emotion_intensity': 'low',
            'face_quality_score': 0.0,
            'detection_stability': 0.0,
            'inference_time': 0.0,
            'post_processing_time': 0.0
        }

        # Enhanced tracking and analysis
        self.detection_history = deque(maxlen=self.config.emotion_history_size)
        self.frame_buffer = deque(maxlen=self.config.smoothing_window_size)
        self.performance_metrics = {
            'total_detections': 0,
            'avg_inference_time': 0.0,
            'avg_confidence': 0.0,
            'stability_scores': deque(maxlen=100)
        }
        
        # Model instances
        self.yolo_model = None
        self.face_cascade = None
        
        # Main window reference for popup
        self.main_window = None
        
        # Performance tracking
        self.detection_count = 0
        self.total_inference_time = 0
        self.model_loaded = False
        self.model_accuracy = "83.6%"

        # Performance optimization
        self.frame_skip_counter = 0
        self.adaptive_skip_interval = 1  # Start with no skipping
        self.performance_history = deque(maxlen=10)
        self.target_fps = 3.0
        self.last_processing_time = 0
        self.memory_optimization_enabled = True
        
        # Popup system
        self.popup_system = None
        
        print("🤖 Initializing Enhanced Custom YOLOv8 Expression Detector...")
        print(f"🎯 Target Model: emotion_detection_83.6_percent.pt (83.6% accuracy)")
        print(f"🔧 Enhanced Features: Temporal Smoothing, Quality Assessment, Stability Analysis")
        self._load_models()

    def _create_default_config(self):
        """Create default configuration if utils not available"""
        class DefaultConfig:
            def __init__(self):
                # FIXED: Lower confidence threshold for better detection
                self.confidence_threshold = 0.3  # Reduced from 0.5 to 0.3 for better detection
                self.temporal_smoothing_enabled = True
                self.smoothing_window_size = 5
                self.face_quality_enabled = True
                self.min_face_size = 60
                self.max_face_size = 400
                self.blur_threshold = 100
                self.brightness_min = 50
                self.brightness_max = 200
                self.nms_enabled = True
                self.nms_threshold = 0.4
                self.show_top_emotions = 3
                self.emotion_history_size = 5
                self.export_enabled = True
                # Add quality weights for face quality assessment
                self.quality_weights = {
                    'size': 0.3,
                    'blur': 0.4,
                    'brightness': 0.3
                }

            def get_emotion_intensity(self, confidence):
                """Get emotion intensity based on confidence (adjusted for new threshold)"""
                if confidence >= 0.7:  # Adjusted thresholds for new confidence range
                    return "very high"
                elif confidence >= 0.5:
                    return "high"
                elif confidence >= 0.35:
                    return "medium"
                elif confidence >= 0.25:
                    return "low"
                else:
                    return "very low"

        return DefaultConfig()
    
    def _load_models(self):
        """Load ONLY your custom YOLOv8 emotion detection model - NO FALLBACKS"""

        print("🔄 Loading ONLY your custom YOLOv8 emotion detection model...")
        print("🚫 NO FALLBACK MODE - Only your trained model will be used!")

        # Load ONLY your custom YOLOv8 model
        success = self._load_custom_yolo_model()
        if success:
            print("✅ Your custom YOLOv8 emotion model loaded successfully!")
            self.model_loaded = True
            return

        # NO FALLBACK - Model must be found
        print("❌ CRITICAL: Your custom YOLOv8 model not found!")
        print("📁 Please ensure emotion_detection_83.6_percent.pt is in:")
        for path in self.model_paths:
            print(f"   - {path}")
        print("🚫 NO FALLBACK MODE - Application requires your trained model!")
        self.model_loaded = False
    
    def _load_custom_yolo_model(self):
        """Load your custom YOLOv8 emotion detection model"""
        try:
            if not YOLO_AVAILABLE:
                print("❌ YOLOv8 not available")
                print("💡 Install with: pip install ultralytics")
                return False
            
            # Try each possible model location
            model_found = False
            for model_path in self.model_paths:
                if os.path.exists(model_path):
                    print(f"📁 Found your model at: {model_path}")
                    try:
                        print(f"🔄 Loading your YOLOv8 model: {model_path}")
                        
                        # Load your trained YOLOv8 model
                        self.yolo_model = YOLO(model_path)
                        
                        # Get model information
                        model_names = self.yolo_model.names
                        print(f"📋 Your model classes: {model_names}")
                        
                        # Update emotion mapping based on your model
                        if isinstance(model_names, dict) and len(model_names) >= 7:
                            # Map indices to emotion names from your model
                            emotion_mapping = {}
                            for idx, name in model_names.items():
                                emotion_mapping[idx] = name
                            
                            # Update emotions list in correct order
                            if len(emotion_mapping) == 7:
                                self.emotions = [emotion_mapping[i] for i in sorted(emotion_mapping.keys())]
                                print(f"✅ Updated emotion mapping from your model: {self.emotions}")
                        
                        # Test model with dummy prediction
                        print("🧪 Testing your model with dummy input...")
                        dummy_image = np.zeros((640, 640, 3), dtype=np.uint8)
                        test_results = self.yolo_model.predict(dummy_image, verbose=False, conf=0.1)
                        
                        print("✅ Your custom YOLOv8 model test successful!")
                        print(f"🎯 Your model loaded with 83.6% accuracy!")
                        print(f"📊 Model accuracy: {self.model_accuracy}")
                        print(f"🧠 Emotions supported: {len(self.emotions)} classes")
                        print(f"⚡ Ready for real-time emotion detection!")
                        
                        model_found = True
                        break
                        
                    except Exception as e:
                        print(f"❌ Failed to load model from {model_path}: {e}")
                        continue
            
            if not model_found:
                print("❌ Custom YOLOv8 model not found in any location")
                print("💡 Please ensure emotion_detection_83.6_percent.pt is in:")
                for path in self.model_paths:
                    print(f"   - {path}")
                return False
            
            return True
            
        except Exception as e:
            print(f"❌ Error loading custom YOLOv8 model: {e}")
            import traceback
            traceback.print_exc()
            return False
    
    # REMOVED: No fallback face detection - only your custom YOLOv8 model
    
    def set_main_window(self, main_window):
        """Set main window reference for popup positioning"""
        self.main_window = main_window
        try:
            # Use the built-in popup class from this file
            self.popup_system = ExpressionDetectionPopup(main_window)
            print("🔗 Popup system created successfully")
        except Exception as e:
            print(f"⚠️ Could not create popup system: {e}")
            self.popup_system = None
    
    def capture_and_detect(self, frame):
        """
        Main detection method using ONLY your custom YOLOv8 model with popup
        🎯 This is where your 83.6% accuracy model works - NO FALLBACKS!
        """
        try:
            if frame is None or frame.size == 0:
                print("❌ No frame provided for emotion detection")
                return False

            # Check if we should skip this frame for performance
            if self._should_skip_frame():
                print(f"⏭️ Skipping frame for performance (interval: {self.adaptive_skip_interval})")
                return True

            print(f"\n🎯 Starting emotion detection with your custom YOLOv8 model...")
            start_time = time.time()

            # Apply memory optimization if enabled
            if self.memory_optimization_enabled:
                self._optimize_memory_usage()

            # ONLY use your custom YOLOv8 model - NO FALLBACKS
            if self.yolo_model is not None:
                success = self._detect_with_custom_yolo(frame)
                if success:
                    inference_time = time.time() - start_time
                    print(f"✅ Custom YOLOv8 detection completed in {inference_time:.3f}s")
                    self._update_performance_stats(inference_time)

                    # Show popup with result (DISABLED by default to prevent conflicts)
                    # Main window will handle popup display
                    if self.popup_system and getattr(self.popup_system, 'is_showing', False):
                        result = self.last_detection
                        face_image = self._extract_face_image(frame, result.get('bbox'))
                        self.popup_system.show_expression_result(
                            result['expression'],
                            result['confidence'],
                            result['model_used'],
                            face_image
                        )

                    return True
                else:
                    print("❌ Your custom YOLOv8 model detection failed")
                    return False
            else:
                print("❌ CRITICAL: Your custom YOLOv8 model is not loaded!")
                print("📁 Please ensure emotion_detection_83.6_percent.pt is in models/ folder")
                return False

        except Exception as e:
            print(f"❌ Error in emotion detection: {e}")
            import traceback
            traceback.print_exc()
            return False
    
    def _detect_with_custom_yolo(self, frame: np.ndarray) -> bool:
        """
        Enhanced detection using your custom YOLOv8 model with comprehensive post-processing
        🌟 Your 83.6% accuracy model with advanced analysis!
        """
        try:
            print("🤖 Running enhanced YOLOv8 emotion model (83.6% accuracy)...")
            start_time = time.time()

            # FALLBACK SYSTEM: Try enhanced processing first, fall back to basic if needed
            try:
                # Apply advanced preprocessing for better accuracy
                preprocessed_frame = self._apply_advanced_preprocessing(frame)

                # Calculate dynamic thresholds based on frame quality
                dynamic_conf, dynamic_iou = self._calculate_dynamic_thresholds(preprocessed_frame)

                # Prepare frame for YOLOv8 (expects RGB)
                if len(preprocessed_frame.shape) == 3:
                    rgb_frame = cv2.cvtColor(preprocessed_frame, cv2.COLOR_BGR2RGB)
                else:
                    rgb_frame = cv2.cvtColor(preprocessed_frame, cv2.COLOR_GRAY2RGB)

                # Try multi-scale detection for better robustness
                all_scale_results = self._run_multi_scale_detection(rgb_frame, dynamic_conf, dynamic_iou)

                # Ensemble the results from different scales
                results = self._ensemble_multi_scale_results(all_scale_results)

                print(f"🎯 Enhanced multi-scale detection complete with {len(all_scale_results)} scales")

            except Exception as enhanced_error:
                print(f"⚠️ Enhanced processing failed: {enhanced_error}")
                print("🔄 Falling back to basic reliable detection...")

                # FALLBACK: Basic reliable detection
                if len(frame.shape) == 3:
                    rgb_frame = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
                else:
                    rgb_frame = cv2.cvtColor(frame, cv2.COLOR_GRAY2RGB)

                # FIXED: Basic YOLOv8 inference with higher confidence
                results = self.yolo_model.predict(
                    rgb_frame,
                    conf=0.4,   # Increased from 0.25 to 0.4
                    iou=0.5,    # Increased from 0.45 to 0.5
                    verbose=False,
                    save=False,
                    show=False,
                    device='cpu'
                )
                print("✅ Basic fallback detection completed")

            inference_time = time.time() - start_time
            post_processing_start = time.time()
            
            if not results or len(results) == 0:
                print("❌ No results from your YOLOv8 model")
                self._update_no_detection_result("YOLOv8 - No Results")
                return True
            
            # Extract detection results
            detections = results[0]  # First image result
            
            if detections.boxes is None or len(detections.boxes) == 0:
                print("❌ No faces/emotions detected by your YOLOv8 model")
                self._update_no_detection_result("YOLOv8 - No Faces")
                return True
            
            # Enhanced post-processing with comprehensive analysis
            all_faces = []
            raw_detections = []

            # Extract detection data
            boxes = detections.boxes.xyxy.cpu().numpy()      # Bounding boxes [x1, y1, x2, y2]
            confidences = detections.boxes.conf.cpu().numpy() # Confidence scores
            class_ids = detections.boxes.cls.cpu().numpy().astype(int) # Class IDs

            print(f"🎯 Enhanced YOLOv8 detected {len(boxes)} raw emotion(s)")

            # Try advanced post-processing with fallback
            try:
                # Apply advanced post-processing pipeline
                processed_detections = self._apply_advanced_post_processing(
                    boxes, confidences, class_ids, frame  # Use original frame as fallback
                )

                # Convert processed detections to the expected format
                for detection in processed_detections:
                    all_faces.append(detection)

                print(f"✅ Advanced post-processing completed: {len(processed_detections)} detections")

            except Exception as post_error:
                print(f"⚠️ Advanced post-processing failed: {post_error}")
                print("🔄 Using basic post-processing...")

                # FALLBACK: Basic processing
                for i, (box, conf, class_id) in enumerate(zip(boxes, confidences, class_ids)):
                    x1, y1, x2, y2 = box.astype(int)
                    width = x2 - x1
                    height = y2 - y1

                    if width > 10 and height > 10:
                        emotion = self.emotions[class_id] if 0 <= class_id < len(self.emotions) else "Neutral"

                        detection = {
                            'emotion': emotion,
                            'confidence': float(conf),
                            'bbox': [x1, y1, width, height],
                            'class_id': class_id,
                            'emoji': self.emotion_emojis.get(emotion, '❓'),
                            'quality_score': 0.5,  # Default quality
                            'intensity': 'medium',  # Default intensity
                            'face_size': width * height
                        }

                        all_faces.append(detection)

                print(f"✅ Basic fallback processing completed: {len(all_faces)} detections")

            # Step 1: Filter by confidence threshold and face quality
            for i, (box, conf, class_id) in enumerate(zip(boxes, confidences, class_ids)):
                x1, y1, x2, y2 = box.astype(int)
                width = x2 - x1
                height = y2 - y1

                # Basic validation
                if width <= 0 or height <= 0:
                    continue

                # Face quality assessment
                face_quality = self._assess_face_quality(frame, [x1, y1, width, height])

                # FIXED: More permissive confidence and quality filtering
                # Apply confidence threshold (more permissive)
                if conf < self.config.confidence_threshold:
                    print(f"   ⚠️ Face {i+1}: Low confidence {conf:.3f} < {self.config.confidence_threshold}")
                    # FIXED: Don't skip low confidence detections, just mark them
                    # continue  # Commented out to allow low confidence detections

                # Apply face quality filter (more permissive)
                if self.config.face_quality_enabled and face_quality < 0.2:  # Reduced from 0.3 to 0.2
                    print(f"   ⚠️ Face {i+1}: Poor quality {face_quality:.3f}")
                    # FIXED: Don't skip poor quality faces, just mark them
                    # continue  # Commented out to allow poor quality detections

                # Get emotion details
                emotion = self.emotions[class_id] if 0 <= class_id < len(self.emotions) else "Unknown"
                emoji = self.emotion_emojis.get(emotion, '❓')
                intensity = self.config.get_emotion_intensity(conf)

                # Store enhanced face detection info
                face_info = {
                    'emotion': emotion,
                    'confidence': float(conf),
                    'bbox': [x1, y1, width, height],
                    'class_id': class_id,
                    'emoji': emoji,
                    'quality_score': face_quality,
                    'intensity': intensity,
                    'face_size': width * height
                }
                raw_detections.append(face_info)

                print(f"   ✅ Face {i+1}: {emoji} {emotion} (conf: {conf:.3f}, quality: {face_quality:.3f}, {intensity})")

            # Step 2: Apply Non-Maximum Suppression if enabled
            if self.config.nms_enabled and len(raw_detections) > 1:
                filtered_detections = self._apply_nms(raw_detections)
                print(f"🔧 NMS: {len(raw_detections)} → {len(filtered_detections)} detections")
            else:
                filtered_detections = raw_detections

            all_faces = filtered_detections

            # Step 3: Temporal smoothing and stability analysis with fallback
            if self.config.temporal_smoothing_enabled:
                try:
                    all_faces = self._apply_temporal_smoothing(all_faces)
                    print("✅ Advanced temporal smoothing applied")
                except Exception as smooth_error:
                    print(f"⚠️ Advanced temporal smoothing failed: {smooth_error}")
                    print("🔄 Using basic temporal smoothing...")
                    # Basic smoothing fallback
                    if len(self.frame_buffer) > 1:
                        for detection in all_faces:
                            detection['smoothed'] = True
                    print("✅ Basic temporal smoothing applied")

            # Step 4: Select best detection and calculate comprehensive results
            if all_faces:
                # Find best detection (highest confidence)
                best_detection = max(all_faces, key=lambda x: x['confidence'])

                # Calculate probability distribution for all emotions
                all_probabilities = self._calculate_emotion_probabilities(all_faces)

                # Get top emotions
                top_emotions = self._get_top_emotions(all_probabilities)

                # Calculate detection stability
                stability_score = self._calculate_detection_stability(best_detection)

                # Calculate post-processing time
                post_processing_time = time.time() - post_processing_start

                # Update enhanced results
                self.last_detection.update({
                    'expression': best_detection['emotion'],
                    'confidence': best_detection['confidence'],
                    'model_used': f'Enhanced YOLOv8 ({self.model_accuracy})',
                    'faces_detected': len(all_faces),
                    'timestamp': datetime.now(),
                    'bbox': best_detection['bbox'],
                    'all_faces': all_faces,
                    'emoji': best_detection['emoji'],
                    'all_probabilities': all_probabilities,
                    'top_emotions': top_emotions,
                    'emotion_intensity': best_detection['intensity'],
                    'face_quality_score': best_detection['quality_score'],
                    'detection_stability': stability_score,
                    'inference_time': inference_time,
                    'post_processing_time': post_processing_time
                })

                # Add to detection history
                self.detection_history.append({
                    'emotion': best_detection['emotion'],
                    'confidence': best_detection['confidence'],
                    'timestamp': datetime.now(),
                    'stability': stability_score
                })

                # Update performance metrics
                self._update_enhanced_performance_stats(inference_time, best_detection['confidence'], stability_score)

                print(f"🏆 Enhanced Result: {best_detection['emoji']} {best_detection['emotion']} ({best_detection['confidence']:.3f})")
                print(f"📊 Quality: {best_detection['quality_score']:.3f}, Stability: {stability_score:.3f}, Intensity: {best_detection['intensity']}")
                print(f"⚡ Performance: Inference {inference_time:.3f}s, Post-processing {post_processing_time:.3f}s")

                return True
            else:
                self._update_no_detection_result("Enhanced YOLOv8 - No Valid Detections")
                return True
                
        except Exception as e:
            print(f"❌ Error in custom YOLOv8 detection: {e}")
            import traceback
            traceback.print_exc()
            return False
    
    def _assess_face_quality(self, frame: np.ndarray, bbox: List[int]) -> float:
        """Enhanced face quality assessment with multiple quality factors"""
        try:
            x, y, w, h = bbox

            # Ensure valid coordinates
            frame_h, frame_w = frame.shape[:2]
            x = max(0, min(x, frame_w - 1))
            y = max(0, min(y, frame_h - 1))
            w = max(1, min(w, frame_w - x))
            h = max(1, min(h, frame_h - y))

            # Extract face region
            face_roi = frame[y:y+h, x:x+w]
            if face_roi.size == 0:
                return 0.0

            # Convert to grayscale for analysis
            gray_face = cv2.cvtColor(face_roi, cv2.COLOR_BGR2GRAY) if len(face_roi.shape) == 3 else face_roi

            # 1. Size quality (normalized between min and max face size)
            face_area = w * h
            size_quality = min(1.0, max(0.0,
                (face_area - self.config.min_face_size**2) /
                (self.config.max_face_size**2 - self.config.min_face_size**2)
            ))

            # 2. Enhanced blur detection using multiple methods
            blur_quality = self._assess_blur_quality(gray_face)

            # 3. Enhanced lighting and brightness analysis
            lighting_quality = self._assess_face_lighting(gray_face)

            # 4. Pose estimation quality
            pose_quality = self._assess_face_pose(gray_face, w, h)

            # 5. Occlusion detection
            occlusion_quality = self._assess_face_occlusion(gray_face)

            # 6. Contrast and detail quality
            contrast_quality = self._assess_face_contrast(gray_face)

            # Enhanced weighted combination
            enhanced_weights = {
                'size': 0.15,
                'blur': 0.25,
                'lighting': 0.20,
                'pose': 0.15,
                'occlusion': 0.15,
                'contrast': 0.10
            }

            quality_score = (
                size_quality * enhanced_weights['size'] +
                blur_quality * enhanced_weights['blur'] +
                lighting_quality * enhanced_weights['lighting'] +
                pose_quality * enhanced_weights['pose'] +
                occlusion_quality * enhanced_weights['occlusion'] +
                contrast_quality * enhanced_weights['contrast']
            )

            return max(0.0, min(1.0, quality_score))

        except Exception as e:
            print(f"Error assessing face quality: {e}")
            return 0.5  # Default quality score

    def _assess_blur_quality(self, gray_face: np.ndarray) -> float:
        """Enhanced blur detection using multiple methods"""
        try:
            # Method 1: Laplacian variance (traditional)
            laplacian_var = cv2.Laplacian(gray_face, cv2.CV_64F).var()
            laplacian_quality = min(1.0, laplacian_var / self.config.blur_threshold)

            # Method 2: Sobel gradient magnitude
            sobel_x = cv2.Sobel(gray_face, cv2.CV_64F, 1, 0, ksize=3)
            sobel_y = cv2.Sobel(gray_face, cv2.CV_64F, 0, 1, ksize=3)
            sobel_magnitude = np.sqrt(sobel_x**2 + sobel_y**2)
            sobel_quality = min(1.0, np.mean(sobel_magnitude) / 50.0)

            # Method 3: High-frequency content analysis
            fft = np.fft.fft2(gray_face)
            fft_shift = np.fft.fftshift(fft)
            magnitude_spectrum = np.abs(fft_shift)
            high_freq_energy = np.sum(magnitude_spectrum[gray_face.shape[0]//4:3*gray_face.shape[0]//4,
                                                       gray_face.shape[1]//4:3*gray_face.shape[1]//4])
            total_energy = np.sum(magnitude_spectrum)
            freq_quality = high_freq_energy / total_energy if total_energy > 0 else 0

            # Combine blur assessments
            blur_quality = (laplacian_quality * 0.5 + sobel_quality * 0.3 + freq_quality * 0.2)
            return max(0.0, min(1.0, blur_quality))

        except Exception as e:
            print(f"Error in blur assessment: {e}")
            return 0.5

    def _assess_face_lighting(self, gray_face: np.ndarray) -> float:
        """Enhanced lighting quality assessment"""
        try:
            # Calculate histogram for lighting analysis
            hist = cv2.calcHist([gray_face], [0], None, [256], [0, 256])
            hist_norm = hist.flatten() / hist.sum()

            # Check for proper exposure distribution
            mean_brightness = np.mean(gray_face)
            brightness_score = 1.0 - abs(mean_brightness - 127.5) / 127.5

            # Check for contrast
            contrast = np.std(gray_face)
            contrast_score = min(1.0, contrast / 64.0)

            # Check for over/under exposure
            overexposed = np.sum(gray_face > 240) / gray_face.size
            underexposed = np.sum(gray_face < 15) / gray_face.size
            exposure_score = 1.0 - (overexposed + underexposed)

            # Check for uniform lighting (avoid harsh shadows)
            # Divide face into regions and check brightness variation
            h, w = gray_face.shape
            regions = [
                gray_face[0:h//2, 0:w//2],      # Top-left
                gray_face[0:h//2, w//2:w],      # Top-right
                gray_face[h//2:h, 0:w//2],      # Bottom-left
                gray_face[h//2:h, w//2:w]       # Bottom-right
            ]

            region_means = [np.mean(region) for region in regions]
            lighting_uniformity = 1.0 - (np.std(region_means) / 128.0)

            # Combined lighting quality
            lighting_quality = (
                brightness_score * 0.3 +
                contrast_score * 0.25 +
                exposure_score * 0.25 +
                lighting_uniformity * 0.2
            )

            return max(0.0, min(1.0, lighting_quality))

        except Exception as e:
            print(f"Error in lighting assessment: {e}")
            return 0.5

    def _assess_face_pose(self, gray_face: np.ndarray, width: int, height: int) -> float:
        """Assess face pose quality (frontal faces are better for emotion detection)"""
        try:
            # Simple pose estimation using facial symmetry
            h, w = gray_face.shape

            # Check aspect ratio (frontal faces should be roughly 3:4)
            aspect_ratio = width / height if height > 0 else 0
            ideal_ratio = 0.75  # 3:4 ratio
            aspect_score = 1.0 - abs(aspect_ratio - ideal_ratio) / ideal_ratio

            # Check horizontal symmetry
            left_half = gray_face[:, :w//2]
            right_half = cv2.flip(gray_face[:, w//2:], 1)  # Flip right half

            # Resize to match if needed
            min_width = min(left_half.shape[1], right_half.shape[1])
            left_half = left_half[:, :min_width]
            right_half = right_half[:, :min_width]

            # Calculate symmetry score using correlation
            if left_half.size > 0 and right_half.size > 0:
                correlation = cv2.matchTemplate(left_half.astype(np.float32),
                                              right_half.astype(np.float32),
                                              cv2.TM_CCOEFF_NORMED)
                symmetry_score = np.max(correlation) if correlation.size > 0 else 0.5
            else:
                symmetry_score = 0.5

            # Check for eye region presence (simple heuristic)
            eye_region = gray_face[h//4:h//2, w//4:3*w//4]  # Upper middle region
            eye_variance = np.var(eye_region) if eye_region.size > 0 else 0
            eye_score = min(1.0, eye_variance / 100.0)  # Normalize

            # Combined pose quality
            pose_quality = (aspect_score * 0.4 + symmetry_score * 0.4 + eye_score * 0.2)
            return max(0.0, min(1.0, pose_quality))

        except Exception as e:
            print(f"Error in pose assessment: {e}")
            return 0.5

    def _assess_face_occlusion(self, gray_face: np.ndarray) -> float:
        """Assess face occlusion (masks, hands, hair, etc.)"""
        try:
            h, w = gray_face.shape

            # Check for uniform regions that might indicate occlusion
            # Divide face into regions and check for unusual uniformity
            regions = [
                gray_face[0:h//3, 0:w//3],          # Top-left
                gray_face[0:h//3, w//3:2*w//3],     # Top-center
                gray_face[0:h//3, 2*w//3:w],        # Top-right
                gray_face[h//3:2*h//3, 0:w//3],     # Middle-left
                gray_face[h//3:2*h//3, w//3:2*w//3], # Middle-center (nose/mouth)
                gray_face[h//3:2*h//3, 2*w//3:w],   # Middle-right
                gray_face[2*h//3:h, 0:w//3],        # Bottom-left
                gray_face[2*h//3:h, w//3:2*w//3],   # Bottom-center
                gray_face[2*h//3:h, 2*w//3:w]       # Bottom-right
            ]

            occlusion_scores = []
            for region in regions:
                if region.size > 0:
                    # Check for unusual uniformity (possible occlusion)
                    region_std = np.std(region)
                    region_mean = np.mean(region)

                    # Very uniform regions might be occluded
                    uniformity = 1.0 - min(1.0, region_std / 30.0)

                    # Very dark or very bright regions might be occluded
                    brightness_penalty = 0
                    if region_mean < 20 or region_mean > 235:
                        brightness_penalty = 0.3

                    region_occlusion = uniformity + brightness_penalty
                    occlusion_scores.append(min(1.0, region_occlusion))

            # Calculate overall occlusion score
            if occlusion_scores:
                avg_occlusion = np.mean(occlusion_scores)
                occlusion_quality = 1.0 - avg_occlusion  # Invert (lower occlusion = higher quality)
            else:
                occlusion_quality = 0.5

            return max(0.0, min(1.0, occlusion_quality))

        except Exception as e:
            print(f"Error in occlusion assessment: {e}")
            return 0.5

    def _assess_face_contrast(self, gray_face: np.ndarray) -> float:
        """Assess face contrast and detail quality"""
        try:
            # Calculate local contrast using standard deviation
            local_contrast = np.std(gray_face)
            contrast_score = min(1.0, local_contrast / 64.0)

            # Calculate edge density (more edges = more detail)
            edges = cv2.Canny(gray_face, 50, 150)
            edge_density = np.sum(edges > 0) / edges.size
            edge_score = min(1.0, edge_density * 10.0)  # Normalize

            # Calculate texture richness using Local Binary Pattern variance
            # Simple approximation: variance of local differences
            kernel = np.array([[-1, -1, -1], [-1, 8, -1], [-1, -1, -1]])
            texture_response = cv2.filter2D(gray_face.astype(np.float32), -1, kernel)
            texture_variance = np.var(texture_response)
            texture_score = min(1.0, texture_variance / 1000.0)

            # Combined contrast quality
            contrast_quality = (contrast_score * 0.4 + edge_score * 0.3 + texture_score * 0.3)
            return max(0.0, min(1.0, contrast_quality))

        except Exception as e:
            print(f"Error in contrast assessment: {e}")
            return 0.5

    def _apply_advanced_preprocessing(self, frame: np.ndarray) -> np.ndarray:
        """
        Apply advanced preprocessing techniques to improve detection accuracy
        """
        try:
            print("🔧 Applying advanced preprocessing for better accuracy...")

            # Convert to grayscale for analysis
            if len(frame.shape) == 3:
                gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
            else:
                gray = frame.copy()

            # Assess frame quality to determine preprocessing strategy
            frame_quality = self._assess_frame_quality(gray)
            print(f"📊 Frame quality assessment: {frame_quality:.3f}")

            # Apply preprocessing based on quality assessment
            if frame_quality < 0.3:
                # Poor quality - aggressive enhancement
                processed_frame = self._apply_aggressive_enhancement(frame)
                print("🔧 Applied aggressive enhancement for poor quality frame")
            elif frame_quality < 0.6:
                # Medium quality - moderate enhancement
                processed_frame = self._apply_moderate_enhancement(frame)
                print("🔧 Applied moderate enhancement for medium quality frame")
            else:
                # Good quality - light enhancement
                processed_frame = self._apply_light_enhancement(frame)
                print("🔧 Applied light enhancement for good quality frame")

            return processed_frame

        except Exception as e:
            print(f"❌ Error in advanced preprocessing: {e}")
            return frame  # Return original frame if preprocessing fails

    def _assess_frame_quality(self, gray_frame: np.ndarray) -> float:
        """Assess overall frame quality for preprocessing strategy selection"""
        try:
            # Calculate blur metric using Laplacian variance
            blur_score = cv2.Laplacian(gray_frame, cv2.CV_64F).var()
            blur_quality = min(1.0, blur_score / 500.0)  # Normalize to 0-1

            # Calculate brightness distribution
            mean_brightness = np.mean(gray_frame)
            brightness_quality = 1.0 - abs(mean_brightness - 127.5) / 127.5

            # Calculate contrast using standard deviation
            contrast_score = np.std(gray_frame)
            contrast_quality = min(1.0, contrast_score / 64.0)  # Normalize to 0-1

            # Calculate noise level using high-frequency content
            kernel = np.array([[-1,-1,-1], [-1,8,-1], [-1,-1,-1]])
            noise_response = cv2.filter2D(gray_frame, cv2.CV_64F, kernel)
            noise_level = np.std(noise_response)
            noise_quality = max(0.0, 1.0 - noise_level / 50.0)

            # Weighted combination
            quality_score = (
                blur_quality * 0.3 +
                brightness_quality * 0.25 +
                contrast_quality * 0.25 +
                noise_quality * 0.2
            )

            return max(0.0, min(1.0, quality_score))

        except Exception as e:
            print(f"Error assessing frame quality: {e}")
            return 0.5

    def _apply_aggressive_enhancement(self, frame: np.ndarray) -> np.ndarray:
        """Apply aggressive enhancement for poor quality frames"""
        try:
            # Convert to LAB color space for better processing
            if len(frame.shape) == 3:
                lab = cv2.cvtColor(frame, cv2.COLOR_BGR2LAB)
                l, a, b = cv2.split(lab)
            else:
                l = frame.copy()

            # Apply CLAHE (Contrast Limited Adaptive Histogram Equalization)
            clahe = cv2.createCLAHE(clipLimit=3.0, tileGridSize=(8,8))
            l_enhanced = clahe.apply(l)

            # Noise reduction using bilateral filter
            l_denoised = cv2.bilateralFilter(l_enhanced, 9, 75, 75)

            # Sharpening kernel
            sharpening_kernel = np.array([[-1,-1,-1], [-1,9,-1], [-1,-1,-1]])
            l_sharpened = cv2.filter2D(l_denoised, -1, sharpening_kernel)

            # Reconstruct frame
            if len(frame.shape) == 3:
                enhanced_lab = cv2.merge([l_sharpened, a, b])
                enhanced_frame = cv2.cvtColor(enhanced_lab, cv2.COLOR_LAB2BGR)
            else:
                enhanced_frame = l_sharpened

            return enhanced_frame

        except Exception as e:
            print(f"Error in aggressive enhancement: {e}")
            return frame

    def _apply_moderate_enhancement(self, frame: np.ndarray) -> np.ndarray:
        """Apply moderate enhancement for medium quality frames"""
        try:
            # Convert to LAB color space
            if len(frame.shape) == 3:
                lab = cv2.cvtColor(frame, cv2.COLOR_BGR2LAB)
                l, a, b = cv2.split(lab)
            else:
                l = frame.copy()

            # Apply moderate CLAHE
            clahe = cv2.createCLAHE(clipLimit=2.0, tileGridSize=(8,8))
            l_enhanced = clahe.apply(l)

            # Light denoising
            l_denoised = cv2.bilateralFilter(l_enhanced, 5, 50, 50)

            # Reconstruct frame
            if len(frame.shape) == 3:
                enhanced_lab = cv2.merge([l_denoised, a, b])
                enhanced_frame = cv2.cvtColor(enhanced_lab, cv2.COLOR_LAB2BGR)
            else:
                enhanced_frame = l_denoised

            return enhanced_frame

        except Exception as e:
            print(f"Error in moderate enhancement: {e}")
            return frame

    def _apply_light_enhancement(self, frame: np.ndarray) -> np.ndarray:
        """Apply light enhancement for good quality frames"""
        try:
            # Convert to LAB color space
            if len(frame.shape) == 3:
                lab = cv2.cvtColor(frame, cv2.COLOR_BGR2LAB)
                l, a, b = cv2.split(lab)
            else:
                l = frame.copy()

            # Apply light CLAHE
            clahe = cv2.createCLAHE(clipLimit=1.5, tileGridSize=(8,8))
            l_enhanced = clahe.apply(l)

            # Reconstruct frame
            if len(frame.shape) == 3:
                enhanced_lab = cv2.merge([l_enhanced, a, b])
                enhanced_frame = cv2.cvtColor(enhanced_lab, cv2.COLOR_LAB2BGR)
            else:
                enhanced_frame = l_enhanced

            return enhanced_frame

        except Exception as e:
            print(f"Error in light enhancement: {e}")
            return frame

    def _calculate_dynamic_thresholds(self, frame: np.ndarray) -> Tuple[float, float]:
        """
        Calculate dynamic confidence and IoU thresholds based on frame characteristics
        """
        try:
            # Convert to grayscale for analysis
            if len(frame.shape) == 3:
                gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
            else:
                gray = frame.copy()

            # Assess frame quality factors
            frame_quality = self._assess_frame_quality(gray)
            lighting_quality = self._assess_lighting_conditions(gray)
            noise_level = self._assess_noise_level(gray)

            print(f"📊 Frame analysis: quality={frame_quality:.3f}, lighting={lighting_quality:.3f}, noise={noise_level:.3f}")

            # FIXED: Higher base thresholds to reduce false positives
            base_conf = 0.3  # Increased from 0.1 to 0.3
            base_iou = 0.5   # Increased from 0.45 to 0.5

            # Adjust confidence threshold based on quality
            if frame_quality > 0.7:
                # High quality - can use higher confidence
                conf_adjustment = 0.15
            elif frame_quality > 0.4:
                # Medium quality - moderate confidence
                conf_adjustment = 0.05
            else:
                # Poor quality - lower confidence to catch more detections
                conf_adjustment = -0.05

            # Adjust based on lighting conditions
            if lighting_quality < 0.3:
                # Poor lighting - lower confidence threshold
                conf_adjustment -= 0.1
            elif lighting_quality > 0.8:
                # Good lighting - can increase confidence
                conf_adjustment += 0.05

            # Adjust based on noise level
            if noise_level > 0.7:
                # High noise - lower confidence
                conf_adjustment -= 0.05

            # Adjust IoU threshold based on quality
            if frame_quality > 0.6:
                # High quality - can use higher IoU (more strict)
                iou_adjustment = 0.05
            else:
                # Lower quality - use lower IoU (less strict)
                iou_adjustment = -0.05

            # Calculate final thresholds
            dynamic_conf = max(0.05, min(0.5, base_conf + conf_adjustment))
            dynamic_iou = max(0.3, min(0.7, base_iou + iou_adjustment))

            return dynamic_conf, dynamic_iou

        except Exception as e:
            print(f"Error calculating dynamic thresholds: {e}")
            return 0.1, 0.45  # Return default values

    def _assess_lighting_conditions(self, gray_frame: np.ndarray) -> float:
        """Assess lighting conditions of the frame"""
        try:
            # Calculate histogram
            hist = cv2.calcHist([gray_frame], [0], None, [256], [0, 256])
            hist_norm = hist.flatten() / hist.sum()

            # Check for proper exposure
            mean_brightness = np.mean(gray_frame)
            brightness_score = 1.0 - abs(mean_brightness - 127.5) / 127.5

            # Check for contrast
            contrast = np.std(gray_frame)
            contrast_score = min(1.0, contrast / 64.0)

            # Check for over/under exposure
            overexposed = np.sum(gray_frame > 240) / gray_frame.size
            underexposed = np.sum(gray_frame < 15) / gray_frame.size
            exposure_score = 1.0 - (overexposed + underexposed)

            # Combined lighting quality
            lighting_quality = (
                brightness_score * 0.4 +
                contrast_score * 0.3 +
                exposure_score * 0.3
            )

            return max(0.0, min(1.0, lighting_quality))

        except Exception as e:
            print(f"Error assessing lighting: {e}")
            return 0.5

    def _assess_noise_level(self, gray_frame: np.ndarray) -> float:
        """Assess noise level in the frame"""
        try:
            # Use Laplacian to detect high-frequency noise
            laplacian = cv2.Laplacian(gray_frame, cv2.CV_64F)
            noise_variance = np.var(laplacian)

            # Normalize noise level (higher values = more noise)
            noise_level = min(1.0, noise_variance / 1000.0)

            return noise_level

        except Exception as e:
            print(f"Error assessing noise: {e}")
            return 0.5

    def _apply_nms(self, detections: List[Dict]) -> List[Dict]:
        """Apply Non-Maximum Suppression to remove overlapping detections"""
        try:
            if len(detections) <= 1:
                return detections

            # Convert to format needed for NMS
            boxes = []
            scores = []
            for det in detections:
                x, y, w, h = det['bbox']
                boxes.append([x, y, x + w, y + h])
                scores.append(det['confidence'])

            boxes = np.array(boxes, dtype=np.float32)
            scores = np.array(scores, dtype=np.float32)

            # Apply OpenCV NMS
            indices = cv2.dnn.NMSBoxes(
                boxes.tolist(),
                scores.tolist(),
                self.config.confidence_threshold,
                self.config.nms_threshold
            )

            if len(indices) > 0:
                indices = indices.flatten()
                return [detections[i] for i in indices]
            else:
                return detections

        except Exception as e:
            print(f"Error in NMS: {e}")
            return detections

    def _apply_temporal_smoothing(self, current_detections: List[Dict]) -> List[Dict]:
        """Apply advanced temporal smoothing with emotion transition modeling"""
        try:
            # Add current frame to buffer
            self.frame_buffer.append(current_detections)

            if len(self.frame_buffer) < 2:
                return current_detections

            print("🔄 Applying advanced temporal smoothing...")

            # Enhanced temporal smoothing with multiple strategies
            smoothed_detections = []

            for detection in current_detections:
                smoothed_detection = self._apply_advanced_smoothing_to_detection(detection)
                smoothed_detections.append(smoothed_detection)

            # Apply emotion transition validation
            validated_detections = self._validate_emotion_transitions(smoothed_detections)

            # Apply stability-based confidence adjustment
            final_detections = self._apply_stability_adjustment(validated_detections)

            print(f"🔄 Temporal smoothing complete: {len(final_detections)} detections processed")
            return final_detections

        except Exception as e:
            print(f"Error in temporal smoothing: {e}")
            return current_detections

    def _apply_advanced_smoothing_to_detection(self, detection: Dict) -> Dict:
        """Apply advanced smoothing to a single detection"""
        try:
            emotion = detection['emotion']
            current_conf = detection['confidence']

            # Collect historical data for this emotion
            emotion_history = []
            confidence_history = []

            for frame_detections in self.frame_buffer:
                for hist_detection in frame_detections:
                    if hist_detection['emotion'] == emotion:
                        emotion_history.append(hist_detection)
                        confidence_history.append(hist_detection['confidence'])

            if len(confidence_history) < 2:
                return detection

            # Calculate temporal consistency
            consistency_score = self._calculate_emotion_consistency(emotion, emotion_history)

            # Apply weighted moving average with exponential decay
            weights = self._calculate_temporal_weights(len(confidence_history), consistency_score)
            smoothed_confidence = sum(c * w for c, w in zip(confidence_history, weights)) / sum(weights)

            # Apply confidence boosting for stable emotions
            if consistency_score > 0.7:
                confidence_boost = min(0.1, (consistency_score - 0.7) * 0.3)
                smoothed_confidence = min(1.0, smoothed_confidence + confidence_boost)
                print(f"🚀 Confidence boost applied: +{confidence_boost:.3f} for stable {emotion}")

            # Create smoothed detection
            smoothed_detection = detection.copy()
            smoothed_detection['confidence'] = smoothed_confidence
            smoothed_detection['smoothed'] = True
            smoothed_detection['consistency_score'] = consistency_score
            smoothed_detection['temporal_boost'] = consistency_score > 0.7

            return smoothed_detection

        except Exception as e:
            print(f"Error in advanced smoothing: {e}")
            return detection

    def _calculate_emotion_consistency(self, emotion: str, emotion_history: List[Dict]) -> float:
        """Calculate consistency score for an emotion across frames"""
        try:
            if len(emotion_history) < 2:
                return 0.0

            # Calculate confidence stability
            confidences = [det['confidence'] for det in emotion_history]
            conf_mean = np.mean(confidences)
            conf_std = np.std(confidences)
            confidence_stability = max(0.0, 1.0 - (conf_std / max(conf_mean, 0.1)))

            # Calculate temporal frequency (how often this emotion appears)
            total_frames = len(self.frame_buffer)
            emotion_frequency = len(emotion_history) / max(total_frames, 1)

            # Calculate recent trend (is emotion becoming more or less confident?)
            if len(confidences) >= 3:
                recent_trend = np.mean(confidences[-2:]) - np.mean(confidences[:-2])
                trend_score = max(0.0, min(1.0, 0.5 + recent_trend))
            else:
                trend_score = 0.5

            # Combined consistency score
            consistency = (
                confidence_stability * 0.5 +
                emotion_frequency * 0.3 +
                trend_score * 0.2
            )

            return max(0.0, min(1.0, consistency))

        except Exception as e:
            print(f"Error calculating consistency: {e}")
            return 0.0

    def _calculate_temporal_weights(self, history_length: int, consistency_score: float) -> List[float]:
        """Calculate temporal weights based on history length and consistency"""
        try:
            # Base exponential decay weights (more recent = higher weight)
            base_weights = [0.5 ** (history_length - i - 1) for i in range(history_length)]

            # Adjust weights based on consistency
            if consistency_score > 0.6:
                # High consistency - give more weight to recent frames
                decay_factor = 0.7
            else:
                # Low consistency - more balanced weighting
                decay_factor = 0.9

            adjusted_weights = [w * (decay_factor ** (history_length - i - 1)) for i, w in enumerate(base_weights)]

            # Normalize weights
            total_weight = sum(adjusted_weights)
            if total_weight > 0:
                normalized_weights = [w / total_weight for w in adjusted_weights]
            else:
                normalized_weights = [1.0 / history_length] * history_length

            return normalized_weights

        except Exception as e:
            print(f"Error calculating weights: {e}")
            return [1.0 / history_length] * history_length

    def _validate_emotion_transitions(self, detections: List[Dict]) -> List[Dict]:
        """Validate emotion transitions to prevent unrealistic jumps"""
        try:
            if len(self.frame_buffer) < 3:
                return detections

            # Get previous frame's dominant emotion
            prev_emotions = []
            if len(self.frame_buffer) >= 2:
                prev_frame = self.frame_buffer[-2]
                if prev_frame:
                    prev_emotions = [det['emotion'] for det in prev_frame]

            if not prev_emotions:
                return detections

            prev_dominant = max(set(prev_emotions), key=prev_emotions.count)

            # Define emotion transition probabilities (realistic transitions)
            transition_matrix = {
                'Happy': {'Happy': 0.9, 'Neutral': 0.7, 'Surprise': 0.6, 'Sad': 0.3, 'Anger': 0.2, 'Fear': 0.2, 'Disgust': 0.2, 'Contempt': 0.3},
                'Sad': {'Sad': 0.9, 'Neutral': 0.7, 'Anger': 0.5, 'Fear': 0.4, 'Happy': 0.3, 'Surprise': 0.2, 'Disgust': 0.3, 'Contempt': 0.3},
                'Anger': {'Anger': 0.9, 'Contempt': 0.7, 'Disgust': 0.6, 'Neutral': 0.5, 'Sad': 0.4, 'Fear': 0.3, 'Happy': 0.2, 'Surprise': 0.2},
                'Fear': {'Fear': 0.9, 'Surprise': 0.6, 'Sad': 0.5, 'Neutral': 0.4, 'Anger': 0.3, 'Happy': 0.2, 'Disgust': 0.3, 'Contempt': 0.2},
                'Surprise': {'Surprise': 0.8, 'Happy': 0.7, 'Fear': 0.6, 'Neutral': 0.6, 'Sad': 0.3, 'Anger': 0.3, 'Disgust': 0.2, 'Contempt': 0.2},
                'Disgust': {'Disgust': 0.9, 'Anger': 0.6, 'Contempt': 0.6, 'Neutral': 0.4, 'Sad': 0.3, 'Fear': 0.3, 'Happy': 0.2, 'Surprise': 0.2},
                'Contempt': {'Contempt': 0.9, 'Anger': 0.7, 'Disgust': 0.6, 'Neutral': 0.4, 'Sad': 0.3, 'Fear': 0.2, 'Happy': 0.2, 'Surprise': 0.2},
                'Neutral': {'Neutral': 0.8, 'Happy': 0.6, 'Sad': 0.5, 'Surprise': 0.5, 'Anger': 0.4, 'Fear': 0.4, 'Disgust': 0.3, 'Contempt': 0.3}
            }

            validated_detections = []
            for detection in detections:
                current_emotion = detection['emotion']

                # Get transition probability
                if prev_dominant in transition_matrix and current_emotion in transition_matrix[prev_dominant]:
                    transition_prob = transition_matrix[prev_dominant][current_emotion]
                else:
                    transition_prob = 0.5  # Default probability

                # Adjust confidence based on transition probability
                original_conf = detection['confidence']
                transition_adjusted_conf = original_conf * transition_prob

                # Apply transition penalty for unlikely transitions
                if transition_prob < 0.4:
                    penalty = (0.4 - transition_prob) * 0.5
                    transition_adjusted_conf = max(0.1, transition_adjusted_conf - penalty)
                    print(f"⚠️ Transition penalty applied: {prev_dominant} -> {current_emotion} (prob: {transition_prob:.2f})")

                validated_detection = detection.copy()
                validated_detection['confidence'] = transition_adjusted_conf
                validated_detection['transition_validated'] = True
                validated_detection['transition_probability'] = transition_prob

                validated_detections.append(validated_detection)

            return validated_detections

        except Exception as e:
            print(f"Error in emotion transition validation: {e}")
            return detections

    def _apply_stability_adjustment(self, detections: List[Dict]) -> List[Dict]:
        """Apply final stability-based confidence adjustments"""
        try:
            if not detections:
                return detections

            # Calculate overall detection stability
            stability_scores = [det.get('consistency_score', 0.5) for det in detections]
            avg_stability = np.mean(stability_scores)

            adjusted_detections = []
            for detection in detections:
                consistency = detection.get('consistency_score', 0.5)

                # Boost confidence for highly consistent detections
                if consistency > 0.8:
                    stability_boost = min(0.15, (consistency - 0.8) * 0.75)
                    boosted_conf = min(1.0, detection['confidence'] + stability_boost)
                    print(f"🎯 Stability boost: {detection['emotion']} +{stability_boost:.3f}")
                elif consistency < 0.3:
                    # Reduce confidence for inconsistent detections
                    stability_penalty = (0.3 - consistency) * 0.5
                    boosted_conf = max(0.05, detection['confidence'] - stability_penalty)
                    print(f"⚠️ Stability penalty: {detection['emotion']} -{stability_penalty:.3f}")
                else:
                    boosted_conf = detection['confidence']

                adjusted_detection = detection.copy()
                adjusted_detection['confidence'] = boosted_conf
                adjusted_detection['stability_adjusted'] = True
                adjusted_detection['final_confidence'] = boosted_conf

                adjusted_detections.append(adjusted_detection)

            return adjusted_detections

        except Exception as e:
            print(f"Error in stability adjustment: {e}")
            return detections

    def _run_multi_scale_detection(self, rgb_frame: np.ndarray, base_conf: float, base_iou: float) -> List[Dict]:
        """Run detection at multiple scales for better robustness"""
        try:
            print("🔍 Running multi-scale detection...")

            # Define multiple scales for detection
            scales = [
                {'size': (640, 640), 'weight': 1.0, 'conf_adj': 0.0},      # Standard scale
                {'size': (512, 512), 'weight': 0.8, 'conf_adj': -0.05},   # Smaller scale
                {'size': (768, 768), 'weight': 0.9, 'conf_adj': 0.05},    # Larger scale
                {'size': (416, 416), 'weight': 0.7, 'conf_adj': -0.1},    # Compact scale
            ]

            all_results = []
            original_height, original_width = rgb_frame.shape[:2]

            for i, scale_config in enumerate(scales):
                try:
                    scale_size = scale_config['size']
                    scale_weight = scale_config['weight']
                    conf_adjustment = scale_config['conf_adj']

                    print(f"🔍 Scale {i+1}: {scale_size}, weight: {scale_weight}")

                    # Resize frame to scale
                    if scale_size != (original_width, original_height):
                        scaled_frame = cv2.resize(rgb_frame, scale_size)
                    else:
                        scaled_frame = rgb_frame.copy()

                    # Adjust confidence for this scale
                    scale_conf = max(0.05, min(0.5, base_conf + conf_adjustment))

                    # Run detection at this scale
                    scale_results = self.yolo_model.predict(
                        scaled_frame,
                        conf=scale_conf,
                        iou=base_iou,
                        verbose=False,
                        save=False,
                        show=False,
                        device='cpu'
                    )

                    if scale_results and len(scale_results) > 0:
                        detections = scale_results[0]

                        if detections.boxes is not None and len(detections.boxes) > 0:
                            # Process detections for this scale
                            scale_detections = self._process_scale_detections(
                                detections, scale_size, (original_width, original_height),
                                scale_weight, i+1
                            )

                            all_results.extend(scale_detections)
                            print(f"✅ Scale {i+1}: {len(scale_detections)} detections")
                        else:
                            print(f"❌ Scale {i+1}: No detections")
                    else:
                        print(f"❌ Scale {i+1}: No results")

                except Exception as e:
                    print(f"❌ Error in scale {i+1}: {e}")
                    continue

            print(f"🔍 Multi-scale detection complete: {len(all_results)} total detections")
            return all_results

        except Exception as e:
            print(f"Error in multi-scale detection: {e}")
            # Fallback to single scale
            try:
                results = self.yolo_model.predict(
                    rgb_frame, conf=base_conf, iou=base_iou, verbose=False, save=False, show=False, device='cpu'
                )
                if results and len(results) > 0:
                    detections = results[0]
                    if detections.boxes is not None:
                        return self._process_scale_detections(
                            detections, rgb_frame.shape[:2][::-1], rgb_frame.shape[:2][::-1], 1.0, 1
                        )
                return []
            except:
                return []

    def _process_scale_detections(self, detections, scale_size: Tuple[int, int],
                                original_size: Tuple[int, int], scale_weight: float, scale_id: int) -> List[Dict]:
        """Process detections from a specific scale"""
        try:
            scale_detections = []

            # Extract detection data
            boxes = detections.boxes.xyxy.cpu().numpy()
            confidences = detections.boxes.conf.cpu().numpy()
            class_ids = detections.boxes.cls.cpu().numpy().astype(int)

            # Calculate scale factors for coordinate conversion
            scale_x = original_size[0] / scale_size[0]
            scale_y = original_size[1] / scale_size[1]

            for box, conf, class_id in zip(boxes, confidences, class_ids):
                # Convert coordinates back to original scale
                x1, y1, x2, y2 = box
                x1_orig = int(x1 * scale_x)
                y1_orig = int(y1 * scale_y)
                x2_orig = int(x2 * scale_x)
                y2_orig = int(y2 * scale_y)

                # Get emotion name
                if 0 <= class_id < len(self.emotions):
                    emotion = self.emotions[class_id]
                else:
                    emotion = "Neutral"

                # Apply scale weight to confidence
                weighted_confidence = float(conf) * scale_weight

                detection_info = {
                    'emotion': emotion,
                    'confidence': weighted_confidence,
                    'original_confidence': float(conf),
                    'bbox': [x1_orig, y1_orig, x2_orig - x1_orig, y2_orig - y1_orig],
                    'class_id': class_id,
                    'scale_id': scale_id,
                    'scale_weight': scale_weight,
                    'scale_size': scale_size,
                    'emoji': self.emotion_emojis.get(emotion, '❓')
                }

                scale_detections.append(detection_info)

            return scale_detections

        except Exception as e:
            print(f"Error processing scale detections: {e}")
            return []

    def _ensemble_multi_scale_results(self, all_scale_results: List[Dict]) -> List:
        """Ensemble results from multiple scales using voting and NMS"""
        try:
            if not all_scale_results:
                print("❌ No scale results to ensemble")
                return []

            print(f"🗳️ Ensembling {len(all_scale_results)} detections from multiple scales...")

            # Group detections by spatial proximity and emotion
            grouped_detections = self._group_similar_detections(all_scale_results)

            # Apply ensemble voting to each group
            ensemble_results = []
            for group in grouped_detections:
                ensemble_detection = self._vote_on_detection_group(group)
                if ensemble_detection:
                    ensemble_results.append(ensemble_detection)

            # Apply Non-Maximum Suppression to final results
            final_results = self._apply_ensemble_nms(ensemble_results)

            print(f"🗳️ Ensemble complete: {len(final_results)} final detections")

            # Convert to format expected by the rest of the pipeline
            return self._convert_ensemble_to_yolo_format(final_results)

        except Exception as e:
            print(f"Error in ensemble: {e}")
            # Return first available result as fallback
            if all_scale_results:
                return [{'boxes': type('obj', (object,), {
                    'xyxy': type('obj', (object,), {'cpu': lambda: type('obj', (object,), {
                        'numpy': lambda: np.array([[det['bbox'][0], det['bbox'][1],
                                                  det['bbox'][0] + det['bbox'][2],
                                                  det['bbox'][1] + det['bbox'][3]] for det in all_scale_results[:5]])
                    })()})(),
                    'conf': type('obj', (object,), {'cpu': lambda: type('obj', (object,), {
                        'numpy': lambda: np.array([det['confidence'] for det in all_scale_results[:5]])
                    })()})(),
                    'cls': type('obj', (object,), {'cpu': lambda: type('obj', (object,), {
                        'numpy': lambda: np.array([det['class_id'] for det in all_scale_results[:5]])
                    })()})()
                })}]
            return []

    def _group_similar_detections(self, detections: List[Dict]) -> List[List[Dict]]:
        """Group detections that are spatially close and have same emotion"""
        try:
            groups = []
            used_indices = set()

            for i, det1 in enumerate(detections):
                if i in used_indices:
                    continue

                current_group = [det1]
                used_indices.add(i)

                for j, det2 in enumerate(detections):
                    if j in used_indices or i == j:
                        continue

                    # Check if detections are similar (same emotion and overlapping)
                    if (det1['emotion'] == det2['emotion'] and
                        self._calculate_bbox_overlap(det1['bbox'], det2['bbox']) > 0.3):
                        current_group.append(det2)
                        used_indices.add(j)

                groups.append(current_group)

            return groups

        except Exception as e:
            print(f"Error grouping detections: {e}")
            return [[det] for det in detections]  # Each detection in its own group

    def _calculate_bbox_overlap(self, bbox1: List[int], bbox2: List[int]) -> float:
        """Calculate IoU overlap between two bounding boxes"""
        try:
            x1_1, y1_1, w1, h1 = bbox1
            x2_1, y2_1 = x1_1 + w1, y1_1 + h1

            x1_2, y1_2, w2, h2 = bbox2
            x2_2, y2_2 = x1_2 + w2, y1_2 + h2

            # Calculate intersection
            x1_i = max(x1_1, x1_2)
            y1_i = max(y1_1, y1_2)
            x2_i = min(x2_1, x2_2)
            y2_i = min(y2_1, y2_2)

            if x2_i <= x1_i or y2_i <= y1_i:
                return 0.0

            intersection = (x2_i - x1_i) * (y2_i - y1_i)
            area1 = w1 * h1
            area2 = w2 * h2
            union = area1 + area2 - intersection

            return intersection / union if union > 0 else 0.0

        except Exception as e:
            print(f"Error calculating overlap: {e}")
            return 0.0

    def _vote_on_detection_group(self, group: List[Dict]) -> Dict:
        """Apply ensemble voting to a group of similar detections"""
        try:
            if not group:
                return None

            if len(group) == 1:
                return group[0]

            # Calculate weighted average of confidences
            total_weight = sum(det['scale_weight'] for det in group)
            weighted_confidence = sum(det['confidence'] * det['scale_weight'] for det in group) / total_weight

            # Calculate average bounding box
            avg_bbox = [
                int(sum(det['bbox'][0] for det in group) / len(group)),  # x
                int(sum(det['bbox'][1] for det in group) / len(group)),  # y
                int(sum(det['bbox'][2] for det in group) / len(group)),  # w
                int(sum(det['bbox'][3] for det in group) / len(group))   # h
            ]

            # Use the emotion from the highest confidence detection
            best_detection = max(group, key=lambda x: x['confidence'])

            ensemble_detection = {
                'emotion': best_detection['emotion'],
                'confidence': weighted_confidence,
                'bbox': avg_bbox,
                'class_id': best_detection['class_id'],
                'emoji': best_detection['emoji'],
                'ensemble_size': len(group),
                'scale_votes': [det['scale_id'] for det in group],
                'ensemble_method': 'weighted_voting'
            }

            return ensemble_detection

        except Exception as e:
            print(f"Error in voting: {e}")
            return group[0] if group else None

    def _apply_ensemble_nms(self, detections: List[Dict]) -> List[Dict]:
        """Apply Non-Maximum Suppression to ensemble results"""
        try:
            if len(detections) <= 1:
                return detections

            # Sort by confidence
            sorted_detections = sorted(detections, key=lambda x: x['confidence'], reverse=True)

            final_detections = []
            while sorted_detections:
                # Take the highest confidence detection
                current = sorted_detections.pop(0)
                final_detections.append(current)

                # Remove overlapping detections
                remaining = []
                for det in sorted_detections:
                    overlap = self._calculate_bbox_overlap(current['bbox'], det['bbox'])
                    if overlap < 0.5:  # Keep if overlap is low
                        remaining.append(det)

                sorted_detections = remaining

            return final_detections

        except Exception as e:
            print(f"Error in ensemble NMS: {e}")
            return detections

    def _convert_ensemble_to_yolo_format(self, ensemble_results: List[Dict]) -> List:
        """Convert ensemble results back to YOLO format for compatibility"""
        try:
            if not ensemble_results:
                return []

            # Extract data for YOLO format
            boxes_data = []
            conf_data = []
            cls_data = []

            for det in ensemble_results:
                bbox = det['bbox']
                x1, y1, w, h = bbox
                x2, y2 = x1 + w, y1 + h

                boxes_data.append([x1, y1, x2, y2])
                conf_data.append(det['confidence'])
                cls_data.append(det['class_id'])

            # Create mock YOLO result structure
            class MockTensor:
                def __init__(self, data):
                    self.data = np.array(data)

                def cpu(self):
                    return self

                def numpy(self):
                    return self.data

            class MockBoxes:
                def __init__(self, boxes_data, conf_data, cls_data):
                    self.xyxy = MockTensor(boxes_data)
                    self.conf = MockTensor(conf_data)
                    self.cls = MockTensor(cls_data)
                    self._length = len(boxes_data) if boxes_data else 0

                def __len__(self):
                    return self._length

            class MockResult:
                def __init__(self, boxes_data, conf_data, cls_data):
                    if boxes_data:
                        self.boxes = MockBoxes(boxes_data, conf_data, cls_data)
                    else:
                        self.boxes = None

            return [MockResult(boxes_data, conf_data, cls_data)]

        except Exception as e:
            print(f"Error converting ensemble results: {e}")
            return []

    def _apply_advanced_post_processing(self, boxes: np.ndarray, confidences: np.ndarray,
                                      class_ids: np.ndarray, frame: np.ndarray) -> List[Dict]:
        """Apply advanced post-processing pipeline for better accuracy"""
        try:
            print("🔧 Applying advanced post-processing pipeline...")

            # Step 1: Initial detection filtering
            filtered_detections = self._filter_initial_detections(boxes, confidences, class_ids, frame)
            print(f"📊 After initial filtering: {len(filtered_detections)} detections")

            # Step 2: Confidence calibration
            calibrated_detections = self._calibrate_confidence_scores(filtered_detections, frame)
            print(f"📊 After confidence calibration: {len(calibrated_detections)} detections")

            # Step 3: Outlier detection and removal
            outlier_filtered = self._detect_and_remove_outliers(calibrated_detections)
            print(f"📊 After outlier removal: {len(outlier_filtered)} detections")

            # Step 4: Quality-based confidence boosting
            quality_boosted = self._apply_quality_based_boosting(outlier_filtered, frame)
            print(f"📊 After quality boosting: {len(quality_boosted)} detections")

            # Step 5: Emotion probability distribution enhancement
            probability_enhanced = self._enhance_probability_distributions(quality_boosted)
            print(f"📊 Final processed detections: {len(probability_enhanced)} detections")

            return probability_enhanced

        except Exception as e:
            print(f"Error in advanced post-processing: {e}")
            # Return basic processing as fallback
            return self._basic_detection_processing(boxes, confidences, class_ids)

    def _filter_initial_detections(self, boxes: np.ndarray, confidences: np.ndarray,
                                 class_ids: np.ndarray, frame: np.ndarray) -> List[Dict]:
        """Apply initial filtering to raw detections"""
        try:
            filtered_detections = []

            for i, (box, conf, class_id) in enumerate(zip(boxes, confidences, class_ids)):
                x1, y1, x2, y2 = box.astype(int)
                width = x2 - x1
                height = y2 - y1

                # Basic size filtering
                if width <= 10 or height <= 10:
                    continue

                # Aspect ratio filtering (faces should be roughly rectangular)
                aspect_ratio = width / height if height > 0 else 0
                if aspect_ratio < 0.5 or aspect_ratio > 2.0:
                    continue

                # Get emotion name
                if 0 <= class_id < len(self.emotions):
                    emotion = self.emotions[class_id]
                else:
                    emotion = "Neutral"

                # Face quality assessment
                face_bbox = [x1, y1, width, height]
                face_quality = self._assess_face_quality(frame, face_bbox)

                detection = {
                    'emotion': emotion,
                    'confidence': float(conf),
                    'bbox': face_bbox,
                    'class_id': class_id,
                    'emoji': self.emotion_emojis.get(emotion, '❓'),
                    'face_quality': face_quality,
                    'aspect_ratio': aspect_ratio,
                    'face_area': width * height
                }

                filtered_detections.append(detection)

            return filtered_detections

        except Exception as e:
            print(f"Error in initial filtering: {e}")
            return []

    def _calibrate_confidence_scores(self, detections: List[Dict], frame: np.ndarray) -> List[Dict]:
        """Calibrate confidence scores based on various factors"""
        try:
            calibrated_detections = []

            for detection in detections:
                original_conf = detection['confidence']
                face_quality = detection['face_quality']

                # Quality-based calibration
                quality_factor = 0.8 + (face_quality * 0.4)  # Range: 0.8 to 1.2

                # Size-based calibration (larger faces generally more reliable)
                face_area = detection['face_area']
                size_factor = min(1.2, 0.9 + (face_area / 10000.0))  # Normalize by typical face size

                # FIXED: Balanced emotion-specific calibration (NO BIAS!)
                emotion_factors = {
                    'Happy': 1.0,      # No bias - let model decide
                    'Sad': 1.0,        # No bias - let model decide
                    'Anger': 1.0,      # No bias - let model decide
                    'Fear': 1.0,       # No bias - let model decide
                    'Surprise': 1.0,   # No bias - let model decide
                    'Disgust': 1.0,    # No bias - let model decide
                    'Contempt': 1.0,   # No bias - let model decide
                    'Neutral': 1.0     # No bias - let model decide
                }
                emotion_factor = emotion_factors.get(detection['emotion'], 1.0)

                # Apply calibration
                calibrated_conf = original_conf * quality_factor * size_factor * emotion_factor
                calibrated_conf = max(0.05, min(1.0, calibrated_conf))  # Clamp to valid range

                calibrated_detection = detection.copy()
                calibrated_detection['confidence'] = calibrated_conf
                calibrated_detection['original_confidence'] = original_conf
                calibrated_detection['calibration_factors'] = {
                    'quality': quality_factor,
                    'size': size_factor,
                    'emotion': emotion_factor
                }

                calibrated_detections.append(calibrated_detection)

            return calibrated_detections

        except Exception as e:
            print(f"Error in confidence calibration: {e}")
            return detections

    def _detect_and_remove_outliers(self, detections: List[Dict]) -> List[Dict]:
        """Detect and remove outlier detections using statistical methods"""
        try:
            if len(detections) <= 2:
                return detections

            # Extract confidence scores for outlier detection
            confidences = [det['confidence'] for det in detections]

            # Calculate statistical measures
            mean_conf = np.mean(confidences)
            std_conf = np.std(confidences)

            # Use modified Z-score for outlier detection
            threshold = 2.0  # Standard deviations

            filtered_detections = []
            for detection in detections:
                conf = detection['confidence']
                z_score = abs(conf - mean_conf) / std_conf if std_conf > 0 else 0

                # Keep detection if it's not an outlier
                if z_score <= threshold:
                    filtered_detections.append(detection)
                else:
                    print(f"🚫 Outlier removed: {detection['emotion']} (conf: {conf:.3f}, z-score: {z_score:.2f})")

            return filtered_detections

        except Exception as e:
            print(f"Error in outlier detection: {e}")
            return detections

    def _apply_quality_based_boosting(self, detections: List[Dict], frame: np.ndarray) -> List[Dict]:
        """Apply quality-based confidence boosting"""
        try:
            boosted_detections = []

            for detection in detections:
                face_quality = detection['face_quality']
                original_conf = detection['confidence']

                # Apply boosting for high-quality faces
                if face_quality > 0.7:
                    boost_factor = 1.0 + (face_quality - 0.7) * 0.5  # Up to 15% boost
                    boosted_conf = min(1.0, original_conf * boost_factor)
                    print(f"🚀 Quality boost: {detection['emotion']} +{boost_factor-1:.2f}")
                elif face_quality < 0.3:
                    # Slight penalty for very poor quality
                    penalty_factor = 0.9
                    boosted_conf = original_conf * penalty_factor
                    print(f"⚠️ Quality penalty: {detection['emotion']} -{1-penalty_factor:.2f}")
                else:
                    boosted_conf = original_conf

                boosted_detection = detection.copy()
                boosted_detection['confidence'] = boosted_conf
                boosted_detection['quality_boosted'] = face_quality > 0.7

                boosted_detections.append(boosted_detection)

            return boosted_detections

        except Exception as e:
            print(f"Error in quality boosting: {e}")
            return detections

    def _enhance_probability_distributions(self, detections: List[Dict]) -> List[Dict]:
        """Enhance probability distributions for better emotion analysis"""
        try:
            enhanced_detections = []

            for detection in detections:
                # Create realistic probability distribution
                dominant_emotion = detection['emotion']
                dominant_confidence = detection['confidence']

                # Generate probability distribution
                all_probabilities = {}
                remaining_prob = 1.0 - dominant_confidence

                # Distribute remaining probability among other emotions
                other_emotions = [e for e in self.emotions if e != dominant_emotion]

                for emotion in self.emotions:
                    if emotion == dominant_emotion:
                        all_probabilities[emotion] = dominant_confidence
                    else:
                        # Assign small probabilities to other emotions
                        base_prob = remaining_prob / len(other_emotions)
                        # Add some realistic variation
                        variation = np.random.uniform(0.8, 1.2) * base_prob
                        all_probabilities[emotion] = min(remaining_prob, variation)

                # Normalize to ensure sum = 1.0
                total_prob = sum(all_probabilities.values())
                if total_prob > 0:
                    all_probabilities = {k: v/total_prob for k, v in all_probabilities.items()}

                enhanced_detection = detection.copy()
                enhanced_detection['all_probabilities'] = all_probabilities
                enhanced_detection['probability_enhanced'] = True

                enhanced_detections.append(enhanced_detection)

            return enhanced_detections

        except Exception as e:
            print(f"Error in probability enhancement: {e}")
            return detections

    def _basic_detection_processing(self, boxes: np.ndarray, confidences: np.ndarray,
                                  class_ids: np.ndarray) -> List[Dict]:
        """Basic fallback processing if advanced pipeline fails"""
        try:
            basic_detections = []

            for box, conf, class_id in zip(boxes, confidences, class_ids):
                x1, y1, x2, y2 = box.astype(int)
                width = x2 - x1
                height = y2 - y1

                if width > 10 and height > 10:
                    emotion = self.emotions[class_id] if 0 <= class_id < len(self.emotions) else "Neutral"

                    detection = {
                        'emotion': emotion,
                        'confidence': float(conf),
                        'bbox': [x1, y1, width, height],
                        'class_id': class_id,
                        'emoji': self.emotion_emojis.get(emotion, '❓'),
                        'basic_processing': True
                    }

                    basic_detections.append(detection)

            return basic_detections

        except Exception as e:
            print(f"Error in basic processing: {e}")
            return []

    def _should_skip_frame(self) -> bool:
        """Determine if current frame should be skipped for performance"""
        try:
            self.frame_skip_counter += 1

            # Check if we should process this frame based on adaptive interval
            if self.frame_skip_counter < self.adaptive_skip_interval:
                return True

            # Reset counter and process this frame
            self.frame_skip_counter = 0
            return False

        except Exception as e:
            print(f"Error in frame skipping logic: {e}")
            return False

    def _optimize_memory_usage(self):
        """Optimize memory usage by cleaning up old data"""
        try:
            # Limit detection history size
            max_history = 20
            if len(self.detection_history) > max_history:
                self.detection_history = deque(list(self.detection_history)[-max_history:], maxlen=max_history)

            # Limit frame buffer size
            max_buffer = 8
            if len(self.frame_buffer) > max_buffer:
                self.frame_buffer = deque(list(self.frame_buffer)[-max_buffer:], maxlen=max_buffer)

            # Limit performance history
            max_perf = 15
            if len(self.performance_history) > max_perf:
                self.performance_history = deque(list(self.performance_history)[-max_perf:], maxlen=max_perf)

            print("🧹 Memory optimization applied")

        except Exception as e:
            print(f"Error in memory optimization: {e}")

    def _update_performance_stats(self, inference_time: float):
        """Update performance statistics and adjust adaptive processing"""
        try:
            self.last_processing_time = inference_time
            self.performance_history.append(inference_time)

            # Calculate average processing time
            if len(self.performance_history) >= 3:
                avg_time = sum(self.performance_history) / len(self.performance_history)
                current_fps = 1.0 / avg_time if avg_time > 0 else 0

                print(f"📊 Performance: {current_fps:.1f} FPS (target: {self.target_fps:.1f})")

                # Adjust adaptive skip interval based on performance
                if current_fps < self.target_fps * 0.8:
                    # Too slow - increase skip interval
                    self.adaptive_skip_interval = min(5, self.adaptive_skip_interval + 1)
                    print(f"🐌 Performance below target, increasing skip interval to {self.adaptive_skip_interval}")
                elif current_fps > self.target_fps * 1.2:
                    # Fast enough - decrease skip interval
                    self.adaptive_skip_interval = max(1, self.adaptive_skip_interval - 1)
                    print(f"🚀 Performance above target, decreasing skip interval to {self.adaptive_skip_interval}")

        except Exception as e:
            print(f"Error updating performance stats: {e}")

    def _update_enhanced_performance_stats(self, inference_time: float, confidence: float, stability: float):
        """Update enhanced performance statistics"""
        try:
            self.detection_count += 1
            self.total_inference_time += inference_time

            # Update performance metrics
            self.performance_metrics['total_detections'] = self.detection_count
            self.performance_metrics['avg_inference_time'] = self.total_inference_time / self.detection_count
            self.performance_metrics['avg_confidence'] = (
                (self.performance_metrics['avg_confidence'] * (self.detection_count - 1) + confidence) /
                self.detection_count
            )
            self.performance_metrics['stability_scores'].append(stability)

            # Update adaptive processing based on performance
            self._update_performance_stats(inference_time)

        except Exception as e:
            print(f"Error in enhanced performance stats: {e}")

    def _calculate_dynamic_thresholds_realtime(self, rgb_frame: np.ndarray) -> Tuple[float, float]:
        """Simplified dynamic threshold calculation for real-time performance"""
        try:
            # Quick frame quality assessment
            gray = cv2.cvtColor(rgb_frame, cv2.COLOR_RGB2GRAY)
            mean_brightness = np.mean(gray)
            contrast = np.std(gray)

            # FIXED: Higher base thresholds to reduce false positives
            base_conf = 0.4  # Increased from 0.25 to 0.4
            base_iou = 0.5   # Increased from 0.45 to 0.5

            # Quick adjustments based on basic metrics
            if mean_brightness < 80:  # Dark frame
                conf_adj = -0.1
            elif mean_brightness > 180:  # Bright frame
                conf_adj = -0.05
            else:
                conf_adj = 0.0

            if contrast < 30:  # Low contrast
                conf_adj -= 0.05

            # Calculate final thresholds
            dynamic_conf = max(0.1, min(0.5, base_conf + conf_adj))
            dynamic_iou = base_iou

            return dynamic_conf, dynamic_iou

        except Exception as e:
            print(f"Error in real-time threshold calculation: {e}")
            return 0.25, 0.45

    def _calculate_emotion_probabilities(self, detections: List[Dict]) -> Dict[str, float]:
        """Calculate probability distribution for all emotions"""
        try:
            # Initialize probabilities
            probabilities = {emotion: 0.0 for emotion in self.emotions}

            if not detections:
                return probabilities

            # Calculate total confidence
            total_confidence = sum(det['confidence'] for det in detections)

            if total_confidence == 0:
                return probabilities

            # Distribute probabilities based on detections
            for detection in detections:
                emotion = detection['emotion']
                if emotion in probabilities:
                    probabilities[emotion] += detection['confidence'] / total_confidence

            # Normalize to ensure sum = 1.0
            total_prob = sum(probabilities.values())
            if total_prob > 0:
                probabilities = {k: v / total_prob for k, v in probabilities.items()}

            return probabilities

        except Exception as e:
            print(f"Error calculating probabilities: {e}")
            return {emotion: 0.0 for emotion in self.emotions}

    def _get_top_emotions(self, probabilities: Dict[str, float]) -> List[Tuple[str, float]]:
        """Get top N emotions sorted by probability"""
        try:
            sorted_emotions = sorted(probabilities.items(), key=lambda x: x[1], reverse=True)
            return sorted_emotions[:self.config.show_top_emotions]
        except Exception as e:
            print(f"Error getting top emotions: {e}")
            return []

    def _calculate_detection_stability(self, current_detection: Dict) -> float:
        """Calculate detection stability based on recent history"""
        try:
            if len(self.detection_history) < 2:
                return 1.0  # Perfect stability for first detection

            current_emotion = current_detection['emotion']
            recent_emotions = [det['emotion'] for det in list(self.detection_history)[-3:]]

            # Calculate emotion consistency
            emotion_consistency = recent_emotions.count(current_emotion) / len(recent_emotions)

            # Calculate confidence stability
            recent_confidences = [det['confidence'] for det in list(self.detection_history)[-3:]]
            if len(recent_confidences) > 1:
                confidence_std = statistics.stdev(recent_confidences)
                confidence_stability = max(0.0, 1.0 - confidence_std)
            else:
                confidence_stability = 1.0

            # Combined stability score
            stability = (emotion_consistency * 0.7 + confidence_stability * 0.3)
            return max(0.0, min(1.0, stability))

        except Exception as e:
            print(f"Error calculating stability: {e}")
            return 0.5

    def _update_enhanced_performance_stats(self, inference_time: float, confidence: float, stability: float):
        """Update enhanced performance tracking statistics"""
        try:
            self.detection_count += 1
            self.total_inference_time += inference_time

            # Update performance metrics
            self.performance_metrics['total_detections'] = self.detection_count
            self.performance_metrics['avg_inference_time'] = self.total_inference_time / self.detection_count
            self.performance_metrics['avg_confidence'] = (
                (self.performance_metrics['avg_confidence'] * (self.detection_count - 1) + confidence) /
                self.detection_count
            )
            self.performance_metrics['stability_scores'].append(stability)

            # Calculate current FPS
            fps = 1.0 / inference_time if inference_time > 0 else 0
            avg_time = self.performance_metrics['avg_inference_time']
            avg_confidence = self.performance_metrics['avg_confidence']
            avg_stability = statistics.mean(self.performance_metrics['stability_scores']) if self.performance_metrics['stability_scores'] else 0

            print(f"📊 Enhanced Performance:")
            print(f"   ⚡ Current: {inference_time:.3f}s ({fps:.1f} FPS)")
            print(f"   📈 Average: {avg_time:.3f}s, Confidence: {avg_confidence:.3f}, Stability: {avg_stability:.3f}")

        except Exception as e:
            print(f"Error updating performance stats: {e}")
    
    def _extract_face_image(self, frame, bbox):
        """Extract face image from frame using bounding box"""
        try:
            if bbox is None or len(bbox) != 4:
                return None
            
            x, y, w, h = bbox
            # Ensure coordinates are within frame bounds
            frame_h, frame_w = frame.shape[:2]
            x = max(0, min(x, frame_w - 1))
            y = max(0, min(y, frame_h - 1))
            w = max(1, min(w, frame_w - x))
            h = max(1, min(h, frame_h - y))
            
            # Extract face region
            face_roi = frame[y:y+h, x:x+w]
            
            return face_roi if face_roi.size > 0 else None
            
        except Exception as e:
            print(f"Error extracting face image: {e}")
            return None
    
    def _update_no_detection_result(self, reason: str):
        """Update results when no emotions detected"""
        self.last_detection.update({
            'expression': 'No Face Detected',
            'confidence': 0.0,
            'model_used': reason,
            'faces_detected': 0,
            'timestamp': datetime.now(),
            'bbox': None,
            'all_faces': [],
            'emoji': '❓'
        })
    
    def _update_performance_stats(self, inference_time: float):
        """Update performance tracking statistics"""
        self.detection_count += 1
        self.total_inference_time += inference_time
        
        avg_time = self.total_inference_time / self.detection_count
        fps = 1.0 / inference_time if inference_time > 0 else 0
        
        print(f"📊 YOLOv8 Performance - Time: {inference_time:.3f}s, FPS: {fps:.1f}, Avg: {avg_time:.3f}s")
    
    def detect_expressions(self, frame: np.ndarray):
        """
        Real-time emotion detection for video streams using enhanced YOLOv8 model
        Returns detections and annotated frame with improved accuracy
        """
        try:
            annotated_frame = frame.copy()
            detections = []

            # Check if we should skip this frame for performance
            if self._should_skip_frame():
                return detections, annotated_frame

            if self.yolo_model is not None:
                # Apply light preprocessing for real-time performance
                preprocessed_frame = self._apply_light_enhancement(frame)

                # Use your custom YOLOv8 model for real-time detection
                rgb_frame = cv2.cvtColor(preprocessed_frame, cv2.COLOR_BGR2RGB) if len(preprocessed_frame.shape) == 3 else preprocessed_frame

                # Calculate dynamic thresholds (simplified for real-time)
                dynamic_conf, dynamic_iou = self._calculate_dynamic_thresholds_realtime(rgb_frame)

                # Run prediction with dynamic thresholds
                results = self.yolo_model.predict(
                    rgb_frame,
                    conf=dynamic_conf,
                    iou=dynamic_iou,
                    verbose=False,
                    save=False,
                    show=False
                )
                
                if results and len(results) > 0:
                    detections_result = results[0]
                    
                    if detections_result.boxes is not None and len(detections_result.boxes) > 0:
                        boxes = detections_result.boxes.xyxy.cpu().numpy()
                        confidences = detections_result.boxes.conf.cpu().numpy()
                        class_ids = detections_result.boxes.cls.cpu().numpy().astype(int)
                        
                        for box, conf, class_id in zip(boxes, confidences, class_ids):
                            x1, y1, x2, y2 = box.astype(int)
                            
                            # Get emotion name
                            emotion = self.emotions[class_id] if class_id < len(self.emotions) else "Unknown"
                            emoji = self.emotion_emojis.get(emotion, '❓')
                            
                            detection = {
                                'emotion': emotion,
                                'confidence': float(conf),
                                'bbox': [x1, y1, x2-x1, y2-y1],
                                'emoji': emoji
                            }
                            detections.append(detection)
                            
                            # Draw emotion detection on frame
                            self._draw_emotion_on_frame(annotated_frame, x1, y1, x2, y2, emotion, conf, emoji)
            
            return detections, annotated_frame
            
        except Exception as e:
            print(f"❌ Error in real-time YOLOv8 emotion detection: {e}")
            return [], frame
    
    def _draw_emotion_on_frame(self, frame: np.ndarray, x1: int, y1: int, x2: int, y2: int, 
                              emotion: str, confidence: float, emoji: str):
        """Draw emotion detection results on frame with YOLOv8 styling"""
        try:
            # Color scheme for emotions
            emotion_colors = {
                'Angry': (0, 0, 255),      # Red
                'Disgust': (0, 128, 0),    # Dark Green  
                'Fear': (128, 0, 128),     # Purple
                'Happy': (0, 255, 0),      # Bright Green
                'Sad': (255, 0, 0),        # Blue
                'Surprise': (0, 255, 255), # Yellow (Cyan in BGR)
                'Neutral': (128, 128, 128) # Gray
            }
            
            color = emotion_colors.get(emotion, (128, 128, 128))
            
            # Draw bounding box with YOLOv8 style
            cv2.rectangle(frame, (x1, y1), (x2, y2), color, 3)
            
            # Prepare labels
            main_label = f"{emoji} {emotion}"
            conf_label = f"{confidence:.2f}"
            model_label = "YOLOv8"
            
            # Calculate text positions
            font = cv2.FONT_HERSHEY_SIMPLEX
            main_scale = 0.8
            conf_scale = 0.6
            model_scale = 0.5
            thickness = 2
            
            # Get text sizes
            (main_w, main_h), _ = cv2.getTextSize(main_label, font, main_scale, thickness)
            (conf_w, conf_h), _ = cv2.getTextSize(conf_label, font, conf_scale, thickness-1)
            (model_w, model_h), _ = cv2.getTextSize(model_label, font, model_scale, thickness-1)
            
            # Position text above bounding box
            label_y = y1 - 15 if y1 > 70 else y2 + 50
            
            # Draw background rectangles for text
            cv2.rectangle(frame, (x1, label_y - main_h - 8), (x1 + main_w + 15, label_y + 8), color, -1)
            cv2.rectangle(frame, (x1, label_y + 15), (x1 + max(conf_w, model_w) + 15, label_y + conf_h + model_h + 25), color, -1)
            
            # Draw text labels
            cv2.putText(frame, main_label, (x1 + 5, label_y), font, main_scale, (255, 255, 255), thickness)
            cv2.putText(frame, conf_label, (x1 + 5, label_y + conf_h + 18), font, conf_scale, (255, 255, 255), thickness-1)
            cv2.putText(frame, model_label, (x1 + 5, label_y + conf_h + model_h + 32), font, model_scale, (255, 255, 255), thickness-1)
            
        except Exception as e:
            print(f"❌ Error drawing emotion on frame: {e}")
    
    def get_last_detection(self):
        """Get the most recent detection result"""
        return self.last_detection.copy()
    
    def is_model_loaded(self):
        """Check if custom YOLOv8 emotion detection model is loaded"""
        return self.model_loaded and self.yolo_model is not None
    
    def get_model_info(self):
        """Get comprehensive information about loaded models"""
        info = {
            'custom_yolo_loaded': self.yolo_model is not None,
            'face_detection_loaded': self.face_cascade is not None,
            'model_accuracy': self.model_accuracy,
            'model_file': 'emotion_detection_83.6_percent.pt',
            'model_paths_checked': self.model_paths,
            'emotions_supported': self.emotions,
            'emotion_count': len(self.emotions),
            'detection_count': self.detection_count,
            'avg_inference_time': self.total_inference_time / max(1, self.detection_count),
            'realtime_capable': self.yolo_model is not None,
            'status': 'Custom YOLOv8 Loaded (83.6%)' if self.yolo_model else 'Fallback Mode'
        }
        return info
    
    def cleanup(self):
        """Cleanup resources and close windows"""
        try:
            if self.popup_system:
                self.popup_system.close_popup()
            
            # Clear model from memory if needed
            if self.yolo_model is not None:
                del self.yolo_model
                self.yolo_model = None
            
            print("🧹 Custom YOLOv8 expression detector cleanup completed")
            
        except Exception as e:
            print(f"❌ Error in cleanup: {e}")


# Integration function for your main application
def integrate_custom_yolo_popup(main_window_instance):
    """
    Call this function to integrate your custom YOLOv8 model with popup system
    """
    try:
        # Replace the existing facial expression detector
        custom_detector = CustomYOLOv8ExpressionDetector()
        custom_detector.set_main_window(main_window_instance.root)
        
        # Replace the detector in main window
        main_window_instance.face_detector = custom_detector
        
        print("✅ Custom YOLOv8 model with popup system integrated successfully!")
        print("💡 Press SPACE bar during video capture to detect expression with your 83.6% model")
        print("🎯 Your emotion_detection_83.6_percent.pt model is now active!")
        
        return custom_detector
        
    except Exception as e:
        print(f"❌ Error integrating custom YOLOv8 popup system: {e}")
        return None


# Test function for your custom model
def test_custom_yolo_popup():
    """Test your custom YOLOv8 model with popup system"""
    print("🧪 TESTING YOUR CUSTOM YOLOV8 EMOTION MODEL WITH POPUP")
    print("=" * 70)
    
    try:
        # Create test window
        root = tk.Tk()
        root.title("Custom YOLOv8 Model Test")
        root.geometry("900x700")
        root.configure(bg='#f0f0f0')
        
        # Create test content
        test_label = tk.Label(root, 
                             text="🎭 Your Custom YOLOv8 Emotion Detection Test\n\n" +
                                  "Model: emotion_detection_83.6_percent.pt\n" +
                                  "Accuracy: 83.6%\n\n" +
                                  "Press the button to test popup with your model",
                             font=('Arial', 14), bg='#f0f0f0', justify='center')
        test_label.pack(expand=True, pady=50)
        
        # Initialize your custom detector
        custom_detector = CustomYOLOv8ExpressionDetector()
        custom_detector.set_main_window(root)
        
        def test_detection():
            """Test your model with dummy data"""
            # Create test frame
            test_frame = np.random.randint(0, 255, (480, 640, 3), dtype=np.uint8)
            
            # Add some face-like patterns for testing
            cv2.circle(test_frame, (320, 240), 80, (200, 180, 160), -1)  # Face
            cv2.circle(test_frame, (300, 220), 10, (50, 50, 50), -1)     # Left eye
            cv2.circle(test_frame, (340, 220), 10, (50, 50, 50), -1)     # Right eye
            cv2.ellipse(test_frame, (320, 260), (20, 10), 0, 0, 180, (50, 50, 50), 2)  # Smile
            
            # Test detection
            success = custom_detector.capture_and_detect(test_frame)
            if success:
                result = custom_detector.get_last_detection()
                print(f"✅ Test result: {result['emoji']} {result['expression']} ({result['confidence']:.3f})")
            else:
                print("❌ Test detection failed")
        
        # Test button
        test_btn = tk.Button(root, text="🎭 Test Your YOLOv8 Model Popup",
                            font=('Arial', 14, 'bold'), bg='#2563eb', fg='white',
                            relief='flat', bd=0, pady=15, padx=25,
                            command=test_detection)
        test_btn.pack(pady=30)
        
        # Model info
        model_info = custom_detector.get_model_info()
        status_text = f"Model Status: {model_info['status']}\n"
        status_text += f"Custom Model Loaded: {'✅' if model_info['custom_yolo_loaded'] else '❌'}\n"
        status_text += f"Emotions Supported: {model_info['emotion_count']}\n"
        status_text += f"Real-time Capable: {'✅' if model_info['realtime_capable'] else '❌'}"
        
        status_label = tk.Label(root, text=status_text,
                               font=('Arial', 11), bg='#f0f0f0', fg='#666',
                               justify='left')
        status_label.pack(pady=20)
        
        # Instructions
        instructions = tk.Label(root, 
                               text="This popup will appear when SPACE bar is pressed during video capture\n" +
                                    "Your trained model will provide 83.6% accurate emotion detection!",
                               font=('Arial', 10), bg='#f0f0f0', fg='#666',
                               justify='center')
        instructions.pack(pady=10)
        
        root.mainloop()
        
    except Exception as e:
        print(f"❌ Error during testing: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    test_custom_yolo_popup()