"""
Database Integration Module for AI Video Detection Dashboard
Provides real-time database operations and detection logging
"""

import sqlite3
import threading
import time
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
import json
import os

# Import error handling for graceful database operations
try:
    from .database_error_handler import safe_log_detection, database_operation
except ImportError:
    # Fallback if error handler is not available
    def safe_log_detection(detection_type, log_function, **kwargs):
        try:
            return log_function(**kwargs)
        except Exception as e:
            print(f"⚠️ Database error for {detection_type}: {e}")
            return False

    def database_operation(operation_name):
        def decorator(func):
            return func
        return decorator

class DetectionDatabase:
    """Unified database manager for detection results with real-time capabilities"""

    def __init__(self, db_path="detection_results.db"):
        self.db_path = db_path
        # Use shared session ID for consistency across all detection modules
        self.session_id = self.get_shared_session_id()
        self.lock = threading.Lock()
        self.init_database()

    @classmethod
    def get_shared_session_id(cls):
        """Get or create a shared session ID for all detection modules"""
        if not hasattr(cls, '_shared_session_id'):
            cls._shared_session_id = f"session_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        return cls._shared_session_id
        
    def init_database(self):
        """Initialize the detection results database with enhanced schema"""
        try:
            with self.lock:
                conn = sqlite3.connect(self.db_path)
                cursor = conn.cursor()
                
                # Unified age detections table - supports both age ranges and numeric ages
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS age_detections (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
                        age INTEGER CHECK(age >= 0 AND age <= 150),
                        age_range TEXT,
                        confidence REAL CHECK(confidence >= 0.0 AND confidence <= 1.0),
                        model_used TEXT NOT NULL DEFAULT 'Unknown',
                        session_id TEXT NOT NULL,
                        face_bbox TEXT,
                        coordinates TEXT,  -- Added for compatibility
                        category TEXT,
                        quality_score REAL DEFAULT 0.0,
                        stability_score REAL DEFAULT 0.0,
                        processing_time REAL DEFAULT 0.0,
                        created_at DATETIME DEFAULT CURRENT_TIMESTAMP
                    )
                ''')
                
                # Enhanced object detections table
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS object_detections (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
                        object_name TEXT NOT NULL,
                        confidence REAL CHECK(confidence >= 0.0 AND confidence <= 1.0),
                        bbox_x INTEGER DEFAULT 0,
                        bbox_y INTEGER DEFAULT 0,
                        bbox_w INTEGER DEFAULT 0,
                        bbox_h INTEGER DEFAULT 0,
                        session_id TEXT NOT NULL,
                        detection_method TEXT DEFAULT 'YOLO',
                        is_anomaly BOOLEAN DEFAULT FALSE,
                        created_at DATETIME DEFAULT CURRENT_TIMESTAMP
                    )
                ''')
                
                # Enhanced expression detections table
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS expression_detections (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
                        expression TEXT NOT NULL,
                        confidence REAL CHECK(confidence >= 0.0 AND confidence <= 1.0),
                        model_used TEXT NOT NULL DEFAULT 'Unknown',
                        session_id TEXT NOT NULL,
                        face_bbox TEXT,
                        all_probabilities TEXT,
                        processing_time REAL DEFAULT 0.0,
                        created_at DATETIME DEFAULT CURRENT_TIMESTAMP
                    )
                ''')
                
                # Enhanced anomaly detections table
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS anomaly_detections (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
                        anomaly_type TEXT NOT NULL,
                        confidence REAL CHECK(confidence >= 0.0 AND confidence <= 1.0),
                        threat_level TEXT DEFAULT 'LOW',
                        session_id TEXT NOT NULL,
                        description TEXT,
                        recording_path TEXT,
                        report_path TEXT,
                        objects_detected TEXT,
                        created_at DATETIME DEFAULT CURRENT_TIMESTAMP
                    )
                ''')
                
                # Session tracking table
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS detection_sessions (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        session_id TEXT UNIQUE NOT NULL,
                        start_time DATETIME DEFAULT CURRENT_TIMESTAMP,
                        end_time DATETIME,
                        total_detections INTEGER DEFAULT 0,
                        total_anomalies INTEGER DEFAULT 0,
                        status TEXT DEFAULT 'ACTIVE'
                    )
                ''')
                
                # Create indexes for performance
                indexes = [
                    'CREATE INDEX IF NOT EXISTS idx_age_timestamp ON age_detections(timestamp)',
                    'CREATE INDEX IF NOT EXISTS idx_age_session ON age_detections(session_id)',
                    'CREATE INDEX IF NOT EXISTS idx_object_timestamp ON object_detections(timestamp)',
                    'CREATE INDEX IF NOT EXISTS idx_object_session ON object_detections(session_id)',
                    'CREATE INDEX IF NOT EXISTS idx_expression_timestamp ON expression_detections(timestamp)',
                    'CREATE INDEX IF NOT EXISTS idx_expression_session ON expression_detections(session_id)',
                    'CREATE INDEX IF NOT EXISTS idx_anomaly_timestamp ON anomaly_detections(timestamp)',
                    'CREATE INDEX IF NOT EXISTS idx_anomaly_session ON anomaly_detections(session_id)',
                    'CREATE INDEX IF NOT EXISTS idx_session_start ON detection_sessions(start_time)'
                ]
                
                for index_sql in indexes:
                    cursor.execute(index_sql)
                
                # Run database migrations to ensure schema is up to date
                self._run_migrations(cursor)

                # Initialize current session
                cursor.execute('''
                    INSERT OR IGNORE INTO detection_sessions (session_id, start_time)
                    VALUES (?, ?)
                ''', (self.session_id, datetime.now()))

                conn.commit()
                conn.close()
                print(f"✅ Database initialized successfully - Session: {self.session_id}")

        except Exception as e:
            print(f"❌ Error initializing database: {e}")

    def _run_migrations(self, cursor):
        """Run database migrations to update schema"""
        try:
            # Check if detection_method column exists in object_detections
            cursor.execute("PRAGMA table_info(object_detections)")
            columns = [column[1] for column in cursor.fetchall()]

            if 'detection_method' not in columns:
                print("🔧 Adding detection_method column to object_detections...")
                cursor.execute("ALTER TABLE object_detections ADD COLUMN detection_method TEXT DEFAULT 'YOLO'")
                print("✅ Added detection_method column")

            # Check if category column exists in age_detections
            cursor.execute("PRAGMA table_info(age_detections)")
            columns = [column[1] for column in cursor.fetchall()]

            if 'category' not in columns:
                print("🔧 Adding category column to age_detections...")
                cursor.execute("ALTER TABLE age_detections ADD COLUMN category TEXT")
                print("✅ Added category column")

            if 'quality_score' not in columns:
                print("🔧 Adding quality_score column to age_detections...")
                cursor.execute("ALTER TABLE age_detections ADD COLUMN quality_score REAL DEFAULT 0.0")
                print("✅ Added quality_score column")

            if 'stability_score' not in columns:
                print("🔧 Adding stability_score column to age_detections...")
                cursor.execute("ALTER TABLE age_detections ADD COLUMN stability_score REAL DEFAULT 0.0")
                print("✅ Added stability_score column")

            # Check if is_anomaly column exists in object_detections
            cursor.execute("PRAGMA table_info(object_detections)")
            columns = [column[1] for column in cursor.fetchall()]

            if 'is_anomaly' not in columns:
                print("🔧 Adding is_anomaly column to object_detections...")
                cursor.execute("ALTER TABLE object_detections ADD COLUMN is_anomaly BOOLEAN DEFAULT 0")
                print("✅ Added is_anomaly column")

            # Check if columns exist in expression_detections
            cursor.execute("PRAGMA table_info(expression_detections)")
            columns = [column[1] for column in cursor.fetchall()]

            if 'coordinates' not in columns:
                print("🔧 Adding coordinates column to expression_detections...")
                cursor.execute("ALTER TABLE expression_detections ADD COLUMN coordinates TEXT DEFAULT ''")
                print("✅ Added coordinates column")

            if 'face_bbox' not in columns:
                print("🔧 Adding face_bbox column to expression_detections...")
                cursor.execute("ALTER TABLE expression_detections ADD COLUMN face_bbox TEXT DEFAULT ''")
                print("✅ Added face_bbox column")

            if 'all_probabilities' not in columns:
                print("🔧 Adding all_probabilities column to expression_detections...")
                cursor.execute("ALTER TABLE expression_detections ADD COLUMN all_probabilities TEXT DEFAULT ''")
                print("✅ Added all_probabilities column")

            if 'processing_time' not in columns:
                print("🔧 Adding processing_time column to expression_detections...")
                cursor.execute("ALTER TABLE expression_detections ADD COLUMN processing_time REAL DEFAULT 0.0")
                print("✅ Added processing_time column")

            # Check if age_range and face_bbox columns exist in age_detections
            cursor.execute("PRAGMA table_info(age_detections)")
            columns = [column[1] for column in cursor.fetchall()]

            if 'age_range' not in columns:
                print("🔧 Adding age_range column to age_detections...")
                cursor.execute("ALTER TABLE age_detections ADD COLUMN age_range TEXT DEFAULT ''")
                print("✅ Added age_range column")

            if 'face_bbox' not in columns:
                print("🔧 Adding face_bbox column to age_detections...")
                cursor.execute("ALTER TABLE age_detections ADD COLUMN face_bbox TEXT DEFAULT ''")
                print("✅ Added face_bbox column")

            if 'processing_time' not in columns:
                print("🔧 Adding processing_time column to age_detections...")
                cursor.execute("ALTER TABLE age_detections ADD COLUMN processing_time REAL DEFAULT 0.0")
                print("✅ Added processing_time column")

            # Check if columns exist in anomaly_detections
            cursor.execute("PRAGMA table_info(anomaly_detections)")
            columns = [column[1] for column in cursor.fetchall()]

            if 'description' not in columns:
                print("🔧 Adding description column to anomaly_detections...")
                cursor.execute("ALTER TABLE anomaly_detections ADD COLUMN description TEXT DEFAULT ''")
                print("✅ Added description column")

            if 'recording_path' not in columns:
                print("🔧 Adding recording_path column to anomaly_detections...")
                cursor.execute("ALTER TABLE anomaly_detections ADD COLUMN recording_path TEXT DEFAULT ''")
                print("✅ Added recording_path column")

            if 'report_path' not in columns:
                print("🔧 Adding report_path column to anomaly_detections...")
                cursor.execute("ALTER TABLE anomaly_detections ADD COLUMN report_path TEXT DEFAULT ''")
                print("✅ Added report_path column")

            if 'objects_detected' not in columns:
                print("🔧 Adding objects_detected column to anomaly_detections...")
                cursor.execute("ALTER TABLE anomaly_detections ADD COLUMN objects_detected TEXT DEFAULT ''")
                print("✅ Added objects_detected column")

            print("✅ Database migrations completed")

        except Exception as e:
            print(f"⚠️ Error running database migrations: {e}")
    
    def log_age_detection(self, age_range: str = None, age: int = None, confidence: float = 0.0,
                         model_used: str = "Unknown", face_bbox: str = "", bbox: tuple = None,
                         category: str = "", quality_score: float = 0.0, stability_score: float = 0.0,
                         processing_time: float = 0.0) -> bool:
        """Log age detection result - supports both age ranges and integer ages"""
        try:
            # Handle bbox parameter (tuple format)
            if bbox and isinstance(bbox, (tuple, list)) and len(bbox) >= 4:
                face_bbox = f"({bbox[0]},{bbox[1]},{bbox[2]},{bbox[3]})"

            # If age_range is provided, extract estimated age
            if age_range and age is None:
                # Extract age from range like "(25-32)" or "Young Adult (20-34)"
                import re
                age_match = re.search(r'\((\d+)-(\d+)\)', age_range)
                if age_match:
                    min_age, max_age = int(age_match.group(1)), int(age_match.group(2))
                    age = (min_age + max_age) // 2  # Use middle of range
                else:
                    # Fallback mapping for categories
                    age_mapping = {
                        'Child': 8, 'Teen': 16, 'Young Adult': 27, 'Adult': 44, 'Senior': 65
                    }
                    for key, default_age in age_mapping.items():
                        if key.lower() in age_range.lower():
                            age = default_age
                            break
                    else:
                        age = 30  # Default fallback

            # If age is provided but no age_range, determine range
            if age and not age_range:
                if age < 13:
                    age_range = "Child (0-12)"
                elif age < 20:
                    age_range = "Teen (13-19)"
                elif age < 35:
                    age_range = "Young Adult (20-34)"
                elif age < 55:
                    age_range = "Adult (35-54)"
                else:
                    age_range = "Senior (55+)"

            # Ensure we have both age and age_range
            if not age:
                age = 30  # Default fallback
            if not age_range:
                age_range = "Adult (20-50)"

            data = {
                'timestamp': datetime.now(),
                'age': max(0, min(150, int(age))),
                'age_range': str(age_range)[:50],
                'confidence': max(0.0, min(1.0, float(confidence))),
                'model_used': str(model_used)[:50],
                'session_id': self.session_id,
                'face_bbox': str(face_bbox)[:200],
                'processing_time': float(processing_time)
            }

            # Add additional fields if provided
            if category:
                data['category'] = str(category)[:30]
            if quality_score > 0:
                data['quality_score'] = float(quality_score)
            if stability_score > 0:
                data['stability_score'] = float(stability_score)

            return self._insert_detection('age_detections', data)

        except Exception as e:
            print(f"❌ Error logging age detection: {e}")
            return False
    
    def log_object_detection(self, object_name: str, confidence: float, bbox: tuple = (0,0,0,0),
                           detection_method: str = "YOLO", is_anomaly: bool = False) -> bool:
        """Log object detection result"""
        try:
            bbox_x, bbox_y, bbox_w, bbox_h = bbox if len(bbox) == 4 else (0, 0, 0, 0)
            
            data = {
                'timestamp': datetime.now(),
                'object_name': str(object_name)[:50],
                'confidence': max(0.0, min(1.0, float(confidence))),
                'bbox_x': int(bbox_x),
                'bbox_y': int(bbox_y),
                'bbox_w': int(bbox_w),
                'bbox_h': int(bbox_h),
                'session_id': self.session_id,
                'detection_method': str(detection_method)[:30],
                'is_anomaly': bool(is_anomaly)
            }
            
            return self._insert_detection('object_detections', data)
            
        except Exception as e:
            print(f"❌ Error logging object detection: {e}")
            return False
    
    def log_expression_detection(self, expression: str, confidence: float, model_used: str = "Unknown",
                               coordinates: str = "", face_bbox: str = "", all_probabilities: dict = None,
                               processing_time: float = 0.0) -> bool:
        """Log expression detection result - supports both coordinates and face_bbox for compatibility"""
        try:
            # Support both coordinates and face_bbox parameters for backward compatibility
            coord_value = coordinates if coordinates else face_bbox
            bbox_value = face_bbox if face_bbox else coordinates

            data = {
                'timestamp': datetime.now(),
                'expression': str(expression)[:30],
                'confidence': max(0.0, min(1.0, float(confidence))),
                'model_used': str(model_used)[:50],
                'session_id': self.session_id,
                'coordinates': str(coord_value)[:200],
                'face_bbox': str(bbox_value)[:200],  # Store in both fields for compatibility
                'processing_time': float(processing_time)
            }

            # Add probabilities if provided
            if all_probabilities:
                data['all_probabilities'] = json.dumps(all_probabilities)

            return self._insert_detection('expression_detections', data)

        except Exception as e:
            print(f"❌ Error logging expression detection: {e}")
            return False
    
    def log_anomaly_detection(self, anomaly_type: str, confidence: float, threat_level: str = "LOW",
                            description: str = "", recording_path: str = "", report_path: str = "",
                            objects_detected: list = None) -> bool:
        """Log anomaly detection result"""
        try:
            data = {
                'timestamp': datetime.now(),
                'anomaly_type': str(anomaly_type)[:50],
                'confidence': max(0.0, min(1.0, float(confidence))),
                'threat_level': str(threat_level)[:10].upper(),
                'session_id': self.session_id,
                'description': str(description)[:500],
                'recording_path': str(recording_path)[:200],
                'report_path': str(report_path)[:200],
                'objects_detected': json.dumps(objects_detected) if objects_detected else None
            }
            
            return self._insert_detection('anomaly_detections', data)
            
        except Exception as e:
            print(f"❌ Error logging anomaly detection: {e}")
            return False
    
    def _insert_detection(self, table: str, data: dict) -> bool:
        """Generic method to insert detection data"""
        try:
            with self.lock:
                conn = sqlite3.connect(self.db_path)
                cursor = conn.cursor()
                
                # Build dynamic INSERT query
                columns = list(data.keys())
                placeholders = ['?' for _ in columns]
                values = list(data.values())
                
                query = f"INSERT INTO {table} ({', '.join(columns)}) VALUES ({', '.join(placeholders)})"
                cursor.execute(query, values)
                
                # Update session total
                cursor.execute('''
                    UPDATE detection_sessions 
                    SET total_detections = total_detections + 1
                    WHERE session_id = ?
                ''', (self.session_id,))
                
                # Update anomaly count if it's an anomaly
                if table == 'anomaly_detections':
                    cursor.execute('''
                        UPDATE detection_sessions 
                        SET total_anomalies = total_anomalies + 1
                        WHERE session_id = ?
                    ''', (self.session_id,))
                
                conn.commit()
                conn.close()
                return True
                
        except Exception as e:
            print(f"❌ Error inserting into {table}: {e}")
            return False
    
    def get_recent_detections(self, detection_type: str, limit: int = 100) -> List[Dict]:
        """Get recent detections of specified type"""
        try:
            table_map = {
                'age': 'age_detections',
                'object': 'object_detections',
                'expression': 'expression_detections',
                'anomaly': 'anomaly_detections'
            }
            
            table = table_map.get(detection_type)
            if not table:
                return []
            
            with self.lock:
                conn = sqlite3.connect(self.db_path)
                cursor = conn.cursor()
                
                cursor.execute(f'''
                    SELECT * FROM {table} 
                    ORDER BY timestamp DESC 
                    LIMIT ?
                ''', (limit,))
                
                results = cursor.fetchall()
                column_names = [description[0] for description in cursor.description]
                
                conn.close()
                
                return [dict(zip(column_names, row)) for row in results]
                
        except Exception as e:
            print(f"❌ Error getting recent {detection_type} detections: {e}")
            return []
    
    def get_session_summary(self, session_id: str = None) -> Dict:
        """Get comprehensive session summary"""
        if not session_id:
            session_id = self.session_id
            
        try:
            with self.lock:
                conn = sqlite3.connect(self.db_path)
                cursor = conn.cursor()
                
                # Get session info
                cursor.execute('''
                    SELECT * FROM detection_sessions WHERE session_id = ?
                ''', (session_id,))
                session_info = cursor.fetchone()
                
                if not session_info:
                    return {}
                
                # Get counts for each detection type
                counts = {}
                for det_type, table in [('age', 'age_detections'), ('object', 'object_detections'),
                                      ('expression', 'expression_detections'), ('anomaly', 'anomaly_detections')]:
                    cursor.execute(f'''
                        SELECT COUNT(*) FROM {table} WHERE session_id = ?
                    ''', (session_id,))
                    counts[det_type] = cursor.fetchone()[0]
                
                # Get average confidence
                cursor.execute('''
                    SELECT AVG(confidence) FROM (
                        SELECT confidence FROM age_detections WHERE session_id = ?
                        UNION ALL
                        SELECT confidence FROM object_detections WHERE session_id = ?
                        UNION ALL
                        SELECT confidence FROM expression_detections WHERE session_id = ?
                        UNION ALL
                        SELECT confidence FROM anomaly_detections WHERE session_id = ?
                    )
                ''', (session_id, session_id, session_id, session_id))
                avg_confidence = cursor.fetchone()[0] or 0.0
                
                conn.close()
                
                return {
                    'session_id': session_id,
                    'start_time': session_info[2],
                    'total_detections': session_info[4],  # Fixed: column 4 is total_detections
                    'total_anomalies': session_info[5],   # Fixed: column 5 is total_anomalies
                    'status': session_info[6],            # Fixed: column 6 is status
                    'detection_counts': counts,
                    'avg_confidence': avg_confidence
                }
                
        except Exception as e:
            print(f"❌ Error getting session summary: {e}")
            return {}
    
    def clear_old_data(self, days_old: int = 30) -> int:
        """Clear data older than specified days"""
        try:
            cutoff_date = datetime.now() - timedelta(days=days_old)
            
            with self.lock:
                conn = sqlite3.connect(self.db_path)
                cursor = conn.cursor()
                
                tables = ['age_detections', 'object_detections', 'expression_detections', 'anomaly_detections']
                total_deleted = 0
                
                for table in tables:
                    cursor.execute(f"DELETE FROM {table} WHERE timestamp < ?", (cutoff_date,))
                    total_deleted += cursor.rowcount
                
                # Clean up old sessions
                cursor.execute("DELETE FROM detection_sessions WHERE start_time < ?", (cutoff_date,))
                
                conn.commit()
                conn.close()
                
                print(f"✅ Cleared {total_deleted} old detection records")
                return total_deleted
                
        except Exception as e:
            print(f"❌ Error clearing old data: {e}")
            return 0
    
    def close_session(self):
        """Close current session"""
        try:
            with self.lock:
                conn = sqlite3.connect(self.db_path)
                cursor = conn.cursor()
                
                cursor.execute('''
                    UPDATE detection_sessions 
                    SET end_time = ?, status = 'CLOSED'
                    WHERE session_id = ?
                ''', (datetime.now(), self.session_id))
                
                conn.commit()
                conn.close()
                print(f"✅ Session {self.session_id} closed")
                
        except Exception as e:
            print(f"❌ Error closing session: {e}")

# Global database instance
_db_instance = None

def get_database() -> DetectionDatabase:
    """Get global database instance"""
    global _db_instance
    if _db_instance is None:
        _db_instance = DetectionDatabase()
    return _db_instance

def log_detection(detection_type: str, **kwargs) -> bool:
    """Convenience function to log detection"""
    db = get_database()
    
    if detection_type == 'age':
        return db.log_age_detection(**kwargs)
    elif detection_type == 'object':
        return db.log_object_detection(**kwargs)
    elif detection_type == 'expression':
        return db.log_expression_detection(**kwargs)
    elif detection_type == 'anomaly':
        return db.log_anomaly_detection(**kwargs)
    else:
        print(f"❌ Unknown detection type: {detection_type}")
        return False
