@echo off
title AI Video Detection Tool
echo.
echo 🛡️ AI Video Detection Tool Starting...
echo =========================================
echo.

:: Go to script directory
cd /d "%~dp0"

:: Check Python
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python not found! Install from https://python.org
    pause
    exit /b 1
)

:: Install dependencies quickly
echo 📦 Installing dependencies...
pip install opencv-python numpy pillow tensorflow --quiet

:: Create directories if needed
for %%d in (logs reports snapshots Security_Footage models) do (
    if not exist "%%d" mkdir "%%d"
)

:: Launch app
echo 🚀 Starting AI Video Detection...
echo.
echo Trying new restructured application...
python app/launcher.py
if errorlevel 1 (
    echo.
    echo Falling back to legacy application...
    python run_enhanced_app.py
)

:: Handle exit
if errorlevel 1 (
    echo.
    echo ❌ Error occurred - check camera permissions
    pause
)

echo.
echo 👋 Session ended
pause