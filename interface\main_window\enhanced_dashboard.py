"""
AI Video Detection Dashboard - Streamlined & Fully Functional
Real-time analytics, accurate data display, and comprehensive PDF reporting
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import os
import shutil
from datetime import datetime, timedelta
import json
import csv
import sqlite3
import threading
import time
from collections import Counter
import numpy as np

# Improved database integration
try:
    from utils.database_integration import get_database
    DATABASE_INTEGRATION_AVAILABLE = True
except ImportError:
    DATABASE_INTEGRATION_AVAILABLE = False
    print("⚠️ Improved database integration not available")

# PDF generation
try:
    from reportlab.lib.pagesizes import letter, A4
    from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Table, TableStyle, PageBreak
    from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
    from reportlab.lib.units import inch
    from reportlab.lib import colors
    from reportlab.graphics.shapes import Drawing
    from reportlab.graphics.charts.piecharts import Pie
    from reportlab.graphics.charts.barcharts import VerticalBarChart
    PDF_AVAILABLE = True
    print("✅ PDF generation available (ReportLab)")
except ImportError:
    PDF_AVAILABLE = False
    print("⚠️ PDF generation not available - install reportlab: pip install reportlab")

# Charts for analytics
try:
    import matplotlib.pyplot as plt
    from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
    from matplotlib.figure import Figure
    MATPLOTLIB_AVAILABLE = True
except ImportError:
    MATPLOTLIB_AVAILABLE = False

class ImprovedButton(tk.Button):
    """Improved button with comprehensive user experience features"""

    def __init__(self, parent, text, command, bg_color='#2E86AB', fg_color='white',
                 width=12, tooltip=None, loading_text=None, success_text=None,
                 confirm_action=False, confirm_message=None, **kwargs):

        # Store configuration
        self.bg_color = bg_color
        self.fg_color = fg_color
        self.hover_color = self.lighten_color(bg_color)
        self.click_color = self.darken_color(bg_color)
        self.disabled_color = '#BDC3C7'

        # Store text states
        self.original_text = text
        self.loading_text = loading_text or f"⏳ {text.split(' ', 1)[-1]}..."
        self.success_text = success_text or f"✅ {text.split(' ', 1)[-1]} Complete"

        # Store command and confirmation settings
        self.original_command = command
        self.confirm_action = confirm_action
        self.confirm_message = confirm_message or f"Are you sure you want to {text.lower()}?"

        # State management
        self.is_loading = False
        self.is_disabled = False

        # Create button with enhanced styling
        super().__init__(parent, text=text, command=self.handle_click,
                        bg=bg_color, fg=fg_color,
                        font=('Arial', 11, 'bold'), relief='flat', bd=0,
                        pady=10, padx=15, width=width, cursor='hand2',
                        activebackground=self.hover_color, activeforeground=fg_color,
                        **kwargs)

        # Bind events for enhanced interactions
        self.bind('<Enter>', self.on_enter)
        self.bind('<Leave>', self.on_leave)
        self.bind('<Button-1>', self.on_click)
        self.bind('<ButtonRelease-1>', self.on_release)
        self.bind('<FocusIn>', self.on_focus_in)
        self.bind('<FocusOut>', self.on_focus_out)

        # Add tooltip if provided
        if tooltip:
            self.create_tooltip(tooltip)

    def lighten_color(self, color):
        """Lighten color for hover effect"""
        try:
            if color.startswith('#'):
                color = color[1:]
            r, g, b = int(color[0:2], 16), int(color[2:4], 16), int(color[4:6], 16)
            r = min(255, r + 40)
            g = min(255, g + 40)
            b = min(255, b + 40)
            return f'#{r:02x}{g:02x}{b:02x}'
        except:
            return '#5DADE2'

    def darken_color(self, color):
        """Darken color for click effect"""
        try:
            if color.startswith('#'):
                color = color[1:]
            r, g, b = int(color[0:2], 16), int(color[2:4], 16), int(color[4:6], 16)
            r = max(0, r - 40)
            g = max(0, g - 40)
            b = max(0, b - 40)
            return f'#{r:02x}{g:02x}{b:02x}'
        except:
            return '#1F5F7A'

    def create_tooltip(self, text):
        """Create tooltip for the button"""
        def show_tooltip(event):
            tooltip = tk.Toplevel()
            tooltip.wm_overrideredirect(True)
            tooltip.wm_geometry(f"+{event.x_root+10}+{event.y_root+10}")
            tooltip.configure(bg='#2C3E50')

            label = tk.Label(tooltip, text=text, bg='#2C3E50', fg='white',
                           font=('Arial', 9), padx=8, pady=4)
            label.pack()

            def hide_tooltip():
                tooltip.destroy()

            tooltip.after(3000, hide_tooltip)  # Auto-hide after 3 seconds
            self.tooltip_window = tooltip

        def hide_tooltip(event):
            if hasattr(self, 'tooltip_window'):
                self.tooltip_window.destroy()

        self.bind('<Enter>', lambda e: [self.on_enter(e), show_tooltip(e)])
        self.bind('<Leave>', lambda e: [self.on_leave(e), hide_tooltip(e)])

    def handle_click(self):
        """Handle button click with confirmation and loading states"""
        if self.is_loading or self.is_disabled:
            return

        # Show confirmation dialog if required
        if self.confirm_action:
            from tkinter import messagebox
            if not messagebox.askyesno("Confirm Action", self.confirm_message):
                return

        # Execute command with loading state
        if self.original_command:
            self.set_loading_state(True)
            try:
                # Execute command in a way that allows for async operations
                result = self.original_command()

                # Show success state briefly
                self.set_success_state()
                self.after(1500, lambda: self.set_loading_state(False))

            except Exception as e:
                print(f"❌ Button command error: {e}")
                self.set_loading_state(False)
                from tkinter import messagebox
                messagebox.showerror("Error", f"Operation failed: {str(e)}")

    def set_loading_state(self, loading=True):
        """Set button loading state"""
        self.is_loading = loading
        if loading:
            self.config(text=self.loading_text, bg=self.disabled_color,
                       cursor='wait', state='disabled')
        else:
            self.config(text=self.original_text, bg=self.bg_color,
                       cursor='hand2', state='normal')

    def set_success_state(self):
        """Briefly show success state"""
        self.config(text=self.success_text, bg='#27AE60')

    def set_enabled(self, enabled=True):
        """Enable or disable the button"""
        self.is_disabled = not enabled
        if enabled:
            self.config(state='normal', bg=self.bg_color, cursor='hand2')
        else:
            self.config(state='disabled', bg=self.disabled_color, cursor='X_cursor')

    def on_enter(self, event):
        """Handle mouse enter"""
        if not self.is_loading and not self.is_disabled:
            self.config(bg=self.hover_color)

    def on_leave(self, event):
        """Handle mouse leave"""
        if not self.is_loading and not self.is_disabled:
            self.config(bg=self.bg_color)

    def on_click(self, event):
        """Handle mouse click"""
        if not self.is_loading and not self.is_disabled:
            self.config(bg=self.click_color)

    def on_release(self, event):
        """Handle mouse release"""
        if not self.is_loading and not self.is_disabled:
            self.config(bg=self.hover_color)

    def on_focus_in(self, event):
        """Handle keyboard focus"""
        if not self.is_loading and not self.is_disabled:
            self.config(relief='solid', bd=2)

    def on_focus_out(self, event):
        """Handle keyboard focus loss"""
        self.config(relief='flat', bd=0)

# Keep ColoredButton for backward compatibility
class ColoredButton(ImprovedButton):
    """Backward compatibility wrapper for ColoredButton"""
    def __init__(self, parent, text, command, bg_color='#2E86AB', fg_color='white', width=12, **kwargs):
        super().__init__(parent, text, command, bg_color, fg_color, width, **kwargs)

# Keep EnhancedButton for backward compatibility
class EnhancedButton(ImprovedButton):
    """Backward compatibility wrapper for EnhancedButton"""
    def __init__(self, parent, text, command, bg_color='#2E86AB', fg_color='white', width=12, **kwargs):
        super().__init__(parent, text, command, bg_color, fg_color, width, **kwargs)

# Keep ProfessionalButton for backward compatibility
class ProfessionalButton(ImprovedButton):
    """Backward compatibility wrapper for ProfessionalButton"""
    def __init__(self, parent, text, command, bg_color='#2E86AB', fg_color='white', width=12, **kwargs):
        super().__init__(parent, text, command, bg_color, fg_color, width, **kwargs)

class StreamlinedDashboard:
    """Streamlined AI Detection Dashboard with real-time data and PDF reports"""
    
    def __init__(self):
        self.root = None
        self.enhanced_db = None
        self.current_data = {}
        self.stat_labels = {}
        self.auto_refresh = False
        self.refresh_interval = 5000  # 5 seconds
        self.after_id = None
        self.last_refresh_time = datetime.now()

        # Initialize improved database - SAME instance as main application
        if DATABASE_INTEGRATION_AVAILABLE:
            try:
                self.improved_db = get_database()
                print("✅ Improved database connected to dashboard")
                print(f"📊 Database path: {self.improved_db.db_path}")
                print(f"📊 Session ID: {self.improved_db.session_id}")

                # Test database connection
                test_counts = self.get_current_detection_counts()
                print(f"📊 Current database records: {test_counts}")

            except Exception as e:
                print(f"⚠️ Improved database connection failed: {e}")
                self.improved_db = None
        
        # Detection type filters
        self.detection_types = ['age', 'object', 'expression', 'anomaly']

        # Initialize filter variables (needed for non-GUI usage)
        self.filter_vars = {}
        for det_type in self.detection_types:
            self.filter_vars[det_type] = type('MockVar', (), {'get': lambda self=None: True})()

        # Initialize time variable (needed for non-GUI usage)
        self.time_var = type('MockVar', (), {'get': lambda self=None: "All Time"})()

        # Time range options
        self.time_ranges = {
            "Last Hour": 1,
            "Last 6 Hours": 6,
            "Last 24 Hours": 24,
            "Last 7 Days": 168,
            "All Time": 0
        }

        # Default to show ALL data to ensure alignment with detection counts
        self.default_time_range = "All Time"

    def get_current_detection_counts(self):
        """Get current detection counts from database for verification"""
        try:
            if not self.improved_db:
                return {"error": "No database connection"}

            counts = {}
            detection_types = ['age', 'object', 'expression', 'anomaly']

            for det_type in detection_types:
                try:
                    # Get all records for this type with increased limit
                    records = self.improved_db.get_recent_detections(det_type, limit=100000)
                    counts[det_type] = len(records)
                except Exception as e:
                    print(f"❌ Error getting {det_type} count: {e}")
                    counts[det_type] = 0

            counts['total'] = sum(counts.values())
            return counts

        except Exception as e:
            print(f"❌ Error getting detection counts: {e}")
            return {"error": str(e)}
    
    def show_dashboard(self):
        """Show the streamlined dashboard window"""
        try:
            # Create dashboard window
            self.root = tk.Toplevel()
            self.root.title("🎯 AI Detection Dashboard - Analytics")
            self.root.geometry("1200x800")
            self.root.configure(bg='#F8F9FA')
            self.root.resizable(True, True)
            
            # Set window icon and properties
            try:
                self.root.iconbitmap('icon.ico')
            except:
                pass
            
            # Create main interface
            self.create_header()
            self.create_controls()
            self.create_statistics()
            self.create_analytics()
            
            # Force initial data load to show REAL-TIME data
            print("🔄 Loading real-time data from main application database...")
            self.refresh_data()

            # Start auto-refresh for live updates
            self.auto_refresh = True
            self.auto_refresh_var.set(True)
            self.start_auto_refresh()

            print("✅ Streamlined dashboard opened with LIVE data")
            print("🔄 Auto-refresh enabled for real-time updates")
            
        except Exception as e:
            print(f"❌ Error opening dashboard: {e}")
            messagebox.showerror("Dashboard Error", f"Failed to open dashboard: {e}")
    
    def create_header(self):
        """Create dashboard header with title and status"""
        header_frame = tk.Frame(self.root, bg='#2C3E50', height=80)
        header_frame.pack(fill='x')
        header_frame.pack_propagate(False)
        
        # Title
        title_label = tk.Label(header_frame, text="🎯 AI Detection Dashboard",
                              font=('Arial', 20, 'bold'), bg='#2C3E50', fg='white')
        title_label.pack(side='left', padx=20, pady=20)
        
        # Status and time
        status_frame = tk.Frame(header_frame, bg='#2C3E50')
        status_frame.pack(side='right', padx=20, pady=20)
        
        self.status_label = tk.Label(status_frame, text="🟢 ACTIVE",
                                   font=('Arial', 12, 'bold'), bg='#2C3E50', fg='#2ECC71')
        self.status_label.pack(anchor='e')
        
        self.time_label = tk.Label(status_frame, text=datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                                 font=('Arial', 10), bg='#2C3E50', fg='#BDC3C7')
        self.time_label.pack(anchor='e')
        
        # Update time every second
        self.update_time()
    
    def create_controls(self):
        """Create control panel with essential buttons"""
        controls_frame = tk.Frame(self.root, bg='white', relief='solid', bd=1)
        controls_frame.pack(fill='x', padx=10, pady=5)
        
        # Time range selection
        time_frame = tk.LabelFrame(controls_frame, text="📅 Time Range", bg='white', fg='#2C3E50',
                                 font=('Arial', 10, 'bold'))
        time_frame.pack(side='left', fill='y', padx=5, pady=5)
        
        self.time_var = tk.StringVar(value="All Time")  # Default to ALL data
        time_combo = ttk.Combobox(time_frame, textvariable=self.time_var,
                                values=list(self.time_ranges.keys()), state='readonly', width=15)
        time_combo.pack(padx=5, pady=5)
        time_combo.bind('<<ComboboxSelected>>', lambda e: self.refresh_data())
        
        # Detection filters
        filter_frame = tk.LabelFrame(controls_frame, text="🔍 Detection Types", bg='white', fg='#2C3E50',
                                   font=('Arial', 10, 'bold'))
        filter_frame.pack(side='left', fill='y', padx=5, pady=5)
        
        self.filter_vars = {}
        for det_type in self.detection_types:
            var = tk.BooleanVar(value=True)
            self.filter_vars[det_type] = var
            cb = tk.Checkbutton(filter_frame, text=det_type.title(), variable=var, bg='white',
                              command=self.refresh_data, font=('Arial', 9))
            cb.pack(anchor='w', padx=5, pady=1)
        
        # Action buttons
        action_frame = tk.LabelFrame(controls_frame, text="⚡ Actions", bg='white', fg='#2C3E50',
                                   font=('Arial', 10, 'bold'))
        action_frame.pack(side='left', fill='y', padx=5, pady=5)
        
        # Improved buttons with tooltips and better functionality
        improved_buttons = [
            {
                'text': "🔄 Refresh",
                'color': '#2E86AB',
                'command': self.improved_refresh_data,
                'tooltip': "Refresh dashboard data from database\nUpdates all statistics and charts",
                'loading_text': "🔄 Refreshing...",
                'success_text': "✅ Refreshed"
            },
            {
                'text': "📄 PDF Report",
                'color': '#27AE60',
                'command': self.improved_generate_pdf_report,
                'tooltip': "Generate comprehensive PDF report\nIncludes all detection data and analytics",
                'loading_text': "📄 Generating...",
                'success_text': "✅ PDF Created"
            },
            {
                'text': "📊 Export CSV",
                'color': '#F39C12',
                'command': self.improved_export_csv,
                'tooltip': "Export detection data to CSV file\nIncludes timestamps and confidence scores",
                'loading_text': "📊 Exporting...",
                'success_text': "✅ CSV Exported"
            },
            {
                'text': "🗑️ Delete Records",
                'color': '#E74C3C',
                'command': self.improved_delete_records,
                'tooltip': "Delete detection records with filters\nSupports confidence and time range filters",
                'confirm_action': True,
                'confirm_message': "⚠️ WARNING: This will open the deletion dialog.\n\n🚨 DELETIONS ARE PERMANENT AND CANNOT BE UNDONE!\n\nDeleted records will be permanently removed from the database.\nMake sure you have backups if needed.\n\nProceed with caution!"
            },
            {
                'text': "🧹 Clear Old Data",
                'color': '#95A5A6',
                'command': self.improved_clear_old_data,
                'tooltip': "Remove old detection records\nHelps maintain database performance",
                'confirm_action': True,
                'confirm_message': "⚠️ WARNING: This will open the data cleanup dialog.\n\n🚨 DATA REMOVAL IS PERMANENT AND CANNOT BE UNDONE!\n\nOld records will be permanently deleted from the database.\nThis action helps maintain performance but removes historical data.\n\nProceed with caution!"
            }
        ]

        for btn_config in improved_buttons:
            btn = EnhancedButton(
                action_frame,
                text=btn_config['text'],
                command=btn_config['command'],
                bg_color=btn_config['color'],
                width=14,
                tooltip=btn_config['tooltip'],
                loading_text=btn_config.get('loading_text'),
                success_text=btn_config.get('success_text'),
                confirm_action=btn_config.get('confirm_action', False),
                confirm_message=btn_config.get('confirm_message')
            )
            btn.pack(side='left', padx=3, pady=5)
        
        # Auto-refresh toggle
        refresh_frame = tk.LabelFrame(controls_frame, text="🔄 Auto-Refresh", bg='white', fg='#2C3E50',
                                    font=('Arial', 10, 'bold'))
        refresh_frame.pack(side='right', fill='y', padx=5, pady=5)
        
        self.auto_refresh_var = tk.BooleanVar()
        auto_cb = tk.Checkbutton(refresh_frame, text="Enable (5s)", variable=self.auto_refresh_var,
                               bg='white', command=self.toggle_auto_refresh, font=('Arial', 9))
        auto_cb.pack(padx=5, pady=5)
        
        # Last refresh status
        self.refresh_status_label = tk.Label(refresh_frame, text="Ready", 
                                           font=('Arial', 8), bg='white', fg='#7F8C8D')
        self.refresh_status_label.pack(padx=5, pady=2)
    
    def create_statistics(self):
        """Create real-time statistics display"""
        stats_frame = tk.Frame(self.root, bg='white', relief='solid', bd=1)
        stats_frame.pack(fill='x', padx=10, pady=5)
        
        title_label = tk.Label(stats_frame, text="📈 Real-Time Detection Statistics",
                              font=('Arial', 14, 'bold'), bg='white', fg='#2C3E50')
        title_label.pack(pady=10)
        
        # Statistics cards container
        cards_container = tk.Frame(stats_frame, bg='white')
        cards_container.pack(fill='x', padx=20, pady=10)
        
        # Configure grid
        for i in range(5):
            cards_container.grid_columnconfigure(i, weight=1)
        
        # Create stat cards
        self.create_stat_card(cards_container, "👶 Age", "0", "#E67E22", 0, 0)
        self.create_stat_card(cards_container, "🔍 Objects", "0", "#16A085", 0, 1)
        self.create_stat_card(cards_container, "😊 Expressions", "0", "#8E44AD", 0, 2)
        self.create_stat_card(cards_container, "🚨 Anomalies", "0", "#E74C3C", 0, 3)
        self.create_stat_card(cards_container, "🎯 Confidence", "0%", "#3498DB", 0, 4)
    
    def create_stat_card(self, parent, title, value, color, row, col):
        """Create individual statistic card"""
        card_frame = tk.Frame(parent, bg='#ECF0F1', relief='solid', bd=1)
        card_frame.grid(row=row, column=col, padx=5, pady=5, sticky='nsew')
        
        # Colored header
        header_frame = tk.Frame(card_frame, bg=color, height=4)
        header_frame.pack(fill='x')
        
        # Content
        content_frame = tk.Frame(card_frame, bg='#ECF0F1')
        content_frame.pack(fill='both', expand=True, padx=10, pady=10)
        
        # Title
        title_label = tk.Label(content_frame, text=title, font=('Arial', 10, 'bold'),
                              bg='#ECF0F1', fg='#2C3E50')
        title_label.pack()
        
        # Value
        value_label = tk.Label(content_frame, text=value, font=('Arial', 16, 'bold'),
                              bg='#ECF0F1', fg=color)
        value_label.pack(pady=5)
        
        # Store reference for updates
        key = title.split()[1].lower()  # Extract key from emoji title
        self.stat_labels[key] = value_label
    
    def create_analytics(self):
        """Create analytics section with charts"""
        analytics_frame = tk.Frame(self.root, bg='white', relief='solid', bd=1)
        analytics_frame.pack(fill='both', expand=True, padx=10, pady=5)
        
        title_label = tk.Label(analytics_frame, text="📊 Detection Analytics",
                              font=('Arial', 14, 'bold'), bg='white', fg='#2C3E50')
        title_label.pack(pady=10)
        
        # Create notebook for different views
        notebook = ttk.Notebook(analytics_frame)
        notebook.pack(fill='both', expand=True, padx=20, pady=10)
        
        # Overview tab
        overview_frame = ttk.Frame(notebook)
        notebook.add(overview_frame, text="📊 Overview")
        self.create_overview_chart(overview_frame)
        
        # Details tab
        details_frame = ttk.Frame(notebook)
        notebook.add(details_frame, text="📋 Details")
        self.create_details_view(details_frame)
    
    def create_overview_chart(self, parent):
        """Create overview chart or text display"""
        if MATPLOTLIB_AVAILABLE:
            self.create_matplotlib_chart(parent)
        else:
            self.create_text_overview(parent)
    
    def create_matplotlib_chart(self, parent):
        """Create matplotlib chart for overview"""
        try:
            fig = Figure(figsize=(10, 6), dpi=80)
            fig.patch.set_facecolor('white')
            
            # Detection counts pie chart
            ax1 = fig.add_subplot(121)
            detection_counts = [
                len(self.current_data.get('age', [])),
                len(self.current_data.get('object', [])),
                len(self.current_data.get('expression', [])),
                len(self.current_data.get('anomaly', []))
            ]
            labels = ['Age', 'Objects', 'Expressions', 'Anomalies']
            colors = ['#E67E22', '#16A085', '#8E44AD', '#E74C3C']
            
            if sum(detection_counts) > 0:
                ax1.pie(detection_counts, labels=labels, colors=colors, autopct='%1.1f%%', startangle=90)
                ax1.set_title('Detection Distribution', fontsize=12, fontweight='bold')
            else:
                ax1.text(0.5, 0.5, 'No data available', ha='center', va='center', transform=ax1.transAxes)
                ax1.set_title('Detection Distribution', fontsize=12, fontweight='bold')
            
            # Confidence histogram
            ax2 = fig.add_subplot(122)
            all_confidences = []
            for data_type in self.current_data.values():
                for item in data_type:
                    confidence = item.get('confidence')
                    if confidence is not None:
                        all_confidences.append(float(confidence))
            
            if all_confidences:
                ax2.hist(all_confidences, bins=15, color='#3498DB', alpha=0.7, edgecolor='black')
                ax2.set_xlabel('Confidence Level')
                ax2.set_ylabel('Count')
                ax2.set_title('Confidence Distribution', fontsize=12, fontweight='bold')
                ax2.grid(True, alpha=0.3)
            else:
                ax2.text(0.5, 0.5, 'No confidence data', ha='center', va='center', transform=ax2.transAxes)
                ax2.set_title('Confidence Distribution', fontsize=12, fontweight='bold')
            
            plt.tight_layout()
            
            # Embed in tkinter
            canvas = FigureCanvasTkAgg(fig, parent)
            canvas.draw()
            canvas.get_tk_widget().pack(fill='both', expand=True)

            # Store reference for updates
            self.chart_canvas = canvas
            self.chart_figure = fig
            
        except Exception as e:
            print(f"❌ Error creating matplotlib chart: {e}")
            self.create_text_overview(parent)
    
    def create_text_overview(self, parent):
        """Create text-based overview when matplotlib unavailable"""
        text_frame = tk.Frame(parent, bg='white')
        text_frame.pack(fill='both', expand=True, padx=20, pady=20)
        
        overview_text = tk.Text(text_frame, font=('Courier', 11), bg='white', fg='#2C3E50',
                               wrap='word', height=15)
        overview_text.pack(fill='both', expand=True)
        
        # Generate overview content
        detection_counts = {
            'Age': len(self.current_data.get('age', [])),
            'Objects': len(self.current_data.get('object', [])),
            'Expressions': len(self.current_data.get('expression', [])),
            'Anomalies': len(self.current_data.get('anomaly', []))
        }
        
        total = sum(detection_counts.values())
        
        content = "📊 DETECTION ANALYTICS OVERVIEW\n"
        content += "=" * 40 + "\n\n"
        
        content += "📈 Detection Counts:\n"
        for det_type, count in detection_counts.items():
            percentage = (count / total * 100) if total > 0 else 0
            content += f"   {det_type}: {count} ({percentage:.1f}%)\n"
        
        content += f"\n📊 Total Detections: {total}\n\n"
        
        # Add insights
        if detection_counts['Objects'] > 0:
            human_count = sum(1 for item in self.current_data.get('object', []) 
                            if item.get('object_name') == 'human')
            content += f"👥 Human Detections: {human_count}\n"
        
        if detection_counts['Anomalies'] > 0:
            high_threat = sum(1 for item in self.current_data.get('anomaly', [])
                            if item.get('threat_level') == 'HIGH')
            content += f"🚨 High Threat Anomalies: {high_threat}\n"
        
        overview_text.insert('1.0', content)
        overview_text.config(state='disabled')
    
    def create_details_view(self, parent):
        """Create detailed data view with comprehensive information"""
        details_frame = tk.Frame(parent, bg='white')
        details_frame.pack(fill='both', expand=True, padx=20, pady=20)

        # Create treeview for detailed data
        columns = ('Type', 'Timestamp', 'Value', 'Confidence', 'Details', 'Session')
        self.details_tree = ttk.Treeview(details_frame, columns=columns, show='headings', height=15)

        # Define headings and column widths
        column_config = {
            'Type': 100,
            'Timestamp': 150,
            'Value': 120,
            'Confidence': 80,
            'Details': 200,
            'Session': 120
        }

        for col, width in column_config.items():
            self.details_tree.heading(col, text=col)
            self.details_tree.column(col, width=width)

        # Add scrollbars
        v_scrollbar = ttk.Scrollbar(details_frame, orient='vertical', command=self.details_tree.yview)
        h_scrollbar = ttk.Scrollbar(details_frame, orient='horizontal', command=self.details_tree.xview)
        self.details_tree.configure(yscrollcommand=v_scrollbar.set, xscrollcommand=h_scrollbar.set)

        # Pack treeview and scrollbars
        self.details_tree.grid(row=0, column=0, sticky='nsew')
        v_scrollbar.grid(row=0, column=1, sticky='ns')
        h_scrollbar.grid(row=1, column=0, sticky='ew')

        # Configure grid weights
        details_frame.grid_rowconfigure(0, weight=1)
        details_frame.grid_columnconfigure(0, weight=1)

        # Populate with initial data
        self.populate_details_tree(self.details_tree)

    def populate_details_tree(self, tree):
        """Populate the details tree with current data"""
        try:
            # Clear existing items
            for item in tree.get_children():
                tree.delete(item)

            # Add data from all detection types
            for det_type, records in self.current_data.items():
                for record in records:
                    try:
                        # Format timestamp
                        timestamp = record.get('timestamp', '')
                        if isinstance(timestamp, str) and len(timestamp) > 19:
                            timestamp = timestamp[:19]  # Truncate to standard format

                        # Get main value based on detection type
                        if det_type == 'age':
                            value = record.get('age_range', record.get('age', 'Unknown'))
                            details = f"Age: {record.get('age', 'N/A')}, Category: {record.get('category', 'N/A')}"
                        elif det_type == 'object':
                            value = record.get('object_name', 'Unknown')
                            bbox = f"({record.get('bbox_x', 0)},{record.get('bbox_y', 0)},{record.get('bbox_w', 0)},{record.get('bbox_h', 0)})"
                            details = f"Bbox: {bbox}, Method: {record.get('detection_method', 'N/A')}"
                        elif det_type == 'expression':
                            value = record.get('expression', 'Unknown')
                            details = f"Coords: {record.get('coordinates', 'N/A')}, Time: {record.get('processing_time', 'N/A')}s"
                        elif det_type == 'anomaly':
                            value = record.get('anomaly_type', 'Unknown')
                            details = f"Threat: {record.get('threat_level', 'N/A')}, Desc: {record.get('description', 'N/A')[:50]}"
                        else:
                            value = 'Unknown'
                            details = 'N/A'

                        # Format confidence
                        confidence = record.get('confidence', 0)
                        if isinstance(confidence, (int, float)):
                            confidence_str = f"{float(confidence):.2%}"
                        else:
                            confidence_str = str(confidence)

                        # Get session ID (truncated)
                        session_id = record.get('session_id', 'Unknown')
                        if len(session_id) > 15:
                            session_id = session_id[-15:]  # Show last 15 characters

                        # Insert row
                        tree.insert('', 'end', values=(
                            det_type.title(),
                            timestamp,
                            value,
                            confidence_str,
                            details,
                            session_id
                        ))

                    except Exception as e:
                        print(f"❌ Error adding record to tree: {e}")
                        continue

            print(f"✅ Details tree populated with {len(tree.get_children())} records")

        except Exception as e:
            print(f"❌ Error populating details tree: {e}")

    def refresh_data(self):
        """Refresh dashboard data from enhanced database - REAL-TIME SYNC with visual feedback"""
        try:
            # Show visual feedback for refresh operation
            if hasattr(self, 'refresh_status_label'):
                self.refresh_status_label.config(text="🔄 Refreshing...", fg='#F39C12')
                if hasattr(self, 'root') and self.root:
                    self.root.update_idletasks()

            print("🔄 Refreshing dashboard data from LIVE database...")
            self.last_refresh_time = datetime.now()

            # Get time range
            start_time, end_time = self.get_time_range()
            print(f"📅 Time range: {start_time.strftime('%H:%M:%S')} to {end_time.strftime('%H:%M:%S')}")

            # Clear current data
            self.current_data = {}

            if self.improved_db:
                # Get data from improved database - SAME database as main application
                total_all_records = 0
                for det_type in self.detection_types:
                    if self.filter_vars[det_type].get():
                        try:
                            # Get ALL records first to see total count - INCREASED LIMIT
                            all_records = self.improved_db.get_recent_detections(det_type, limit=100000)
                            total_all_records += len(all_records)

                            # Filter by time range if not "All Time"
                            if self.time_var.get() != "All Time":
                                filtered_records = []
                                for record in all_records:
                                    try:
                                        # Parse timestamp from database
                                        if isinstance(record.get('timestamp'), str):
                                            record_time = datetime.fromisoformat(record['timestamp'].replace('Z', '+00:00'))
                                        else:
                                            record_time = record['timestamp']

                                        if start_time <= record_time <= end_time:
                                            filtered_records.append(record)
                                    except Exception as e:
                                        print(f"⚠️ Error parsing timestamp for {det_type}: {e}")
                                        # Include record if timestamp parsing fails
                                        filtered_records.append(record)

                                self.current_data[det_type] = filtered_records
                                print(f"📊 {det_type}: {len(filtered_records)}/{len(all_records)} records (filtered)")
                            else:
                                self.current_data[det_type] = all_records
                                print(f"📊 {det_type}: {len(all_records)} records (all time)")

                        except Exception as e:
                            print(f"❌ Error loading {det_type} data: {e}")
                            self.current_data[det_type] = []
                    else:
                        self.current_data[det_type] = []

                print(f"📊 TOTAL DATABASE RECORDS: {total_all_records}")

            else:
                print("❌ Improved database not available - cannot show real data")
                # Initialize empty data
                for det_type in self.detection_types:
                    self.current_data[det_type] = []

            # Update statistics with real data
            print("📊 Updating statistics...")
            self.update_statistics()

            # Update charts with new data
            print("📈 Updating charts...")
            self.update_charts()

            # Update details tree
            if hasattr(self, 'details_tree'):
                print("📋 Updating details tree...")
                self.populate_details_tree(self.details_tree)

            # Force UI update
            if hasattr(self, 'root') and self.root:
                self.root.update_idletasks()

            # Update status with detailed info and success indication
            total_filtered = sum(len(data) for data in self.current_data.values())
            if hasattr(self, 'refresh_status_label'):
                self.refresh_status_label.config(
                    text=f"✅ Last: {self.last_refresh_time.strftime('%H:%M:%S')} ({total_filtered:,} records)",
                    fg='#27AE60'  # Green color for success
                )

            # Show temporary success message
            if hasattr(self, 'root') and self.root:
                # Create temporary success indicator
                success_label = tk.Label(self.root, text="✅ Dashboard Refreshed Successfully!",
                                       bg='#D4EDDA', fg='#155724', font=('Arial', 10, 'bold'),
                                       relief='solid', bd=1, padx=10, pady=5)
                success_label.place(relx=0.5, rely=0.1, anchor='center')

                # Remove success message after 2 seconds
                self.root.after(2000, lambda: success_label.destroy() if success_label.winfo_exists() else None)

            print(f"✅ Dashboard refreshed: {total_filtered} filtered records displayed")

            # Verify data accuracy
            if self.improved_db:
                verification_counts = self.get_current_detection_counts()
                print(f"🔍 VERIFICATION - Total DB records: {verification_counts}")

            # Additional verification - check if we're missing recent data
            if verification_counts.get('total', 0) > total_filtered:
                missing_records = verification_counts.get('total', 0) - total_filtered
                print(f"⚠️ POTENTIAL ISSUE: {missing_records} records not shown in dashboard")
                print(f"   This could be due to time filtering or data format issues")

                # Show time range being used
                start_time, end_time = self.get_time_range()
                print(f"   Time filter: {start_time} to {end_time}")
                print(f"   Selected range: {self.time_var.get()}")
            else:
                print(f"✅ All database records are being displayed in dashboard")

        except Exception as e:
            print(f"❌ Error refreshing data: {e}")
            messagebox.showerror("Refresh Error", f"Failed to refresh data: {e}")

    def get_time_range(self):
        """Get start and end time based on selected range"""
        try:
            selected_range = self.time_var.get()
            hours = self.time_ranges.get(selected_range, 24)

            end_time = datetime.now()
            if hours == 0:  # All time
                start_time = datetime(2020, 1, 1)  # Far back start
            else:
                start_time = end_time - timedelta(hours=hours)

            return start_time, end_time

        except Exception as e:
            print(f"❌ Error getting time range: {e}")
            # Default to last 24 hours
            end_time = datetime.now()
            start_time = end_time - timedelta(hours=24)
            return start_time, end_time

    def update_statistics(self):
        """Update real-time statistics display with analytical data"""
        try:
            # Calculate comprehensive counts from REAL data
            counts = {
                'age': len(self.current_data.get('age', [])),
                'objects': len(self.current_data.get('object', [])),
                'expressions': len(self.current_data.get('expression', [])),
                'anomalies': len(self.current_data.get('anomaly', []))
            }

            # Improved confidence analysis with quality metrics
            confidence_analysis = self.analyze_confidence_distribution()
            avg_confidence = confidence_analysis['overall_average']

            # Calculate detection rates and trends
            detection_rates = self.calculate_detection_rates()

            # Analyze data quality and accuracy indicators
            quality_metrics = self.analyze_data_quality()

            # Update stat labels with REAL counts
            if 'age' in self.stat_labels:
                self.stat_labels['age'].config(text=str(counts['age']))
                print(f"📊 AGE STAT UPDATED: {counts['age']}")

            if 'objects' in self.stat_labels:
                self.stat_labels['objects'].config(text=str(counts['objects']))
                print(f"📊 OBJECT STAT UPDATED: {counts['objects']}")

            if 'expressions' in self.stat_labels:
                self.stat_labels['expressions'].config(text=str(counts['expressions']))
                print(f"📊 EXPRESSION STAT UPDATED: {counts['expressions']}")

            if 'anomalies' in self.stat_labels:
                self.stat_labels['anomalies'].config(text=str(counts['anomalies']))
                print(f"📊 ANOMALY STAT UPDATED: {counts['anomalies']}")

            if 'confidence' in self.stat_labels:
                self.stat_labels['confidence'].config(text=f"{avg_confidence:.1%}")
                print(f"📊 CONFIDENCE STAT UPDATED: {avg_confidence:.1%}")

            # Verify human vs person consistency
            if counts['objects'] > 0:
                object_records = self.current_data.get('object', [])
                human_count = sum(1 for record in object_records if record.get('object_name') == 'human')
                person_count = sum(1 for record in object_records if record.get('object_name') == 'person')
                print(f"🔍 HUMAN/PERSON CHECK: {human_count} humans, {person_count} persons")

            total_detections = sum(counts.values())
            print(f"✅ STATISTICS UPDATED - TOTAL: {total_detections} detections")
            print(f"   Age: {counts['age']}, Objects: {counts['objects']}, Expressions: {counts['expressions']}, Anomalies: {counts['anomalies']}")

        except Exception as e:
            print(f"❌ Error updating statistics: {e}")

    def analyze_confidence_distribution(self):
        """Analyze confidence distribution across all detection types"""
        try:
            analysis = {
                'overall_average': 0.0,
                'by_type': {},
                'distribution': {'high': 0, 'medium': 0, 'low': 0},
                'quality_score': 0.0
            }

            all_confidences = []

            for det_type, records in self.current_data.items():
                type_confidences = []
                for record in records:
                    confidence = record.get('confidence')
                    if confidence is not None:
                        try:
                            conf_val = float(confidence)
                            type_confidences.append(conf_val)
                            all_confidences.append(conf_val)
                        except:
                            continue

                if type_confidences:
                    analysis['by_type'][det_type] = {
                        'average': sum(type_confidences) / len(type_confidences),
                        'min': min(type_confidences),
                        'max': max(type_confidences),
                        'count': len(type_confidences)
                    }
                else:
                    analysis['by_type'][det_type] = {
                        'average': 0.0, 'min': 0.0, 'max': 0.0, 'count': 0
                    }

            if all_confidences:
                analysis['overall_average'] = sum(all_confidences) / len(all_confidences)

                # Categorize confidence levels
                for conf in all_confidences:
                    if conf >= 0.8:
                        analysis['distribution']['high'] += 1
                    elif conf >= 0.6:
                        analysis['distribution']['medium'] += 1
                    else:
                        analysis['distribution']['low'] += 1

                # Calculate quality score (percentage of high confidence detections)
                total = len(all_confidences)
                analysis['quality_score'] = (analysis['distribution']['high'] / total) * 100

            return analysis

        except Exception as e:
            print(f"❌ Error analyzing confidence: {e}")
            return {'overall_average': 0.0, 'by_type': {}, 'distribution': {'high': 0, 'medium': 0, 'low': 0}, 'quality_score': 0.0}

    def calculate_detection_rates(self):
        """Calculate detection rates and temporal patterns"""
        try:
            rates = {
                'detections_per_hour': {},
                'peak_hours': [],
                'temporal_distribution': {},
                'detection_frequency': 0.0
            }

            # Get time range
            start_time, end_time = self.get_time_range()
            time_span_hours = (end_time - start_time).total_seconds() / 3600

            if time_span_hours > 0:
                for det_type, records in self.current_data.items():
                    if records:
                        rates['detections_per_hour'][det_type] = len(records) / time_span_hours
                    else:
                        rates['detections_per_hour'][det_type] = 0.0

                # Calculate overall detection frequency
                total_detections = sum(len(records) for records in self.current_data.values())
                rates['detection_frequency'] = total_detections / time_span_hours

                # Analyze temporal distribution (by hour of day)
                hourly_counts = {}
                for records in self.current_data.values():
                    for record in records:
                        try:
                            timestamp_str = str(record['timestamp'])
                            if 'T' in timestamp_str:
                                record_time = datetime.fromisoformat(timestamp_str.replace('Z', '+00:00'))
                            else:
                                record_time = datetime.strptime(timestamp_str[:19], '%Y-%m-%d %H:%M:%S')

                            hour = record_time.hour
                            hourly_counts[hour] = hourly_counts.get(hour, 0) + 1
                        except:
                            continue

                rates['temporal_distribution'] = hourly_counts

                # Find peak hours (top 3 hours with most detections)
                if hourly_counts:
                    sorted_hours = sorted(hourly_counts.items(), key=lambda x: x[1], reverse=True)
                    rates['peak_hours'] = [f"{hour:02d}:00" for hour, count in sorted_hours[:3]]

            return rates

        except Exception as e:
            print(f"❌ Error calculating detection rates: {e}")
            return {'detections_per_hour': {}, 'peak_hours': [], 'temporal_distribution': {}, 'detection_frequency': 0.0}

    def analyze_data_quality(self):
        """Analyze data quality and accuracy indicators"""
        try:
            quality = {
                'completeness_score': 0.0,
                'consistency_score': 0.0,
                'accuracy_indicators': {},
                'data_integrity': 'Good',
                'recommendations': []
            }

            total_records = sum(len(records) for records in self.current_data.values())

            if total_records > 0:
                # Completeness: Check for missing data fields
                complete_records = 0
                for records in self.current_data.values():
                    for record in records:
                        required_fields = ['timestamp', 'confidence']
                        if all(field in record and record[field] is not None for field in required_fields):
                            complete_records += 1

                quality['completeness_score'] = (complete_records / total_records) * 100

                # Consistency: Check for data format consistency
                consistent_records = 0
                for records in self.current_data.values():
                    for record in records:
                        try:
                            # Check timestamp format
                            timestamp_str = str(record.get('timestamp', ''))
                            if timestamp_str:
                                if 'T' in timestamp_str:
                                    datetime.fromisoformat(timestamp_str.replace('Z', '+00:00'))
                                else:
                                    datetime.strptime(timestamp_str[:19], '%Y-%m-%d %H:%M:%S')

                            # Check confidence range
                            confidence = float(record.get('confidence', 0))
                            if 0.0 <= confidence <= 1.0:
                                consistent_records += 1
                        except:
                            continue

                quality['consistency_score'] = (consistent_records / total_records) * 100

                # Accuracy indicators
                confidence_analysis = self.analyze_confidence_distribution()
                quality['accuracy_indicators'] = {
                    'high_confidence_percentage': confidence_analysis['quality_score'],
                    'average_confidence': confidence_analysis['overall_average'],
                    'detection_distribution': confidence_analysis['distribution']
                }

                # Data integrity assessment
                if quality['completeness_score'] >= 95 and quality['consistency_score'] >= 95:
                    quality['data_integrity'] = 'Excellent'
                elif quality['completeness_score'] >= 85 and quality['consistency_score'] >= 85:
                    quality['data_integrity'] = 'Good'
                elif quality['completeness_score'] >= 70 and quality['consistency_score'] >= 70:
                    quality['data_integrity'] = 'Fair'
                else:
                    quality['data_integrity'] = 'Poor'

                # Generate recommendations
                if quality['completeness_score'] < 90:
                    quality['recommendations'].append("Improve data completeness by ensuring all required fields are captured")

                if quality['consistency_score'] < 90:
                    quality['recommendations'].append("Enhance data validation to improve consistency")

                if confidence_analysis['quality_score'] < 70:
                    quality['recommendations'].append("Consider adjusting detection thresholds to improve confidence scores")

                if not quality['recommendations']:
                    quality['recommendations'].append("Data quality is excellent - maintain current standards")

            return quality

        except Exception as e:
            print(f"❌ Error analyzing data quality: {e}")
            return {'completeness_score': 0.0, 'consistency_score': 0.0, 'accuracy_indicators': {}, 'data_integrity': 'Unknown', 'recommendations': []}

    def update_charts(self):
        """Update charts with new data"""
        try:
            print("🔄 Updating charts with new data...")

            # Check if we have a matplotlib chart canvas to update
            if hasattr(self, 'chart_canvas'):
                # Destroy existing chart
                self.chart_canvas.get_tk_widget().destroy()

            # Find the overview frame and recreate the chart
            if hasattr(self, 'root') and self.root:
                # Find the notebook widget
                for widget in self.root.winfo_children():
                    if isinstance(widget, tk.Frame):
                        for subwidget in widget.winfo_children():
                            if isinstance(subwidget, ttk.Notebook):
                                # Get the overview tab
                                overview_frame = subwidget.nametowidget(subwidget.tabs()[0])

                                # Clear the frame
                                for child in overview_frame.winfo_children():
                                    child.destroy()

                                # Recreate the chart
                                self.create_overview_chart(overview_frame)
                                print("✅ Charts updated successfully")
                                return

            print("⚠️ Chart update completed (no chart canvas found)")

        except Exception as e:
            print(f"❌ Error updating charts: {e}")
            # Fallback: just log the error and continue
        except Exception as e:
            print(f"❌ Error updating charts: {e}")

    def toggle_auto_refresh(self):
        """Toggle auto-refresh functionality"""
        try:
            self.auto_refresh = self.auto_refresh_var.get()

            if self.auto_refresh:
                self.start_auto_refresh()
                print("✅ Auto-refresh enabled")
            else:
                self.stop_auto_refresh()
                print("⏹️ Auto-refresh disabled")

        except Exception as e:
            print(f"❌ Error toggling auto-refresh: {e}")

    def start_auto_refresh(self):
        """Start auto-refresh timer"""
        try:
            if self.auto_refresh and self.root:
                self.refresh_data()
                self.after_id = self.root.after(self.refresh_interval, self.start_auto_refresh)
        except Exception as e:
            print(f"❌ Error in auto-refresh: {e}")

    def stop_auto_refresh(self):
        """Stop auto-refresh timer"""
        try:
            if self.after_id and self.root:
                self.root.after_cancel(self.after_id)
                self.after_id = None
        except Exception as e:
            print(f"❌ Error stopping auto-refresh: {e}")

    def update_time(self):
        """Update time display in header"""
        try:
            if self.root:
                current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                self.time_label.config(text=current_time)
                self.root.after(1000, self.update_time)
        except Exception as e:
            print(f"❌ Error updating time: {e}")

    # Improved button wrapper methods for better user experience
    def improved_refresh_data(self):
        """Improved refresh with better user feedback"""
        try:
            print("🔄 Improved refresh initiated...")
            self.refresh_data()
            return True  # Indicate success for button feedback
        except Exception as e:
            print(f"❌ Improved refresh failed: {e}")
            raise e  # Re-raise for button error handling

    def improved_generate_pdf_report(self):
        """Improved PDF generation with progress feedback"""
        try:
            print("📄 Improved PDF generation initiated...")
            self.generate_pdf_report()
            return True
        except Exception as e:
            print(f"❌ Improved PDF generation failed: {e}")
            raise e

    def improved_export_csv(self):
        """Improved CSV export with progress feedback"""
        try:
            print("📊 Improved CSV export initiated...")
            self.export_csv()
            return True
        except Exception as e:
            print(f"❌ Improved CSV export failed: {e}")
            raise e

    def improved_delete_records(self):
        """Improved delete records with confirmation"""
        try:
            print("🗑️ Improved delete records initiated...")
            self.delete_records()
            return True
        except Exception as e:
            print(f"❌ Improved delete records failed: {e}")
            raise e

    def improved_clear_old_data(self):
        """Improved clear old data with confirmation"""
        try:
            print("🧹 Improved clear old data initiated...")
            self.clear_old_data()
            return True
        except Exception as e:
            print(f"❌ Improved clear old data failed: {e}")
            raise e

    def export_csv(self):
        """Export current data to CSV with comprehensive formatting"""
        try:
            if not self.current_data or not any(self.current_data.values()):
                messagebox.showwarning("No Data", "No data available to export.")
                return

            # Show progress dialog
            progress_dialog = self.show_progress_dialog("Exporting CSV Data...")

            # Get filename
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            exports_dir = "exports"
            os.makedirs(exports_dir, exist_ok=True)
            filename = os.path.join(exports_dir, f"AI_Detection_Data_{timestamp}.csv")

            # Prepare comprehensive data for export
            export_data = []

            for det_type, records in self.current_data.items():
                for record in records:
                    # Create standardized row format
                    row = {
                        'Detection_Type': det_type.title(),
                        'Timestamp': record.get('timestamp', ''),
                        'Confidence': record.get('confidence', ''),
                        'Session_ID': record.get('session_id', ''),
                        'Model_Used': record.get('model_used', ''),
                    }

                    # Add type-specific fields
                    if det_type == 'age':
                        row.update({
                            'Age': record.get('age', ''),
                            'Age_Range': record.get('age_range', ''),
                            'Category': record.get('category', ''),
                            'Quality_Score': record.get('quality_score', ''),
                            'Stability_Score': record.get('stability_score', ''),
                            'Face_Coordinates': record.get('face_bbox', ''),
                            'Processing_Time': record.get('processing_time', '')
                        })
                    elif det_type == 'object':
                        row.update({
                            'Object_Name': record.get('object_name', ''),
                            'Bbox_X': record.get('bbox_x', ''),
                            'Bbox_Y': record.get('bbox_y', ''),
                            'Bbox_W': record.get('bbox_w', ''),
                            'Bbox_H': record.get('bbox_h', ''),
                            'Detection_Method': record.get('detection_method', ''),
                            'Is_Anomaly': record.get('is_anomaly', '')
                        })
                    elif det_type == 'expression':
                        row.update({
                            'Expression': record.get('expression', ''),
                            'Coordinates': record.get('coordinates', ''),
                            'Processing_Time': record.get('processing_time', '')
                        })
                    elif det_type == 'anomaly':
                        row.update({
                            'Anomaly_Type': record.get('anomaly_type', ''),
                            'Threat_Level': record.get('threat_level', ''),
                            'Description': record.get('description', ''),
                            'Recording_Path': record.get('recording_path', ''),
                            'Report_Path': record.get('report_path', '')
                        })

                    export_data.append(row)

            # Write to CSV
            if export_data:
                # Get all possible field names
                all_fields = set()
                for row in export_data:
                    all_fields.update(row.keys())

                fieldnames = ['Detection_Type', 'Timestamp', 'Confidence', 'Session_ID', 'Model_Used'] + \
                           sorted([f for f in all_fields if f not in ['Detection_Type', 'Timestamp', 'Confidence', 'Session_ID', 'Model_Used']])

                with open(filename, 'w', newline='', encoding='utf-8') as csvfile:
                    writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
                    writer.writeheader()
                    writer.writerows(export_data)

                # Close progress dialog
                if hasattr(self, 'progress_dialog') and self.progress_dialog:
                    self.progress_dialog.destroy()

                # Show success message with analytics
                total_records = len(export_data)
                type_counts = {}
                for det_type in self.detection_types:
                    type_counts[det_type] = len(self.current_data.get(det_type, []))

                messagebox.showinfo("Export Successful",
                                  f"✅ CSV Export Completed!\n\n"
                                  f"📄 File: {filename}\n"
                                  f"📊 Total Records: {total_records:,}\n\n"
                                  f"📈 Breakdown:\n"
                                  f"• Age Detections: {type_counts['age']:,}\n"
                                  f"• Object Detections: {type_counts['object']:,}\n"
                                  f"• Expression Detections: {type_counts['expression']:,}\n"
                                  f"• Anomaly Detections: {type_counts['anomaly']:,}\n\n"
                                  f"🔍 Data includes all detection metadata,\n"
                                  f"coordinates, confidence scores, and timestamps.")

                print(f"✅ CSV export completed: {filename}")
                print(f"📊 Exported {total_records} records")

            else:
                messagebox.showwarning("No Data", "No valid data found to export.")

        except Exception as e:
            print(f"❌ Error exporting CSV: {e}")
            messagebox.showerror("Export Error", f"Failed to export CSV: {e}")

    def export_json(self):
        """Export current data to JSON format"""
        try:
            if not self.current_data or not any(self.current_data.values()):
                messagebox.showwarning("No Data", "No data available to export.")
                return

            # Show progress dialog
            progress_dialog = self.show_progress_dialog("Exporting JSON Data...")

            # Get filename
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            exports_dir = "exports"
            os.makedirs(exports_dir, exist_ok=True)
            filename = os.path.join(exports_dir, f"AI_Detection_Data_{timestamp}.json")

            # Prepare comprehensive export structure
            export_structure = {
                'metadata': {
                    'export_timestamp': datetime.now().isoformat(),
                    'time_range': {
                        'start': self.get_time_range()[0].isoformat(),
                        'end': self.get_time_range()[1].isoformat(),
                        'selected_range': self.time_var.get()
                    },
                    'total_records': sum(len(data) for data in self.current_data.values()),
                    'system_info': {
                        'database_path': self.enhanced_db.db_path if self.enhanced_db else 'Unknown',
                        'session_id': self.enhanced_db.session_id if self.enhanced_db else 'Unknown'
                    }
                },
                'analytics': {
                    'confidence_analysis': self.analyze_confidence_distribution(),
                    'detection_rates': self.calculate_detection_rates(),
                    'quality_metrics': self.analyze_data_quality()
                },
                'detection_data': {}
            }

            # Add detection data
            for det_type, records in self.current_data.items():
                export_structure['detection_data'][det_type] = {
                    'count': len(records),
                    'records': records
                }

            # Write to JSON
            with open(filename, 'w', encoding='utf-8') as jsonfile:
                import json
                json.dump(export_structure, jsonfile, indent=2, default=str, ensure_ascii=False)

            # Close progress dialog
            if hasattr(self, 'progress_dialog') and self.progress_dialog:
                self.progress_dialog.destroy()

            # Show success message
            total_records = export_structure['metadata']['total_records']
            messagebox.showinfo("JSON Export Successful",
                              f"✅ JSON Export Completed!\n\n"
                              f"📄 File: {filename}\n"
                              f"📊 Total Records: {total_records:,}\n\n"
                              f"📈 Includes:\n"
                              f"• Complete detection data\n"
                              f"• Analytics and insights\n"
                              f"• Metadata and timestamps\n"
                              f"• Quality metrics\n\n"
                              f"🔍 Perfect for data analysis and\n"
                              f"integration with other systems.")

            print(f"✅ JSON export completed: {filename}")

        except Exception as e:
            print(f"❌ Error exporting JSON: {e}")
            messagebox.showerror("Export Error", f"Failed to export JSON: {e}")

    def delete_records(self):
        """Delete selected records with confirmation and preview"""
        try:
            if not self.enhanced_db:
                messagebox.showerror("Database Error", "Database connection not available.")
                return

            # Create enhanced delete dialog with better size
            delete_dialog = tk.Toplevel(self.root)
            delete_dialog.title("🗑️ Delete Detection Records - Enhanced Interface")
            delete_dialog.geometry("800x700")
            delete_dialog.configure(bg='white')
            delete_dialog.transient(self.root)
            delete_dialog.grab_set()
            delete_dialog.resizable(True, True)

            # Center the dialog
            delete_dialog.update_idletasks()
            x = (delete_dialog.winfo_screenwidth() // 2) - (800 // 2)
            y = (delete_dialog.winfo_screenheight() // 2) - (700 // 2)
            delete_dialog.geometry(f"800x700+{x}+{y}")

            # Header
            header_frame = tk.Frame(delete_dialog, bg='#E74C3C', height=60)
            header_frame.pack(fill='x')
            header_frame.pack_propagate(False)

            tk.Label(header_frame, text="🗑️ Delete Detection Records",
                    font=('Arial', 16, 'bold'), bg='#E74C3C', fg='white').pack(pady=15)

            # Content frame
            content_frame = tk.Frame(delete_dialog, bg='white')
            content_frame.pack(fill='both', expand=True, padx=20, pady=20)

            # Instructions and warning
            instruction_frame = tk.Frame(content_frame, bg='#E8F5E8', relief='solid', bd=1)
            instruction_frame.pack(fill='x', pady=(0, 10))

            tk.Label(instruction_frame, text="📋 HOW TO DELETE RECORDS:",
                    font=('Arial', 11, 'bold'), bg='#E8F5E8', fg='#2E7D32').pack(pady=(10, 5))

            instructions = [
                "1. ✅ Select detection types to delete (Age, Object, Expression, Anomaly)",
                "2. 🎯 Set confidence threshold: 0.0 = delete ALL, 0.8 = delete low-confidence only",
                "3. ⏰ Choose time range: All Time = delete from entire database",
                "4. 👁️ Check preview to see EXACT count of records to be deleted",
                "5. 🗑️ Click 'Delete Selected' and confirm to execute deletion"
            ]

            for instruction in instructions:
                tk.Label(instruction_frame, text=instruction, font=('Arial', 9),
                        bg='#E8F5E8', fg='#1B5E20').pack(anchor='w', padx=15, pady=1)

            tk.Label(instruction_frame, text="", bg='#E8F5E8').pack(pady=5)  # Spacer

            # Warning message
            warning_frame = tk.Frame(content_frame, bg='#FFF3CD', relief='solid', bd=1)
            warning_frame.pack(fill='x', pady=(0, 15))

            tk.Label(warning_frame, text="⚠️ WARNING: This action cannot be undone!",
                    font=('Arial', 12, 'bold'), bg='#FFF3CD', fg='#856404').pack(pady=10)

            # Selection options
            selection_frame = tk.LabelFrame(content_frame, text="🎯 Select Records to Delete",
                                          bg='white', fg='#2C3E50', font=('Arial', 11, 'bold'))
            selection_frame.pack(fill='x', pady=(0, 15))

            # Detection type checkboxes - Get ACTUAL database counts
            delete_vars = {}
            actual_counts = {}

            print("🔍 Getting actual database counts for delete dialog...")
            for det_type in self.detection_types:
                try:
                    # Get actual count from database, not filtered current_data
                    all_records = self.enhanced_db.get_recent_detections(det_type, limit=50000)
                    actual_counts[det_type] = len(all_records)
                    print(f"📊 {det_type}: {actual_counts[det_type]:,} records in database")
                except Exception as e:
                    print(f"❌ Error getting {det_type} count: {e}")
                    actual_counts[det_type] = 0

                var = tk.BooleanVar()
                delete_vars[det_type] = var
                count = actual_counts[det_type]
                cb = tk.Checkbutton(selection_frame, text=f"{det_type.title()} ({count:,} records)",
                                  variable=var, bg='white', font=('Arial', 10))
                cb.pack(anchor='w', padx=10, pady=5)

            # Time range selection
            time_frame = tk.LabelFrame(content_frame, text="📅 Time Range",
                                     bg='white', fg='#2C3E50', font=('Arial', 11, 'bold'))
            time_frame.pack(fill='x', pady=(0, 15))

            time_var = tk.StringVar(value="Selected Time Range")
            time_options = ["Last Hour", "Last 6 Hours", "Last 24 Hours", "Last 7 Days", "All Time", "Selected Time Range"]
            time_combo = ttk.Combobox(time_frame, textvariable=time_var, values=time_options, state='readonly')
            time_combo.pack(padx=10, pady=10, fill='x')

            # Confidence threshold with better explanation
            conf_frame = tk.LabelFrame(content_frame, text="🎯 Confidence Threshold",
                                     bg='white', fg='#2C3E50', font=('Arial', 11, 'bold'))
            conf_frame.pack(fill='x', pady=(0, 15))

            # Add explanation
            explanation_frame = tk.Frame(conf_frame, bg='white')
            explanation_frame.pack(fill='x', padx=10, pady=5)

            tk.Label(explanation_frame, text="💡 Set to 0.0 to delete ALL selected records",
                    font=('Arial', 9, 'bold'), bg='white', fg='#E74C3C').pack(anchor='w')
            tk.Label(explanation_frame, text="💡 Set to 0.8 to delete only low-confidence records (< 80%)",
                    font=('Arial', 9), bg='white', fg='#27AE60').pack(anchor='w')

            conf_var = tk.DoubleVar(value=0.0)
            conf_scale = tk.Scale(conf_frame, from_=0.0, to=1.0, resolution=0.1, orient='horizontal',
                                variable=conf_var, bg='white', font=('Arial', 9))
            conf_scale.pack(padx=10, pady=5, fill='x')

            # Dynamic label that updates with threshold
            conf_label = tk.Label(conf_frame, text="Delete records with confidence below 0.0 (ALL RECORDS)",
                    font=('Arial', 9, 'bold'), bg='white', fg='#E74C3C')
            conf_label.pack(padx=10, pady=(0, 10))

            def update_conf_label(*args):
                threshold = conf_var.get()
                if threshold == 0.0:
                    conf_label.config(text="Delete ALL selected records (no confidence filter)",
                                    fg='#E74C3C')
                else:
                    conf_label.config(text=f"Delete records with confidence below {threshold:.1f} ({threshold*100:.0f}%)",
                                    fg='#F39C12')

            conf_var.trace_add('write', update_conf_label)

            # Preview area
            preview_frame = tk.LabelFrame(content_frame, text="👁️ Deletion Preview",
                                        bg='white', fg='#2C3E50', font=('Arial', 11, 'bold'))
            preview_frame.pack(fill='both', expand=True, pady=(0, 15))

            preview_text = tk.Text(preview_frame, height=8, font=('Courier', 9), bg='#F8F9FA',
                                 fg='#2C3E50', wrap='word', state='disabled')
            preview_text.pack(fill='both', expand=True, padx=10, pady=10)

            def update_preview():
                """Update deletion preview with ACCURATE database counts"""
                try:
                    preview_text.config(state='normal')
                    preview_text.delete('1.0', 'end')

                    total_to_delete = 0
                    preview_content = "🗑️ DELETION PREVIEW (REAL-TIME):\n" + "="*50 + "\n\n"

                    # Get actual counts from database for accurate preview
                    for det_type in self.detection_types:
                        if delete_vars[det_type].get():
                            try:
                                # Get ACTUAL count that would be deleted
                                table_map = {
                                    'age': 'age_detections',
                                    'object': 'object_detections',
                                    'expression': 'expression_detections',
                                    'anomaly': 'anomaly_detections'
                                }
                                table = table_map.get(det_type)

                                if table and self.enhanced_db:
                                    import sqlite3
                                    conn = sqlite3.connect(self.enhanced_db.db_path)
                                    cursor = conn.cursor()

                                    # Build query to count records that would be deleted
                                    query = f"SELECT COUNT(*) FROM {table} WHERE 1=1"
                                    params = []

                                    conf_threshold = conf_var.get()
                                    if conf_threshold > 0:
                                        query += " AND confidence < ?"
                                        params.append(conf_threshold)

                                    # Add time filter if needed
                                    time_range = time_var.get()
                                    if time_range != "All Time" and time_range != "Selected Time Range":
                                        time_ranges = {
                                            "Last Hour": 1,
                                            "Last 6 Hours": 6,
                                            "Last 24 Hours": 24,
                                            "Last 7 Days": 168
                                        }
                                        hours = time_ranges.get(time_range, 24)
                                        cutoff_time = datetime.now() - timedelta(hours=hours)
                                        query += " AND timestamp >= ?"
                                        params.append(cutoff_time)

                                    cursor.execute(query, params)
                                    actual_count = cursor.fetchone()[0]
                                    conn.close()

                                    total_to_delete += actual_count

                                    if conf_threshold > 0:
                                        preview_content += f"📊 {det_type.title()}: {actual_count:,} records (confidence < {conf_threshold:.1f})\n"
                                    else:
                                        preview_content += f"📊 {det_type.title()}: {actual_count:,} records (ALL)\n"
                                else:
                                    preview_content += f"📊 {det_type.title()}: 0 records (no database connection)\n"

                            except Exception as e:
                                print(f"❌ Error getting accurate count for {det_type}: {e}")
                                preview_content += f"📊 {det_type.title()}: Error getting count\n"

                    preview_content += f"\n🎯 TOTAL TO DELETE: {total_to_delete:,} records\n\n"

                    if total_to_delete > 0:
                        preview_content += "⚠️ This action will permanently remove these records\n"
                        preview_content += "from the database and cannot be undone!\n\n"
                        preview_content += "💡 Consider exporting data before deletion.\n\n"
                        preview_content += "✅ These are EXACT counts from the database."
                    else:
                        preview_content += "ℹ️ No records match the current selection criteria.\n\n"
                        if conf_var.get() > 0:
                            preview_content += f"💡 Try setting confidence to 0.0 to delete ALL selected records."

                    preview_text.insert('1.0', preview_content)
                    preview_text.config(state='disabled')

                except Exception as e:
                    print(f"❌ Error updating preview: {e}")

            # Bind update events
            for var in delete_vars.values():
                var.trace_add('write', lambda *args: update_preview())
            time_var.trace_add('write', lambda *args: update_preview())
            conf_var.trace_add('write', lambda *args: update_preview())

            # Initial preview
            update_preview()

            # Buttons
            button_frame = tk.Frame(content_frame, bg='white')
            button_frame.pack(fill='x', pady=10)

            def confirm_deletion():
                """Confirm and execute deletion with proper database operations"""
                try:
                    # Check if any detection types are selected
                    selected_types = [det_type for det_type in self.detection_types if delete_vars[det_type].get()]

                    if not selected_types:
                        messagebox.showwarning("No Selection", "Please select at least one detection type to delete.")
                        return

                    # Calculate estimated records to delete
                    estimated_total = 0
                    for det_type in selected_types:
                        count = actual_counts.get(det_type, 0)
                        conf_threshold = conf_var.get()
                        if conf_threshold > 0:
                            estimated_total += int(count * conf_threshold)  # Rough estimate
                        else:
                            estimated_total += count

                    # Final confirmation with detailed info
                    time_range = time_var.get()
                    conf_threshold = conf_var.get()

                    confirm_message = f"⚠️ CONFIRM DELETION\n\n"
                    confirm_message += f"Selected Types: {', '.join([t.title() for t in selected_types])}\n"
                    confirm_message += f"Time Range: {time_range}\n"
                    confirm_message += f"Confidence Filter: {'< ' + str(conf_threshold) if conf_threshold > 0 else 'All records'}\n"
                    confirm_message += f"Estimated Records: ~{estimated_total:,}\n\n"
                    confirm_message += f"This action cannot be undone!\n\n"
                    confirm_message += f"Continue with deletion?"

                    confirm = messagebox.askyesno("Confirm Deletion", confirm_message)

                    if not confirm:
                        return

                    # Show progress
                    self.show_progress_dialog("Deleting Records...")

                    # Perform deletion with proper database operations
                    deleted_count = 0
                    deletion_details = []

                    for det_type in selected_types:
                        try:
                            # Get table name
                            table_map = {
                                'age': 'age_detections',
                                'object': 'object_detections',
                                'expression': 'expression_detections',
                                'anomaly': 'anomaly_detections'
                            }
                            table = table_map.get(det_type)

                            if table:
                                # Build deletion query
                                query = f"DELETE FROM {table} WHERE 1=1"
                                params = []

                                # Add confidence filter
                                if conf_threshold > 0:
                                    query += " AND confidence < ?"
                                    params.append(conf_threshold)

                                # Add time filter if needed
                                if time_range != "All Time" and time_range != "Selected Time Range":
                                    time_ranges = {
                                        "Last Hour": 1,
                                        "Last 6 Hours": 6,
                                        "Last 24 Hours": 24,
                                        "Last 7 Days": 168
                                    }
                                    hours = time_ranges.get(time_range, 24)
                                    cutoff_time = datetime.now() - timedelta(hours=hours)
                                    query += " AND timestamp >= ?"
                                    params.append(cutoff_time)

                                # Execute deletion
                                import sqlite3
                                conn = sqlite3.connect(self.enhanced_db.db_path)
                                cursor = conn.cursor()

                                print(f"🗑️ Executing deletion for {det_type}: {query}")
                                print(f"   Parameters: {params}")

                                cursor.execute(query, params)
                                type_deleted = cursor.rowcount
                                deleted_count += type_deleted
                                deletion_details.append(f"{det_type.title()}: {type_deleted:,} records")

                                conn.commit()
                                conn.close()

                                print(f"✅ Deleted {type_deleted:,} {det_type} records")

                        except Exception as e:
                            print(f"❌ Error deleting {det_type} records: {e}")
                            deletion_details.append(f"{det_type.title()}: Error - {str(e)}")

                    # Close progress dialog
                    if hasattr(self, 'progress_dialog') and self.progress_dialog:
                        self.progress_dialog.destroy()

                    # Close delete dialog
                    delete_dialog.destroy()

                    # Show detailed success message
                    success_message = f"✅ DELETION COMPLETED!\n\n"
                    success_message += f"Total Records Deleted: {deleted_count:,}\n\n"
                    success_message += "Details:\n"
                    for detail in deletion_details:
                        success_message += f"• {detail}\n"
                    success_message += f"\n📊 Database has been updated.\n"
                    success_message += f"🔄 Dashboard will refresh automatically."

                    messagebox.showinfo("Deletion Complete", success_message)

                    # Refresh dashboard data to reflect changes
                    print("🔄 Refreshing dashboard after deletion...")
                    self.refresh_data()

                except Exception as e:
                    print(f"❌ Error during deletion: {e}")
                    messagebox.showerror("Deletion Error", f"Failed to delete records:\n\n{e}")

                    # Close progress dialog on error
                    if hasattr(self, 'progress_dialog') and self.progress_dialog:
                        self.progress_dialog.destroy()

            # Enhanced delete and cancel buttons
            delete_btn = EnhancedButton(
                button_frame,
                text="🗑️ Delete Selected",
                command=confirm_deletion,
                bg_color='#E74C3C',
                width=15,
                tooltip="Execute deletion with current filters\n🚨 THIS ACTION CANNOT BE UNDONE!",
                loading_text="🗑️ Deleting...",
                success_text="✅ Deleted",
                confirm_action=True,
                confirm_message="🚨 PERMANENT DELETION WARNING!\n\nAre you sure you want to PERMANENTLY DELETE the selected records?\n\n⚠️ THIS ACTION CANNOT BE UNDONE!\n⚠️ DELETED DATA CANNOT BE RECOVERED!\n\nRecords will be permanently removed from the database.\nMake sure you have backups if needed.\n\nProceed with deletion?"
            )
            delete_btn.pack(side='left', padx=5)

            cancel_btn = EnhancedButton(
                button_frame,
                text="❌ Cancel",
                command=delete_dialog.destroy,
                bg_color='#95A5A6',
                width=15,
                tooltip="Close deletion dialog without making changes"
            )
            cancel_btn.pack(side='right', padx=5)

        except Exception as e:
            print(f"❌ Error opening delete dialog: {e}")
            messagebox.showerror("Delete Error", f"Failed to open delete dialog: {e}")

    def clear_old_data(self):
        """Clear old data with user-specified age threshold"""
        try:
            if not self.enhanced_db:
                messagebox.showerror("Database Error", "Database connection not available.")
                return

            # Ask for age threshold
            age_dialog = tk.Toplevel(self.root)
            age_dialog.title("🧹 Clear Old Data")
            age_dialog.geometry("400x300")
            age_dialog.configure(bg='white')
            age_dialog.transient(self.root)
            age_dialog.grab_set()

            # Center the dialog
            age_dialog.update_idletasks()
            x = (age_dialog.winfo_screenwidth() // 2) - (400 // 2)
            y = (age_dialog.winfo_screenheight() // 2) - (300 // 2)
            age_dialog.geometry(f"400x300+{x}+{y}")

            # Header
            header_frame = tk.Frame(age_dialog, bg='#95A5A6', height=60)
            header_frame.pack(fill='x')
            header_frame.pack_propagate(False)

            tk.Label(header_frame, text="🧹 Clear Old Data",
                    font=('Arial', 16, 'bold'), bg='#95A5A6', fg='white').pack(pady=15)

            # Content
            content_frame = tk.Frame(age_dialog, bg='white')
            content_frame.pack(fill='both', expand=True, padx=20, pady=20)

            tk.Label(content_frame, text="Select the age threshold for data cleanup:",
                    font=('Arial', 12), bg='white', fg='#2C3E50').pack(pady=10)

            # Age options
            age_var = tk.IntVar(value=30)
            age_options = [
                (7, "1 week"),
                (30, "1 month"),
                (90, "3 months"),
                (180, "6 months"),
                (365, "1 year")
            ]

            for days, label in age_options:
                rb = tk.Radiobutton(content_frame, text=f"Older than {label} ({days} days)",
                                  variable=age_var, value=days, bg='white', font=('Arial', 10))
                rb.pack(anchor='w', pady=5)

            # Custom option
            custom_frame = tk.Frame(content_frame, bg='white')
            custom_frame.pack(fill='x', pady=10)

            tk.Radiobutton(custom_frame, text="Custom:", variable=age_var, value=0,
                          bg='white', font=('Arial', 10)).pack(side='left')

            custom_var = tk.IntVar(value=30)
            custom_entry = tk.Entry(custom_frame, textvariable=custom_var, width=10, font=('Arial', 10))
            custom_entry.pack(side='left', padx=5)

            tk.Label(custom_frame, text="days", bg='white', font=('Arial', 10)).pack(side='left')

            # Buttons
            button_frame = tk.Frame(content_frame, bg='white')
            button_frame.pack(fill='x', pady=20)

            def execute_cleanup():
                """Execute the cleanup operation"""
                try:
                    days = age_var.get() if age_var.get() > 0 else custom_var.get()

                    if days <= 0:
                        messagebox.showwarning("Invalid Input", "Please enter a valid number of days.")
                        return

                    # Confirm action
                    confirm = messagebox.askyesno("Confirm Cleanup",
                                                f"Delete all records older than {days} days?\n\n"
                                                f"This action cannot be undone!")

                    if not confirm:
                        return

                    # Show progress
                    progress_dialog = self.show_progress_dialog("Cleaning Old Data...")

                    # Perform cleanup
                    deleted_count = self.enhanced_db.clear_old_data(days)

                    # Close dialogs
                    if hasattr(self, 'progress_dialog') and self.progress_dialog:
                        self.progress_dialog.destroy()
                    age_dialog.destroy()

                    # Show result
                    messagebox.showinfo("Cleanup Complete",
                                      f"✅ Cleanup completed!\n\n"
                                      f"🗑️ Deleted {deleted_count:,} old records\n"
                                      f"📅 Older than {days} days\n\n"
                                      f"🔄 Dashboard will refresh automatically.")

                    # Refresh dashboard
                    self.refresh_data()

                except Exception as e:
                    print(f"❌ Error during cleanup: {e}")
                    messagebox.showerror("Cleanup Error", f"Failed to cleanup data: {e}")

            # Enhanced cleanup and cancel buttons
            cleanup_btn = EnhancedButton(
                button_frame,
                text="🧹 Clean Data",
                command=execute_cleanup,
                bg_color='#95A5A6',
                width=12,
                tooltip="Remove old detection records\n🚨 THIS ACTION CANNOT BE UNDONE!",
                loading_text="🧹 Cleaning...",
                success_text="✅ Cleaned",
                confirm_action=True,
                confirm_message="🚨 PERMANENT DATA REMOVAL WARNING!\n\nAre you sure you want to PERMANENTLY REMOVE old data?\n\n⚠️ THIS ACTION CANNOT BE UNDONE!\n⚠️ DELETED DATA CANNOT BE RECOVERED!\n\nOld records will be permanently deleted from the database.\nThis helps maintain performance but removes historical data.\n\nProceed with cleanup?"
            )
            cleanup_btn.pack(side='left', padx=5)

            cancel_cleanup_btn = EnhancedButton(
                button_frame,
                text="❌ Cancel",
                command=age_dialog.destroy,
                bg_color='#BDC3C7',
                width=12,
                tooltip="Close cleanup dialog without making changes"
            )
            cancel_cleanup_btn.pack(side='right', padx=5)

        except Exception as e:
            print(f"❌ Error opening cleanup dialog: {e}")
            messagebox.showerror("Cleanup Error", f"Failed to open cleanup dialog: {e}")

    def show_progress_dialog(self, message):
        """Show progress dialog"""
        try:
            progress_dialog = tk.Toplevel(self.root)
            progress_dialog.title("Processing...")
            progress_dialog.geometry("300x100")
            progress_dialog.configure(bg='white')
            progress_dialog.transient(self.root)
            progress_dialog.grab_set()
            progress_dialog.resizable(False, False)

            # Center the dialog
            progress_dialog.update_idletasks()
            x = (progress_dialog.winfo_screenwidth() // 2) - (300 // 2)
            y = (progress_dialog.winfo_screenheight() // 2) - (100 // 2)
            progress_dialog.geometry(f"300x100+{x}+{y}")

            # Content
            tk.Label(progress_dialog, text=message, font=('Arial', 12),
                    bg='white', fg='#2C3E50').pack(pady=20)

            # Progress bar
            progress_bar = ttk.Progressbar(progress_dialog, mode='indeterminate')
            progress_bar.pack(pady=10, padx=20, fill='x')
            progress_bar.start()

            progress_dialog.update()
            self.progress_dialog = progress_dialog
            return progress_dialog

        except Exception as e:
            print(f"❌ Error showing progress dialog: {e}")
            return None

    def generate_pdf_report(self):
        """Generate comprehensive PDF report with charts and analytics"""
        try:
            if not PDF_AVAILABLE:
                messagebox.showwarning("PDF Not Available",
                                     "PDF generation requires ReportLab.\n\n"
                                     "Install with: pip install reportlab")
                return

            # Show progress dialog
            progress_dialog = self.show_progress_dialog("Generating PDF Report...")

            # Get filename
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            reports_dir = "reports"
            os.makedirs(reports_dir, exist_ok=True)
            filename = os.path.join(reports_dir, f"AI_Detection_Report_{timestamp}.pdf")

            # Create PDF document
            doc = SimpleDocTemplate(filename, pagesize=letter, topMargin=1*inch)
            styles = getSampleStyleSheet()
            story = []

            # Custom styles
            title_style = ParagraphStyle(
                'CustomTitle',
                parent=styles['Heading1'],
                fontSize=24,
                spaceAfter=30,
                textColor=colors.HexColor('#2C3E50'),
                alignment=1  # Center
            )

            heading_style = ParagraphStyle(
                'CustomHeading',
                parent=styles['Heading2'],
                fontSize=16,
                spaceAfter=12,
                textColor=colors.HexColor('#3498DB')
            )

            # Title page
            story.append(Paragraph("🎯 AI Detection System", title_style))
            story.append(Paragraph("Comprehensive Analytics Report", title_style))
            story.append(Spacer(1, 0.5*inch))

            # Report metadata
            start_time, end_time = self.get_time_range()
            metadata_data = [
                ['Report Generated:', datetime.now().strftime('%Y-%m-%d %H:%M:%S')],
                ['Time Period:', f"{start_time.strftime('%Y-%m-%d %H:%M')} to {end_time.strftime('%Y-%m-%d %H:%M')}"],
                ['Total Records:', str(sum(len(data) for data in self.current_data.values()))],
                ['System Status:', 'Operational']
            ]

            metadata_table = Table(metadata_data, colWidths=[2*inch, 3*inch])
            metadata_table.setStyle(TableStyle([
                ('BACKGROUND', (0, 0), (-1, -1), colors.HexColor('#F8F9FA')),
                ('TEXTCOLOR', (0, 0), (-1, -1), colors.HexColor('#2C3E50')),
                ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
                ('FONTNAME', (0, 0), (-1, -1), 'Helvetica'),
                ('FONTSIZE', (0, 0), (-1, -1), 12),
                ('BOTTOMPADDING', (0, 0), (-1, -1), 12),
                ('GRID', (0, 0), (-1, -1), 1, colors.HexColor('#BDC3C7'))
            ]))

            story.append(metadata_table)
            story.append(PageBreak())

            # Enhanced Executive Summary with Analytics
            story.append(Paragraph("📊 Executive Summary & Key Insights", heading_style))

            total_detections = sum(len(data) for data in self.current_data.values())
            confidence_analysis = self.analyze_confidence_distribution()
            detection_rates = self.calculate_detection_rates()
            quality_metrics = self.analyze_data_quality()

            # Calculate time span
            start_time, end_time = self.get_time_range()
            time_span = end_time - start_time
            time_span_hours = time_span.total_seconds() / 3600

            summary_text = f"""
            <b>System Performance Overview:</b><br/>
            This comprehensive analysis covers {total_detections:,} detection events over {time_span_hours:.1f} hours
            ({time_span.days} days, {time_span.seconds//3600} hours). The AI Detection System demonstrates
            {quality_metrics['data_integrity'].lower()} performance with an overall confidence of {confidence_analysis['overall_average']:.1%}.

            <b>Key Performance Indicators:</b><br/>
            • Detection Rate: {detection_rates['detection_frequency']:.1f} detections/hour<br/>
            • Data Quality Score: {quality_metrics['completeness_score']:.1f}% completeness, {quality_metrics['consistency_score']:.1f}% consistency<br/>
            • High Confidence Detections: {confidence_analysis['quality_score']:.1f}% (≥80% confidence)<br/>
            • System Integrity: {quality_metrics['data_integrity']}<br/>

            <b>Detection Distribution:</b><br/>
            The system shows balanced performance across all detection categories with specialized
            insights for age estimation, object recognition, facial expression analysis, and security anomaly detection.
            """

            story.append(Paragraph(summary_text, styles['Normal']))
            story.append(Spacer(1, 0.3*inch))

            # Enhanced Detection Statistics Table
            story.append(Paragraph("📈 Comprehensive Detection Analytics", heading_style))

            stats_data = [['Detection Type', 'Count', 'Percentage', 'Avg Confidence', 'Quality Score', 'Rate/Hour']]

            for det_type in self.detection_types:
                count = len(self.current_data.get(det_type, []))
                percentage = (count / total_detections * 100) if total_detections > 0 else 0

                # Enhanced confidence analysis
                type_analysis = confidence_analysis['by_type'].get(det_type, {})
                avg_conf = type_analysis.get('average', 0)

                # Quality score (percentage of high confidence detections for this type)
                type_records = self.current_data.get(det_type, [])
                high_conf_count = sum(1 for record in type_records
                                    if record.get('confidence', 0) >= 0.8)
                quality_score = (high_conf_count / count * 100) if count > 0 else 0

                # Detection rate
                rate_per_hour = detection_rates['detections_per_hour'].get(det_type, 0)

                stats_data.append([
                    det_type.title(),
                    f"{count:,}",
                    f"{percentage:.1f}%",
                    f"{avg_conf:.1%}",
                    f"{quality_score:.1f}%",
                    f"{rate_per_hour:.1f}"
                ])

            stats_table = Table(stats_data, colWidths=[1.5*inch, 1*inch, 1*inch, 1.5*inch])
            stats_table.setStyle(TableStyle([
                ('BACKGROUND', (0, 0), (-1, 0), colors.HexColor('#3498DB')),
                ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
                ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
                ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
                ('FONTNAME', (0, 1), (-1, -1), 'Helvetica'),
                ('FONTSIZE', (0, 0), (-1, -1), 10),
                ('BOTTOMPADDING', (0, 0), (-1, -1), 12),
                ('BACKGROUND', (0, 1), (-1, -1), colors.HexColor('#ECF0F1')),
                ('GRID', (0, 0), (-1, -1), 1, colors.HexColor('#BDC3C7'))
            ]))

            story.append(stats_table)
            story.append(Spacer(1, 0.3*inch))

            # Advanced Analytics Section
            story.append(Paragraph("🔍 Advanced Analytics & Insights", heading_style))

            # Performance Metrics
            performance_data = [
                ['Metric', 'Value', 'Assessment'],
                ['Overall System Confidence', f"{confidence_analysis['overall_average']:.1%}",
                 'Excellent' if confidence_analysis['overall_average'] > 0.8 else 'Good' if confidence_analysis['overall_average'] > 0.6 else 'Needs Improvement'],
                ['Data Quality Score', f"{quality_metrics['completeness_score']:.1f}%", quality_metrics['data_integrity']],
                ['Detection Frequency', f"{detection_rates['detection_frequency']:.1f}/hour",
                 'High' if detection_rates['detection_frequency'] > 10 else 'Moderate' if detection_rates['detection_frequency'] > 5 else 'Low'],
                ['High Confidence Rate', f"{confidence_analysis['quality_score']:.1f}%",
                 'Excellent' if confidence_analysis['quality_score'] > 80 else 'Good' if confidence_analysis['quality_score'] > 60 else 'Fair']
            ]

            performance_table = Table(performance_data, colWidths=[2.5*inch, 1.5*inch, 1.5*inch])
            performance_table.setStyle(TableStyle([
                ('BACKGROUND', (0, 0), (-1, 0), colors.HexColor('#34495E')),
                ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
                ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
                ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
                ('FONTNAME', (0, 1), (-1, -1), 'Helvetica'),
                ('FONTSIZE', (0, 0), (-1, -1), 10),
                ('BOTTOMPADDING', (0, 0), (-1, -1), 8),
                ('BACKGROUND', (0, 1), (-1, -1), colors.HexColor('#ECF0F1')),
                ('GRID', (0, 0), (-1, -1), 1, colors.HexColor('#BDC3C7'))
            ]))

            story.append(performance_table)
            story.append(Spacer(1, 0.2*inch))

            # Confidence Distribution Analysis
            story.append(Paragraph("📊 Confidence Distribution Analysis", styles['Heading3']))

            conf_dist = confidence_analysis['distribution']
            total_conf_records = sum(conf_dist.values())

            if total_conf_records > 0:
                conf_analysis_text = f"""
                <b>Confidence Level Distribution:</b><br/>
                • High Confidence (≥80%): {conf_dist['high']:,} detections ({conf_dist['high']/total_conf_records*100:.1f}%)<br/>
                • Medium Confidence (60-79%): {conf_dist['medium']:,} detections ({conf_dist['medium']/total_conf_records*100:.1f}%)<br/>
                • Low Confidence (<60%): {conf_dist['low']:,} detections ({conf_dist['low']/total_conf_records*100:.1f}%)<br/>

                <b>Quality Assessment:</b><br/>
                The system demonstrates {'excellent' if conf_dist['high']/total_conf_records > 0.7 else 'good' if conf_dist['high']/total_conf_records > 0.5 else 'moderate'}
                confidence distribution with {conf_dist['high']/total_conf_records*100:.1f}% of detections achieving high confidence levels.
                """
            else:
                conf_analysis_text = "No confidence data available for analysis."

            story.append(Paragraph(conf_analysis_text, styles['Normal']))
            story.append(Spacer(1, 0.2*inch))

            # Temporal Analysis
            if detection_rates['peak_hours']:
                story.append(Paragraph("⏰ Temporal Analysis", styles['Heading3']))

                temporal_text = f"""
                <b>Peak Activity Hours:</b> {', '.join(detection_rates['peak_hours'])}<br/>
                <b>Detection Pattern:</b> The system shows {'consistent' if len(detection_rates['temporal_distribution']) > 12 else 'variable'}
                activity patterns throughout the monitoring period.
                """

                story.append(Paragraph(temporal_text, styles['Normal']))
                story.append(Spacer(1, 0.2*inch))

            # Data Quality Recommendations
            if quality_metrics['recommendations']:
                story.append(Paragraph("💡 Recommendations for Improvement", styles['Heading3']))

                recommendations_text = "<b>System Optimization Recommendations:</b><br/>"
                for i, rec in enumerate(quality_metrics['recommendations'], 1):
                    recommendations_text += f"{i}. {rec}<br/>"

                story.append(Paragraph(recommendations_text, styles['Normal']))
                story.append(Spacer(1, 0.3*inch))

            # Detailed Analysis for each detection type
            for det_type in self.detection_types:
                data = self.current_data.get(det_type, [])
                if data:
                    story.append(Paragraph(f"🔍 {det_type.title()} Detection Analysis", heading_style))

                    analysis_text = self.get_detailed_analysis(det_type, data)
                    story.append(Paragraph(analysis_text, styles['Normal']))

                    # Sample data table
                    if len(data) > 0:
                        story.append(Paragraph("Sample Records:", styles['Heading3']))

                        sample_data = [['Timestamp', 'Value', 'Confidence']]
                        for record in data[-10:]:  # Last 10 records
                            timestamp = record.get('timestamp', '')[:19]  # Remove microseconds

                            if det_type == 'age':
                                value = f"{record.get('age', 0)} years"
                            elif det_type == 'object':
                                value = record.get('object_name', 'unknown')
                            elif det_type == 'expression':
                                value = record.get('expression', 'unknown')
                            elif det_type == 'anomaly':
                                value = f"{record.get('anomaly_type', 'unknown')} ({record.get('threat_level', 'LOW')})"

                            confidence = f"{record.get('confidence', 0):.2f}"
                            sample_data.append([timestamp, value, confidence])

                        sample_table = Table(sample_data, colWidths=[2*inch, 2*inch, 1*inch])
                        sample_table.setStyle(TableStyle([
                            ('BACKGROUND', (0, 0), (-1, 0), colors.HexColor('#95A5A6')),
                            ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
                            ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
                            ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
                            ('FONTNAME', (0, 1), (-1, -1), 'Helvetica'),
                            ('FONTSIZE', (0, 0), (-1, -1), 9),
                            ('BOTTOMPADDING', (0, 0), (-1, -1), 8),
                            ('BACKGROUND', (0, 1), (-1, -1), colors.HexColor('#F8F9FA')),
                            ('GRID', (0, 0), (-1, -1), 1, colors.HexColor('#BDC3C7'))
                        ]))

                        story.append(sample_table)

                    story.append(Spacer(1, 0.2*inch))

            # Footer
            story.append(PageBreak())
            story.append(Paragraph("📋 Report Summary", heading_style))

            footer_text = f"""
            This comprehensive report was generated by the AI Detection Dashboard system.
            The analysis covers {total_detections} detection events across {len([k for k, v in self.current_data.items() if v])}
            active detection categories.

            System Status: {'OPERATIONAL' if total_detections > 0 else 'LIMITED DATA'}
            Report Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

            For technical support or questions about this report, please refer to the system documentation.
            """

            story.append(Paragraph(footer_text, styles['Normal']))

            # Build PDF
            progress_dialog.destroy()
            doc.build(story)

            # Show success message
            result = messagebox.askyesno(
                "PDF Report Generated",
                f"✅ PDF report generated successfully!\n\n"
                f"File: {os.path.basename(filename)}\n"
                f"Size: {os.path.getsize(filename):,} bytes\n\n"
                f"Would you like to open the reports folder?"
            )

            if result:
                self.open_reports_folder()

            print(f"✅ PDF report generated: {filename}")

        except Exception as e:
            if 'progress_dialog' in locals():
                progress_dialog.destroy()
            print(f"❌ Error generating PDF report: {e}")
            messagebox.showerror("PDF Generation Error", f"Failed to generate PDF report:\n{e}")

    def get_detailed_analysis(self, det_type, data):
        """Get enhanced detailed analysis text for a detection type"""
        try:
            if det_type == 'age':
                ages = [item.get('age', 0) for item in data if item.get('age')]
                if ages:
                    avg_age = sum(ages) / len(ages)
                    min_age, max_age = min(ages), max(ages)

                    # Age group analysis
                    age_groups = {
                        'Child (0-12)': len([a for a in ages if a <= 12]),
                        'Teen (13-19)': len([a for a in ages if 13 <= a <= 19]),
                        'Young Adult (20-34)': len([a for a in ages if 20 <= a <= 34]),
                        'Adult (35-54)': len([a for a in ages if 35 <= a <= 54]),
                        'Senior (55+)': len([a for a in ages if a >= 55])
                    }

                    most_common_group = max(age_groups, key=age_groups.get)

                    # Confidence analysis for age detection
                    confidences = [item.get('confidence', 0) for item in data if item.get('confidence')]
                    avg_confidence = sum(confidences) / len(confidences) if confidences else 0

                    return f"Age detection processed {len(ages)} estimations with average age {avg_age:.1f} years (range: {min_age}-{max_age}). Most common demographic: {most_common_group} ({age_groups[most_common_group]} detections). System confidence: {avg_confidence:.1%}. Age distribution shows {', '.join([f'{group}: {count}' for group, count in age_groups.items() if count > 0])}."
                else:
                    return "No valid age data available for analysis."

            elif det_type == 'object':
                objects = [item.get('object_name', '') for item in data]
                object_counts = Counter(objects)
                human_count = object_counts.get('human', 0)
                total_objects = len(objects)

                # Confidence analysis for objects
                confidences = [item.get('confidence', 0) for item in data if item.get('confidence')]
                avg_confidence = sum(confidences) / len(confidences) if confidences else 0

                # Detection method analysis
                methods = [item.get('detection_method', 'Unknown') for item in data]
                method_counts = Counter(methods)
                primary_method = method_counts.most_common(1)[0] if method_counts else ('Unknown', 0)

                # Anomaly analysis
                anomaly_count = sum(1 for item in data if item.get('is_anomaly', False))
                anomaly_percentage = (anomaly_count / total_objects * 100) if total_objects > 0 else 0

                # Spatial analysis (if bbox data available)
                bbox_data = [item for item in data if 'bbox_x' in item and 'bbox_y' in item]
                spatial_info = f" Spatial coverage: {len(bbox_data)} objects with location data." if bbox_data else ""

                return f"Object detection processed {total_objects:,} detections across {len(object_counts)} unique object types using primarily {primary_method[0]} ({primary_method[1]} detections). Human detection: {human_count} instances ({human_count/total_objects*100:.1f}%). System confidence: {avg_confidence:.1%}. Anomaly rate: {anomaly_percentage:.1f}% ({anomaly_count} anomalous objects).{spatial_info} Top objects: {', '.join([f'{obj} ({count})' for obj, count in object_counts.most_common(5)])}."

            elif det_type == 'expression':
                expressions = [item.get('expression', '') for item in data]
                expr_counts = Counter(expressions)
                total_expr = len(expressions)

                # Emotional category analysis
                positive_emotions = ['Happy', 'Surprise', 'Joy']
                negative_emotions = ['Anger', 'Disgust', 'Fear', 'Sad', 'Sadness']
                neutral_emotions = ['Neutral', 'Contempt', 'Calm']

                positive_count = sum(expr_counts.get(emotion, 0) for emotion in positive_emotions)
                negative_count = sum(expr_counts.get(emotion, 0) for emotion in negative_emotions)
                neutral_count = sum(expr_counts.get(emotion, 0) for emotion in neutral_emotions)

                # Confidence analysis
                confidences = [item.get('confidence', 0) for item in data if item.get('confidence')]
                avg_confidence = sum(confidences) / len(confidences) if confidences else 0

                # Model performance
                models = [item.get('model_used', 'Unknown') for item in data]
                model_counts = Counter(models)
                primary_model = model_counts.most_common(1)[0] if model_counts else ('Unknown', 0)

                # Processing time analysis
                processing_times = [item.get('processing_time', 0) for item in data if item.get('processing_time')]
                avg_processing_time = sum(processing_times) / len(processing_times) if processing_times else 0

                emotional_sentiment = "positive" if positive_count > negative_count else "negative" if negative_count > positive_count else "neutral"

                return f"Facial expression analysis processed {total_expr:,} expressions using {primary_model[0]} model with {avg_confidence:.1%} confidence. Emotional distribution: Positive {positive_count} ({positive_count/total_expr*100:.1f}%), Negative {negative_count} ({negative_count/total_expr*100:.1f}%), Neutral {neutral_count} ({neutral_count/total_expr*100:.1f}%). Overall sentiment: {emotional_sentiment}. Average processing: {avg_processing_time*1000:.1f}ms. Top expressions: {', '.join([f'{expr} ({count})' for expr, count in expr_counts.most_common(5)])}."

            elif det_type == 'anomaly':
                threat_levels = [item.get('threat_level', 'LOW') for item in data]
                threat_counts = Counter(threat_levels)
                high_threats = threat_counts.get('HIGH', 0)
                medium_threats = threat_counts.get('MEDIUM', 0)
                low_threats = threat_counts.get('LOW', 0)

                # Anomaly type analysis
                anomaly_types = [item.get('anomaly_type', 'Unknown') for item in data]
                type_counts = Counter(anomaly_types)

                # Confidence analysis
                confidences = [item.get('confidence', 0) for item in data if item.get('confidence')]
                avg_confidence = sum(confidences) / len(confidences) if confidences else 0

                # Temporal analysis
                recent_anomalies = 0
                current_time = datetime.now()
                for item in data:
                    try:
                        timestamp_str = str(item.get('timestamp', ''))
                        if timestamp_str:
                            if 'T' in timestamp_str:
                                record_time = datetime.fromisoformat(timestamp_str.replace('Z', '+00:00'))
                            else:
                                record_time = datetime.strptime(timestamp_str[:19], '%Y-%m-%d %H:%M:%S')

                            if (current_time - record_time).total_seconds() < 3600:  # Last hour
                                recent_anomalies += 1
                    except:
                        continue

                # Security assessment
                if high_threats > 0:
                    security_status = "CRITICAL - Immediate attention required"
                elif medium_threats > 5:
                    security_status = "ELEVATED - Enhanced monitoring recommended"
                elif len(data) > 10:
                    security_status = "MODERATE - Normal security monitoring"
                else:
                    security_status = "LOW - Minimal security concerns"

                # Recording analysis
                recordings = [item for item in data if item.get('recording_path')]
                recording_rate = (len(recordings) / len(data) * 100) if data else 0

                return f"Security anomaly detection identified {len(data):,} events with threat distribution: HIGH {high_threats}, MEDIUM {medium_threats}, LOW {low_threats}. Security status: {security_status}. Detection confidence: {avg_confidence:.1%}. Recent activity: {recent_anomalies} anomalies in last hour. Recording capture rate: {recording_rate:.1f}%. Primary anomaly types: {', '.join([f'{atype} ({count})' for atype, count in type_counts.most_common(3)])}. System maintains continuous security monitoring with automated threat assessment."

            return f"Analysis for {det_type} detection type with {len(data)} records."

        except Exception as e:
            return f"Error generating analysis for {det_type}: {e}"

    def calculate_average_confidence(self):
        """Calculate overall average confidence"""
        try:
            all_confidences = []
            for records in self.current_data.values():
                for record in records:
                    confidence = record.get('confidence')
                    if confidence is not None:
                        all_confidences.append(float(confidence))

            return sum(all_confidences) / len(all_confidences) if all_confidences else 0.0

        except Exception as e:
            print(f"❌ Error calculating average confidence: {e}")
            return 0.0



    def export_csv(self):
        """Export current data to CSV format"""
        try:
            # Get filename
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = filedialog.asksaveasfilename(
                title="Export CSV Data",
                defaultextension=".csv",
                filetypes=[("CSV files", "*.csv"), ("All files", "*.*")],
                initialname=f"detection_data_{timestamp}.csv"
            )

            if not filename:
                return

            progress_dialog = self.show_progress_dialog("Exporting CSV data...")

            with open(filename, 'w', newline='', encoding='utf-8') as csvfile:
                writer = csv.writer(csvfile)

                # Write header
                writer.writerow(['Detection_Type', 'Timestamp', 'Value', 'Confidence', 'Additional_Info'])

                # Write data
                for det_type, records in self.current_data.items():
                    for record in records:
                        timestamp_str = record.get('timestamp', '')
                        confidence = record.get('confidence', 0.0)

                        if det_type == 'age':
                            value = f"{record.get('age', 0)} years"
                            additional = f"Model: {record.get('model_used', 'Unknown')}"
                        elif det_type == 'object':
                            value = record.get('object_name', 'unknown')
                            additional = f"Anomaly: {record.get('is_anomaly', False)}"
                        elif det_type == 'expression':
                            value = record.get('expression', 'unknown')
                            additional = f"Model: {record.get('model_used', 'Unknown')}"
                        elif det_type == 'anomaly':
                            value = record.get('anomaly_type', 'unknown')
                            additional = f"Threat: {record.get('threat_level', 'LOW')}, Desc: {record.get('description', '')}"
                        else:
                            value = str(record)
                            additional = ''

                        writer.writerow([det_type.title(), timestamp_str, value, confidence, additional])

            progress_dialog.destroy()

            # Show success message
            result = messagebox.askyesno(
                "CSV Export Complete",
                f"✅ CSV data exported successfully!\n\n"
                f"File: {os.path.basename(filename)}\n"
                f"Records: {sum(len(data) for data in self.current_data.values())}\n\n"
                f"Would you like to open the file location?"
            )

            if result:
                self.open_file_location(filename)

            print(f"✅ CSV exported: {filename}")

        except Exception as e:
            if 'progress_dialog' in locals():
                progress_dialog.destroy()
            print(f"❌ Error exporting CSV: {e}")
            messagebox.showerror("CSV Export Error", f"Failed to export CSV: {e}")

    def clear_old_data(self):
        """Clear old data with enhanced options"""
        try:
            # Create clear data dialog
            clear_dialog = tk.Toplevel(self.root)
            clear_dialog.title("🧹 Clear Old Data")
            clear_dialog.geometry("400x350")
            clear_dialog.configure(bg='white')
            clear_dialog.transient(self.root)
            clear_dialog.grab_set()

            # Center dialog
            clear_dialog.update_idletasks()
            x = (clear_dialog.winfo_screenwidth() // 2) - (200)
            y = (clear_dialog.winfo_screenheight() // 2) - (175)
            clear_dialog.geometry(f"400x350+{x}+{y}")

            # Header
            header_frame = tk.Frame(clear_dialog, bg='#F39C12', height=60)
            header_frame.pack(fill='x')
            header_frame.pack_propagate(False)

            tk.Label(header_frame, text="🧹 Clear Old Data",
                    font=('Arial', 16, 'bold'), bg='#F39C12', fg='white').pack(pady=15)

            # Content
            content_frame = tk.Frame(clear_dialog, bg='white')
            content_frame.pack(fill='both', expand=True, padx=20, pady=20)

            tk.Label(content_frame, text="⚠️ WARNING: This action cannot be undone!",
                    font=('Arial', 12, 'bold'), bg='white', fg='#E74C3C').pack(pady=(0, 15))

            tk.Label(content_frame, text="Select data age to clear:",
                    font=('Arial', 11), bg='white', fg='#2C3E50').pack(anchor='w', pady=(0, 10))

            # Options
            clear_var = tk.StringVar(value="30")
            options = [
                ("7 days old", "7"),
                ("30 days old (recommended)", "30"),
                ("90 days old", "90"),
                ("All data (⚠️ DANGER)", "all")
            ]

            for text, value in options:
                rb = tk.Radiobutton(content_frame, text=text, variable=clear_var, value=value,
                                  bg='white', font=('Arial', 10))
                rb.pack(anchor='w', pady=3)

            # Current statistics
            stats_frame = tk.Frame(content_frame, bg='#F8F9FA', relief='solid', bd=1)
            stats_frame.pack(fill='x', pady=(15, 10))

            tk.Label(stats_frame, text="📊 Current Database Statistics:",
                    font=('Arial', 10, 'bold'), bg='#F8F9FA', fg='#2C3E50').pack(pady=(10, 5))

            total_records = sum(len(data) for data in self.current_data.values())
            for det_type in self.detection_types:
                count = len(self.current_data.get(det_type, []))
                tk.Label(stats_frame, text=f"{det_type.title()}: {count} records",
                        font=('Arial', 9), bg='#F8F9FA', fg='#7F8C8D').pack()

            tk.Label(stats_frame, text=f"Total: {total_records} records",
                    font=('Arial', 10, 'bold'), bg='#F8F9FA', fg='#2C3E50').pack(pady=(5, 10))

            # Buttons
            button_frame = tk.Frame(content_frame, bg='white')
            button_frame.pack(fill='x', pady=(15, 0))

            def confirm_clear():
                """Confirm and execute data clearing"""
                try:
                    days = clear_var.get()

                    if days == "all":
                        final_confirm = messagebox.askyesno(
                            "⚠️ FINAL WARNING",
                            "You are about to DELETE ALL detection data!\n\n"
                            "This will permanently remove ALL records.\n\n"
                            "Are you absolutely sure?"
                        )
                        if not final_confirm:
                            return

                    if self.enhanced_db:
                        progress_dialog = self.show_progress_dialog("Clearing old data...")

                        try:
                            if days == "all":
                                deleted_count = self.enhanced_db.clear_old_data(0)  # Clear all
                            else:
                                deleted_count = self.enhanced_db.clear_old_data(int(days))

                            progress_dialog.destroy()
                            clear_dialog.destroy()

                            messagebox.showinfo("Data Cleared",
                                              f"✅ Successfully cleared {deleted_count} old records!")

                            self.refresh_data()

                        except Exception as e:
                            progress_dialog.destroy()
                            messagebox.showerror("Clear Error", f"Failed to clear data: {e}")
                    else:
                        messagebox.showerror("Database Error", "Enhanced database not available.")

                except Exception as e:
                    print(f"❌ Error clearing data: {e}")
                    messagebox.showerror("Clear Error", f"Failed to clear data: {e}")

            ColoredButton(button_frame, text="🧹 Clear", command=confirm_clear,
                         bg_color='#F39C12', width=12).pack(side='left', padx=(0, 10))

            ColoredButton(button_frame, text="❌ Cancel", command=clear_dialog.destroy,
                         bg_color='#95A5A6', width=12).pack(side='left')

        except Exception as e:
            print(f"❌ Error opening clear data dialog: {e}")
            messagebox.showerror("Clear Data Error", f"Failed to open clear data dialog: {e}")

    def show_progress_dialog(self, message):
        """Show progress dialog for long operations"""
        try:
            progress_dialog = tk.Toplevel(self.root)
            progress_dialog.title("Processing...")
            progress_dialog.geometry("300x100")
            progress_dialog.configure(bg='white')
            progress_dialog.transient(self.root)
            progress_dialog.grab_set()

            # Center dialog
            progress_dialog.update_idletasks()
            x = (progress_dialog.winfo_screenwidth() // 2) - (150)
            y = (progress_dialog.winfo_screenheight() // 2) - (50)
            progress_dialog.geometry(f"300x100+{x}+{y}")

            tk.Label(progress_dialog, text=message, font=('Arial', 12),
                    bg='white', fg='#2C3E50').pack(pady=30)

            progress_dialog.update()
            return progress_dialog

        except Exception as e:
            print(f"❌ Error showing progress dialog: {e}")
            return None

    def open_reports_folder(self):
        """Open reports folder in file explorer"""
        try:
            reports_dir = os.path.abspath("reports")
            os.makedirs(reports_dir, exist_ok=True)

            import subprocess
            import platform

            system = platform.system()
            if system == "Windows":
                os.startfile(reports_dir)
            elif system == "Darwin":  # macOS
                subprocess.call(["open", reports_dir])
            else:  # Linux
                subprocess.call(["xdg-open", reports_dir])

            print(f"📂 Opened reports folder: {reports_dir}")

        except Exception as e:
            print(f"❌ Error opening reports folder: {e}")
            messagebox.showinfo("Reports Folder", f"Reports are saved in:\n{os.path.abspath('reports')}")

    def open_file_location(self, filename):
        """Open file location in explorer"""
        try:
            import subprocess
            import platform

            folder = os.path.dirname(filename)
            system = platform.system()

            if system == "Windows":
                subprocess.run(['explorer', '/select,', filename])
            elif system == "Darwin":  # macOS
                subprocess.run(['open', '-R', filename])
            else:  # Linux
                subprocess.run(['xdg-open', folder])

        except Exception as e:
            print(f"❌ Error opening file location: {e}")
            self.open_reports_folder()

    def close_dashboard(self):
        """Close dashboard safely"""
        try:
            # Stop auto-refresh
            self.stop_auto_refresh()

            # Close window
            if self.root:
                self.root.destroy()
                self.root = None

            print("✅ Dashboard closed successfully")

        except Exception as e:
            print(f"❌ Error closing dashboard: {e}")

# Factory function to create and show dashboard
def create_dashboard():
    """Create and show the enhanced dashboard"""
    try:
        dashboard = StreamlinedDashboard()
        dashboard.show_dashboard()
        return dashboard
    except Exception as e:
        print(f"❌ Error creating dashboard: {e}")
        messagebox.showerror("Dashboard Error", f"Failed to create dashboard: {e}")
        return None

# Backward compatibility
class DashboardWindow:
    """Backward compatibility wrapper"""
    def __init__(self):
        self.dashboard = None

    def show_dashboard(self):
        """Show dashboard using new implementation"""
        self.dashboard = create_dashboard()
        return self.dashboard

# Main execution for testing
if __name__ == "__main__":
    print("🎯 Testing Enhanced AI Detection Dashboard...")
    print("=" * 50)

    try:
        # Create test window
        root = tk.Tk()
        root.withdraw()  # Hide main window

        # Create and show dashboard
        dashboard = create_dashboard()

        if dashboard:
            print("✅ Enhanced dashboard launched successfully!")
            print("🎯 Features:")
            print("   • Real-time data integration")
            print("   • Professional PDF reports")
            print("   • Comprehensive delete functionality")
            print("   • Streamlined interface")
            print("   • Enhanced error handling")

            # Keep window open
            root.mainloop()
        else:
            print("❌ Failed to launch dashboard")

    except Exception as e:
        print(f"❌ Error in main execution: {e}")

    print("🎯 Dashboard test completed.")
