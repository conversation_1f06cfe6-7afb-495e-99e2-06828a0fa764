# 🛡️ AI Video Detection Tool

A comprehensive real-time AI-powered video detection system with facial expression recognition, age estimation, object detection, and anomaly monitoring capabilities.

## 🎉 **NEW: PyQt5 Enhanced Interface Available!**

The application now features a **modern PyQt5 interface** alongside the original Tkinter version:
- ✅ **Professional desktop appearance** with native OS integration
- ✅ **Enhanced performance** and stability
- ✅ **All original functionality preserved**
- ✅ **Automatic fallback** to Tkinter if PyQt5 unavailable

**Quick Start PyQt5**: `scripts\START_AI_DETECTION_PYQT5.bat`

## 🚀 Quick Start

### **Option 1: PyQt5 Enhanced Version (Recommended)**
```batch
# Windows
scripts\START_AI_DETECTION_PYQT5.bat

# Or manually
python main_pyqt5.py
python app/launcher_pyqt5.py
```

### **Option 2: Original Tkinter Version**
```batch
# Windows
scripts\START_AI_DETECTION.bat

# Or manually
python main.py
python app/launcher.py
```

### **Option 3: Auto-Detection (<PERSON><PERSON>yQt5 First)**
```bash
python main.py              # Now tries PyQt5 first, falls back to Tkinter
```

## 🎯 Features

### **AI Detection Capabilities**
- 😊 **Facial Expression Detection**: Real-time emotion recognition with 83.6% accuracy
- 👶 **Age Estimation**: Advanced age detection with 8 age categories
- 🔍 **Object Detection**: 80+ object classes with human detection
- 🚨 **Anomaly Detection**: Security monitoring with 21 anomaly types
- 📊 **Real-time Analytics**: Live statistics and performance monitoring

### **Interface Options**
- 🎨 **PyQt5 Interface**: Modern, professional desktop application
- 🖥️ **Tkinter Interface**: Original lightweight interface
- 📱 **Responsive Design**: Adapts to different screen sizes
- 🌙 **Professional Styling**: Enhanced visual appearance

### **Video Processing**
- 📹 **Real-time Video**: Live camera feed processing
- 🎥 **Recording**: Video recording with AI detection overlay
- 📸 **Snapshots**: Instant photo capture
- ⚡ **Performance**: Optimized for real-time processing

### **Data Management**
- 📊 **Dashboard**: Comprehensive analytics and reporting
- 📄 **Export**: PDF reports and CSV data export
- 💾 **Database**: SQLite integration for data storage
- 📈 **Statistics**: Session tracking and performance metrics

## 📦 Installation

### **1. Install Python Dependencies**
```bash
# Core dependencies
pip install PyQt5>=5.15.0
pip install opencv-python
pip install numpy
pip install pillow

# Or install from requirements
pip install -r requirement.txt
```

### **2. Download AI Models**
```bash
python download_required_models.py
```

### **3. Run the Application**
```bash
# PyQt5 version (recommended)
python main_pyqt5.py

# Or use batch file
scripts\START_AI_DETECTION_PYQT5.bat
```

## 🏗️ Project Structure

```
📁 AI Video Detection/
├── 🎨 gui_pyqt5/              # PyQt5 interface (NEW)
│   ├── login_window.py        # PyQt5 login interface
│   ├── main_window.py         # PyQt5 main application
│   ├── dashboard_window.py    # PyQt5 analytics dashboard
│   ├── base_components.py     # PyQt5 widgets and styling
│   └── utils.py               # PyQt5 utilities
├── 🖥️ gui/                    # Original Tkinter interface
├── 🤖 detection/              # AI detection modules
├── 📹 recording/              # Video recording system
├── 📊 utils/                  # Utilities and configuration
├── 🎯 models/                 # AI models and weights
├── 📱 app/                    # Application launchers
├── 📜 scripts/                # Batch files and utilities
└── 📚 docs/                   # Documentation
```

## 🎮 Usage

### **Login**
1. Start the application using any method above
2. Use default credentials: `admin` / `password123`
3. Or configure custom authentication in `utils/config.py`

### **Main Interface**
1. **Start Camera**: Begin video capture
2. **AI Detection**: Enable real-time detection modes
3. **Recording**: Record video with AI overlay
4. **Dashboard**: View analytics and reports

### **AI Detection Modes**
- **Manual Expression**: Press SPACE or click button
- **Real-time Age**: Toggle for continuous age detection
- **Real-time Objects**: Toggle for object detection
- **Real-time Anomaly**: Toggle for security monitoring

## 🔧 Configuration

### **Camera Settings**
- Default camera index: 0
- Resolution: 640x480 (configurable)
- FPS: 30 (configurable)

### **AI Detection Settings**
- Expression confidence: 50%
- Age detection interval: 15 frames
- Object detection threshold: 30%
- Anomaly sensitivity: Configurable

### **Interface Settings**
- Theme: Professional blue (PyQt5) / Light blue (Tkinter)
- Auto-refresh: 5 seconds
- Statistics update: Real-time

## 📊 Performance

### **System Requirements**
- **OS**: Windows 10/11, macOS, Linux
- **Python**: 3.7+
- **RAM**: 4GB minimum, 8GB recommended
- **Camera**: USB webcam or built-in camera

### **Performance Metrics**
- **Video FPS**: 30 FPS (typical)
- **AI Detection**: 2-5 FPS (depending on enabled features)
- **Memory Usage**: ~200-500MB
- **Startup Time**: 3-5 seconds

## 🛠️ Troubleshooting

### **Common Issues**
1. **PyQt5 not found**: Install with `pip install PyQt5`
2. **Camera not detected**: Check permissions and connections
3. **AI models missing**: Run `python download_required_models.py`
4. **Performance issues**: Disable some real-time detection modes

### **Fallback Options**
- If PyQt5 fails, application automatically falls back to Tkinter
- If camera fails, test mode with static images available
- If AI models missing, basic interface still functional

## 📚 Documentation

- 📖 **[PyQt5 Migration Guide](docs/PYQT5_MIGRATION_GUIDE.md)**: Complete migration details
- 📋 **[Restructured Guide](docs/RESTRUCTURED_GUIDE.md)**: Project organization
- 🔍 **[Anomaly Detection](docs/ANOMALY_DETECTION_README.md)**: Security features
- 📥 **[Download Guide](docs/DOWNLOAD_GUIDE.md)**: Model installation

## 🎯 Key Improvements in PyQt5 Version

### **Visual Enhancements**
- ✅ Professional desktop appearance
- ✅ Native OS integration and styling
- ✅ Enhanced button and widget styling
- ✅ Improved layout management
- ✅ Better color scheme and typography

### **Performance Improvements**
- ✅ Faster video rendering
- ✅ Optimized memory usage
- ✅ Better real-time responsiveness
- ✅ Enhanced error handling

### **User Experience**
- ✅ Professional message dialogs
- ✅ Better keyboard shortcuts
- ✅ Enhanced window management
- ✅ Improved accessibility

## 🔮 Future Enhancements

- 🌙 **Dark Theme**: Professional dark mode
- 📱 **Mobile Support**: Responsive design improvements
- 🔧 **Advanced Settings**: Comprehensive configuration dialog
- 🌐 **Plugin System**: Extensible architecture
- 📊 **Enhanced Analytics**: Advanced reporting features

## 📞 Support

For issues or questions:
1. Check the troubleshooting section above
2. Review documentation in `docs/` folder
3. Test with fallback Tkinter version
4. Verify dependencies and AI models

## 📄 License

This project is for educational and research purposes. Please ensure compliance with local regulations when using AI detection technologies.

---

**Status**: ✅ **Production Ready** - Both PyQt5 and Tkinter versions fully functional!
