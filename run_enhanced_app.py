#!/usr/bin/env python3
"""
Enhanced AI Video Detection with iOS-Inspired Interface
Run this script to start the application with all enhancements
"""

import sys
import os

# Add the project root to the path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def main():
    """Main entry point for the enhanced application"""
    try:
        print("🛡️ AI Video Detection - iOS Enhanced Interface")
    except UnicodeEncodeError:
        print("AI Video Detection - iOS Enhanced Interface")
    print("=" * 55)
    print()
    try:
        print("🎨 Features:")
        print("   ✅ iOS-inspired blue theme (#007AFF)")
        print("   ✅ Rounded corners and modern typography")
        print("   ✅ Card-based layout with shadows")
        print("   ✅ Animated buttons and status indicators")
        print("   ✅ Enhanced AI detection accuracy")
        print("   ✅ All original functionality preserved")
    except UnicodeEncodeError:
        print("Features:")
        print("   - iOS-inspired blue theme (#007AFF)")
        print("   - Rounded corners and modern typography")
        print("   - Card-based layout with shadows")
        print("   - Animated buttons and status indicators")
        print("   - Enhanced AI detection accuracy")
        print("   - All original functionality preserved")
    print()
    try:
        print("🚀 Starting application...")
    except UnicodeEncodeError:
        print("Starting application...")
    print()
    
    try:
        from gui.main_window import MainWindow
        app = MainWindow()
        app.run()
    except Exception as e:
        print(f"❌ Error: {e}")
        print("\nTroubleshooting:")
        print("1. Ensure all dependencies are installed:")
        print("   pip install opencv-python pillow numpy")
        print("2. Check camera permissions")
        print("3. Close other camera applications")

if __name__ == "__main__":
    main()
