import tkinter as tk
from tkinter import messagebox
import os
import sys

# Ensure we can import from parent directories
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
sys.path.insert(0, parent_dir)

try:
    from system.config.app_config import Config
    from system.utils.logger import setup_logger
except ImportError:
    try:
        # Fallback to old structure
        from utils.config import Config
        from utils.logger import setup_logger
    except ImportError:
        # Final fallback configuration
        class Config:
            DEFAULT_USERNAME = "admin"
            DEFAULT_PASSWORD = "password123"
            WINDOW_SIZE = (1200, 800)

        def setup_logger():
            import logging
            return logging.getLogger(__name__)

class LoginWindow:
    """Enhanced login window with secure authentication"""
    
    def __init__(self):
        self.config = Config()
        
        try:
            self.logger = setup_logger()
            self.logger.info("Starting beautiful secure login interface...")
        except:
            print("Starting login interface...")
        
        self.root = tk.Tk()
        self.authenticated = False
        self.setup_window()
        self.create_interface()
    
    def setup_window(self):
        """Setup main window properties"""
        self.root.title("🛡️ AI Video Detection - Login")
        self.root.geometry("800x600")
        self.root.configure(bg='#E8F4FD')
        self.root.resizable(False, False)

        # Center window
        self.center_window()

        # Handle window close
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
    
    def center_window(self):
        """Center window on screen"""
        self.root.update_idletasks()
        x = (self.root.winfo_screenwidth() // 2) - (800 // 2)
        y = (self.root.winfo_screenheight() // 2) - (600 // 2)
        self.root.geometry(f"800x600+{x}+{y}")
    
    def create_interface(self):
        """Create the login interface"""
        # Main container
        main_frame = tk.Frame(self.root, bg='#E8F4FD')
        main_frame.pack(fill='both', expand=True)

        # Header section
        self.create_header(main_frame)

        # Login section
        self.create_login_section(main_frame)

        # Footer section
        self.create_footer(main_frame)
    
    def create_header(self, parent):
        """Create header section"""
        header_frame = tk.Frame(parent, bg='#2C3E50', height=120)
        header_frame.pack(fill='x')
        header_frame.pack_propagate(False)

        # Header content
        header_content = tk.Frame(header_frame, bg='#2C3E50')
        header_content.pack(expand=True)

        # Logo and title - properly centered
        logo_label = tk.Label(header_content,
                             text="🛡️",
                             font=('Arial', 48),
                             bg='#2C3E50',
                             fg='#5DADE2')
        logo_label.pack(pady=(20, 5))

        title_label = tk.Label(header_content,
                              text="AI Video Detection",
                              font=('Arial', 20, 'bold'),
                              bg='#2C3E50',
                              fg='white')
        title_label.pack()

        subtitle_label = tk.Label(header_content,
                                 text="Security Monitoring & Analytics",
                                 font=('Arial', 12),
                                 bg='#2C3E50',
                                 fg='#BDC3C7')
        subtitle_label.pack(pady=(0, 20))
    
    def create_login_section(self, parent):
        """Create login form section"""
        # Login container
        login_container = tk.Frame(parent, bg='#E8F4FD')
        login_container.pack(expand=True, fill='both', padx=50, pady=50)

        # Login card
        login_card = tk.Frame(login_container, bg='white', relief='solid', bd=2)
        login_card.pack(expand=True, fill='none', ipadx=50, ipady=40)
        
        # Login card title
        card_title = tk.Label(login_card,
                             text="🔐",
                             font=('Arial', 18, 'bold'),
                             bg='white',
                             fg='#2C3E50')
        card_title.pack(pady=(0, 30))
        
        # Username field
        username_frame = tk.Frame(login_card, bg='white')
        username_frame.pack(fill='x', pady=10)
        
        username_label = tk.Label(username_frame,
                                 text="👤 Username:",
                                 font=('Arial', 12, 'bold'),
                                 bg='white',
                                 fg='#2C3E50')
        username_label.pack(anchor='w')
        
        self.username_entry = tk.Entry(username_frame,
                                      font=('Arial', 14),
                                      width=25,
                                      relief='solid',
                                      bd=1,
                                      bg='#F8F9FA')
        self.username_entry.pack(fill='x', pady=(5, 0))
        self.username_entry.insert(0, "admin")  # Default username
        
        # Password field
        password_frame = tk.Frame(login_card, bg='white')
        password_frame.pack(fill='x', pady=10)
        
        password_label = tk.Label(password_frame,
                                 text="🔒 Password:",
                                 font=('Arial', 12, 'bold'),
                                 bg='white',
                                 fg='#2C3E50')
        password_label.pack(anchor='w')
        
        self.password_entry = tk.Entry(password_frame,
                                      font=('Arial', 14),
                                      width=25,
                                      show='*',
                                      relief='solid',
                                      bd=1,
                                      bg='#F8F9FA')
        self.password_entry.pack(fill='x', pady=(5, 0))
        self.password_entry.insert(0, "password123")  # Default password for demo
        
        # Show/Hide password
        show_password_var = tk.BooleanVar()
        show_password_check = tk.Checkbutton(password_frame,
                                           text="Show password",
                                           variable=show_password_var,
                                           command=lambda: self.toggle_password_visibility(show_password_var.get()),
                                           bg='white',
                                           fg='#7F8C8D',
                                           font=('Arial', 10))
        show_password_check.pack(anchor='w', pady=(5, 0))
        
        # Login button
        login_button = tk.Button(login_card,
                               text="Login",
                               font=('Arial', 14, 'bold'),
                               bg='#3498DB',
                               fg='white',
                               relief='flat',
                               bd=0,
                               pady=12,
                               cursor='hand2',
                               command=self.authenticate)
        login_button.pack(fill='x', pady=(30, 10))
        
        # Features preview
        features_frame = tk.Frame(login_card, bg='white')
        features_frame.pack(fill='x', pady=(20, 0))
        
        features_title = tk.Label(features_frame,
                                 text="",
                                 font=('Arial', 12, 'bold'),
                                 bg='white',
                                 fg='#2C3E50')
        features_title.pack(anchor='w')
        
        features_text = """• Real-time facial expression detection
• Advanced age estimation and analysis
• Intelligent object detection and tracking  
• Security anomaly monitoring and alerts
• Comprehensive analytics and reporting"""
        
        features_label = tk.Label(features_frame,
                                 text=features_text,
                                 font=('Arial', 10),
                                 bg='white',
                                 fg='#34495E',
                                 justify='left')
        features_label.pack(anchor='w', pady=(5, 0))
        
        # Bind Enter key to login
        self.root.bind('<Return>', lambda event: self.authenticate())
        
        # Focus on username field
        self.username_entry.focus()
    
    def create_footer(self, parent):
        """Create footer section"""
        footer_frame = tk.Frame(parent, bg='#34495E', height=60)
        footer_frame.pack(fill='x', side='bottom')
        footer_frame.pack_propagate(False)
        
        footer_content = tk.Frame(footer_frame, bg='#34495E')
        footer_content.pack(expand=True)
        
        footer_text = tk.Label(footer_content,
                              text="AI Video Detection",
                              font=('Arial', 10),
                              bg='#34495E',
                              fg='#BDC3C7')
        footer_text.pack(expand=True)
        
        status_text = tk.Label(footer_content,
                              text="🟢 Online | Ready for Authentication",
                              font=('Arial', 9),
                              bg='#34495E',
                              fg='#27AE60')
        status_text.pack()
    
    def toggle_password_visibility(self, show):
        """Toggle password field visibility"""
        if show:
            self.password_entry.config(show='')
        else:
            self.password_entry.config(show='*')
    
    def authenticate(self):
        """Handle authentication"""
        try:
            username = self.username_entry.get().strip()
            password = self.password_entry.get().strip()
            
            print(f"🔐 Authentication attempt - Username: {username}")
            
            # Check credentials (case-insensitive username)
            if (username.lower() == self.config.DEFAULT_USERNAME.lower() and 
                password == self.config.DEFAULT_PASSWORD):
                
                print("✅ Authentication successful!")
                self.authenticated = True
                
                # Show success message
                messagebox.showinfo("Login Successful", 
                                   f"🎉 Welcome to AI Video Detection Tool!\n\n"
                                   f"User: {username}\n"
                                   f"Access Level: Administrator\n"
                                   f"Tool Status: All modules online")
                
                # Close login window and start main application
                self.root.destroy()
                self.start_main_application()
                
            else:
                print("❌ Authentication failed!")
                messagebox.showerror("Login Failed", 
                                    "❌ Invalid credentials!\n\n"
                                    "Please check your username and password.\n"
                                    "Default: admin / password123")
                
                # Clear password field
                self.password_entry.delete(0, tk.END)
                self.password_entry.focus()
                
        except Exception as e:
            print(f"❌ Authentication error: {e}")
            messagebox.showerror("Authentication Error", 
                                f"An error occurred during authentication:\n{str(e)}")
    
    def start_main_application(self):
        """Start the main application after successful login"""
        try:
            print("🚀 Starting main application...")
            
            # Try to import and start the main window
            try:
                from interface.main_window.main_window import MainWindow
                print("✅ Main window module imported successfully (new structure)")
            except ImportError:
                try:
                    from gui.main_window import MainWindow
                    print("✅ Main window module imported successfully (legacy structure)")
                except ImportError:
                    raise
                
                # Create and run main window
                main_app = MainWindow()
                main_app.run()
                
            except ImportError as e:
                print(f"⚠️ Main window import error: {e}")
                print("🔄 Starting fallback interface...")
                self.start_fallback_interface()
                
        except Exception as e:
            print(f"❌ Error starting main application: {e}")
            messagebox.showerror("Application Error", 
                                f"Error starting main application:\n{str(e)}")
    
    def start_fallback_interface(self):
        """Start fallback interface when main window fails"""
        try:
            # Create simple main window
            main_root = tk.Tk()
            main_root.title("🛡️ AI Video Detection - Main")
            main_root.geometry("1000x700")
            main_root.configure(bg='#E8F4FD')
            
            # Main frame
            main_frame = tk.Frame(main_root, bg='#E8F4FD')
            main_frame.pack(fill='both', expand=True, padx=20, pady=20)
            
            # Header
            header_frame = tk.Frame(main_frame, bg='#2C3E50', height=80)
            header_frame.pack(fill='x', pady=(0, 20))
            header_frame.pack_propagate(False)
            
            header_content = tk.Frame(header_frame, bg='#2C3E50')
            header_content.pack(expand=True)
            
            title_label = tk.Label(header_content,
                                  text="🛡️ AI Video Detection - Main",
                                  font=('Arial', 18, 'bold'),
                                  bg='#2C3E50',
                                  fg='white')
            title_label.pack(expand=True)
            
            # Content area
            content_frame = tk.Frame(main_frame, bg='white', relief='solid', bd=2)
            content_frame.pack(fill='both', expand=True)
            
            # Content
            content_title = tk.Label(content_frame,
                                    text="🎯 Tool Dashboard",
                                    font=('Arial', 16, 'bold'),
                                    bg='white',
                                    fg='#2C3E50')
            content_title.pack(pady=20)
            
            # Status
            status_text = """
🟢 System Status: Online and Ready
🟢 Authentication: Successful  
🟢 Core Modules: Loaded
⚠️ Advanced Features: Limited Mode

📋 Available Functions:
• Camera access and testing
• Basic video processing
• System configuration
• Help and documentation
            """
            
            status_label = tk.Label(content_frame,
                                   text=status_text,
                                   font=('Arial', 12),
                                   bg='white',
                                   fg='#2C3E50',
                                   justify='left')
            status_label.pack(pady=20)
            
            # Buttons
            button_frame = tk.Frame(content_frame, bg='white')
            button_frame.pack(pady=20)
            
            # Camera test button
            def test_camera():
                try:
                    import cv2
                    cap = cv2.VideoCapture(0)
                    if cap.isOpened():
                        ret, frame = cap.read()
                        if ret:
                            messagebox.showinfo("Camera Test", "✅ Camera is working perfectly!\n\nResolution: {}x{}\nStatus: Ready for detection".format(frame.shape[1], frame.shape[0]))
                        else:
                            messagebox.showwarning("Camera Test", "⚠️ Camera detected but cannot read frames")
                        cap.release()
                    else:
                        messagebox.showerror("Camera Test", "❌ No camera detected\n\nPlease check camera connection")
                except ImportError:
                    messagebox.showerror("Camera Test", "❌ OpenCV not available\n\nInstall with: pip install opencv-python")
                except Exception as e:
                    messagebox.showerror("Camera Test", f"❌ Camera error:\n{str(e)}")
            
            camera_btn = tk.Button(button_frame,
                                  text="📷 Test Camera",
                                  font=('Arial', 12, 'bold'),
                                  bg='#3498DB',
                                  fg='white',
                                  relief='flat',
                                  bd=0,
                                  pady=10,
                                  padx=20,
                                  cursor='hand2',
                                  command=test_camera)
            camera_btn.pack(side='left', padx=10)
            
            # Dashboard button
            def open_dashboard():
                try:
                    # Try to import dashboard
                    from interface.main_window.dashboard import DashboardWindow
                    dashboard = DashboardWindow()
                    dashboard.run()
                    messagebox.showinfo("Dashboard", "✅ Dashboard opened successfully!")
                except ImportError:
                    try:
                        from gui.dashboard_window import DashboardWindow
                        dashboard = DashboardWindow()
                        dashboard.run()
                        messagebox.showinfo("Dashboard", "✅ Dashboard opened successfully!")
                    except ImportError:
                        messagebox.showinfo("Dashboard", "📊 Dashboard feature!\n\nThis will include:\n• Real-time analytics\n• Detection reports\n• Performance metrics\n• Export options")
                except Exception as e:
                    messagebox.showerror("Dashboard Error", f"Error opening dashboard:\n{str(e)}")
            
            dashboard_btn = tk.Button(button_frame,
                                     text="📊 Analytics Dashboard",
                                     font=('Arial', 12, 'bold'),
                                     bg='#27AE60',
                                     fg='white',
                                     relief='flat',
                                     bd=0,
                                     pady=10,
                                     padx=20,
                                     cursor='hand2',
                                     command=open_dashboard)
            dashboard_btn.pack(side='left', padx=10)
            
            # Settings button
            def open_settings():
                messagebox.showinfo("Settings", "⚙️ Tool Settings\n\nThis will include:\n• Detection parameters\n• Camera configuration\n• Alert thresholds\n• Export preferences")
            
            settings_btn = tk.Button(button_frame,
                                    text="⚙️ Settings",
                                    font=('Arial', 12, 'bold'),
                                    bg='#F39C12',
                                    fg='white',
                                    relief='flat',
                                    bd=0,
                                    pady=10,
                                    padx=20,
                                    cursor='hand2',
                                    command=open_settings)
            settings_btn.pack(side='left', padx=10)
            
            # Exit button
            def exit_app():
                if messagebox.askyesno("Exit", "Are you sure you want to exit?"):
                    main_root.destroy()
            
            exit_btn = tk.Button(button_frame,
                                text="🚪 Exit",
                                font=('Arial', 12, 'bold'),
                                bg='#E74C3C',
                                fg='white',
                                relief='flat',
                                bd=0,
                                pady=10,
                                padx=20,
                                cursor='hand2',
                                command=exit_app)
            exit_btn.pack(side='left', padx=10)
            
            # Instructions
            instructions_text = """
🔧 Setup Instructions:
1. Install dependencies: pip install opencv-python numpy pillow
2. Download AI models: python download_models.py  
3. Restart for full functionality

📞 Support: Check documentation for advanced features
            """
            
            instructions_label = tk.Label(content_frame,
                                         text=instructions_text,
                                         font=('Arial', 10),
                                         bg='white',
                                         fg='#7F8C8D',
                                         justify='left')
            instructions_label.pack(pady=20)
            
            # Run the fallback interface
            main_root.mainloop()
            
        except Exception as e:
            print(f"❌ Fallback interface error: {e}")
            messagebox.showerror("Critical Error", "Unable to start any interface. Please check your Python installation.")
    
    def on_closing(self):
        """Handle window closing"""
        if messagebox.askyesno("Exit", "Are you sure you want to exit the application?"):
            self.root.destroy()
    
    def run(self):
        """Run the login window"""
        print("🖥️ Launching secure login interface...")
        self.root.mainloop()
