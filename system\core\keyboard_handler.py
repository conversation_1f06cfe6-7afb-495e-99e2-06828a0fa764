"""
Keyboard Handler Module for AI Video Detection
Handles all keyboard shortcuts and input events
"""

import tkinter as tk
from typing import Callable, Dict, Any, Optional
import logging


class KeyboardHandler:
    """
    Centralized keyboard input handler for the AI Video Detection application.
    Manages all keyboard shortcuts and delegates actions to appropriate handlers.
    """
    
    def __init__(self, main_window):
        """
        Initialize keyboard handler with reference to main window.
        
        Args:
            main_window: Reference to the main window instance
        """
        self.main_window = main_window
        self.logger = logging.getLogger(__name__)
        
        # Keyboard shortcut mappings
        self.shortcuts = {
            # Expression detection shortcuts
            'space': self._handle_expression_detection,
            'c': self._handle_expression_detection,
            
            # Single detection shortcuts
            'a': self._handle_age_detection,
            'o': self._handle_object_detection,
            
            # Recording and snapshot shortcuts
            's': self._handle_snapshot,
            'r': self._handle_recording_toggle,
            
            # Real-time detection toggles
            '1': self._handle_age_toggle,
            '2': self._handle_object_toggle,
            '3': self._handle_anomaly_toggle,
            
            # System shortcuts
            'escape': self._handle_escape,
            'f11': self._handle_fullscreen_toggle,
            'f5': self._handle_refresh,
            
            # Help and dashboard
            'h': self._handle_help,
            'd': self._handle_dashboard,
            
            # Camera controls
            'ctrl+c': self._handle_camera_toggle,
            'ctrl+r': self._handle_recording_toggle,
            'ctrl+s': self._handle_snapshot,
            
            # Window controls
            'ctrl+m': self._handle_minimize,
            'ctrl+q': self._handle_quit,
        }
        
        # Status messages for user feedback
        self.status_messages = {
            'camera_required': "❌ Start camera first!",
            'expression_detected': "😊 Expression detection triggered",
            'age_detected': "👶 Age detection triggered", 
            'object_detected': "🔍 Object detection triggered",
            'snapshot_taken': "📸 Snapshot captured",
            'recording_toggled': "🔴 Recording toggled",
            'detection_toggled': "🔄 Real-time detection toggled",
            'help_shown': "❓ Help displayed",
            'dashboard_opened': "📊 Dashboard opened",
            'camera_toggled': "📷 Camera toggled",
            'fullscreen_toggled': "🖥️ Fullscreen toggled",
            'system_refreshed': "🔄 System refreshed"
        }
        
        # Key combination handlers
        self.ctrl_pressed = False
        self.alt_pressed = False
        self.shift_pressed = False
        
        self.logger.info("🎹 Keyboard handler initialized")
    
    def bind_to_window(self, window: tk.Tk) -> None:
        """
        Bind keyboard events to the specified window.
        
        Args:
            window: The tkinter window to bind events to
        """
        try:
            # Bind key press and release events
            window.bind('<KeyPress>', self._on_key_press)
            window.bind('<KeyRelease>', self._on_key_release)
            
            # Bind specific key combinations
            window.bind('<Control-c>', lambda e: self._handle_camera_toggle())
            window.bind('<Control-r>', lambda e: self._handle_recording_toggle())
            window.bind('<Control-s>', lambda e: self._handle_snapshot())
            window.bind('<Control-m>', lambda e: self._handle_minimize())
            window.bind('<Control-q>', lambda e: self._handle_quit())
            window.bind('<F11>', lambda e: self._handle_fullscreen_toggle())
            window.bind('<F5>', lambda e: self._handle_refresh())
            
            # Ensure window can receive focus
            window.focus_set()
            
            self.logger.info("✅ Keyboard events bound to window")
            
        except Exception as e:
            self.logger.error(f"❌ Error binding keyboard events: {e}")
    
    def _on_key_press(self, event: tk.Event) -> None:
        """
        Handle key press events.
        
        Args:
            event: The keyboard event
        """
        try:
            key = event.keysym.lower()
            
            # Track modifier keys
            if key in ['control_l', 'control_r']:
                self.ctrl_pressed = True
            elif key in ['alt_l', 'alt_r']:
                self.alt_pressed = True
            elif key in ['shift_l', 'shift_r']:
                self.shift_pressed = True
            
            # Handle key combinations
            if self.ctrl_pressed and key in ['c', 'r', 's', 'm', 'q']:
                combo_key = f'ctrl+{key}'
                if combo_key in self.shortcuts:
                    self.shortcuts[combo_key]()
                    return
            
            # Handle single key shortcuts
            if key in self.shortcuts:
                self.shortcuts[key]()
                
            # Log key press for debugging
            if hasattr(self.main_window, 'config') and getattr(self.main_window.config, 'DEBUG_MODE', False):
                self.logger.debug(f"🔵 Key pressed: {key}")
                
        except Exception as e:
            self.logger.error(f"❌ Error handling key press: {e}")
    
    def _on_key_release(self, event: tk.Event) -> None:
        """
        Handle key release events.
        
        Args:
            event: The keyboard event
        """
        try:
            key = event.keysym.lower()
            
            # Reset modifier key states
            if key in ['control_l', 'control_r']:
                self.ctrl_pressed = False
            elif key in ['alt_l', 'alt_r']:
                self.alt_pressed = False
            elif key in ['shift_l', 'shift_r']:
                self.shift_pressed = False
                
        except Exception as e:
            self.logger.error(f"❌ Error handling key release: {e}")
    
    # Expression Detection Handlers
    def _handle_expression_detection(self) -> None:
        """Handle expression detection shortcut (SPACE/C keys)."""
        try:
            if self._is_camera_available():
                self.logger.info("🔵 Expression detection triggered by keyboard")
                self.main_window.detect_expression()
                self._update_status('expression_detected')
            else:
                self._update_status('camera_required')
        except Exception as e:
            self.logger.error(f"❌ Error in expression detection: {e}")
    
    # Single Detection Handlers
    def _handle_age_detection(self) -> None:
        """Handle single age detection shortcut (A key)."""
        try:
            if self._is_camera_available():
                self.logger.info("🔵 Age detection triggered by keyboard")
                if hasattr(self.main_window, 'detect_age_single'):
                    self.main_window.detect_age_single()
                self._update_status('age_detected')
            else:
                self._update_status('camera_required')
        except Exception as e:
            self.logger.error(f"❌ Error in age detection: {e}")
    
    def _handle_object_detection(self) -> None:
        """Handle single object detection shortcut (O key)."""
        try:
            if self._is_camera_available():
                self.logger.info("🔵 Object detection triggered by keyboard")
                if hasattr(self.main_window, 'detect_objects_single'):
                    self.main_window.detect_objects_single()
                self._update_status('object_detected')
            else:
                self._update_status('camera_required')
        except Exception as e:
            self.logger.error(f"❌ Error in object detection: {e}")
    
    # Media Control Handlers
    def _handle_snapshot(self) -> None:
        """Handle snapshot shortcut (S key)."""
        try:
            if self._is_camera_available():
                self.logger.info("🔵 Snapshot triggered by keyboard")
                self.main_window.take_snapshot()
                self._update_status('snapshot_taken')
            else:
                self._update_status('camera_required')
        except Exception as e:
            self.logger.error(f"❌ Error taking snapshot: {e}")
    
    def _handle_recording_toggle(self) -> None:
        """Handle recording toggle shortcut (R key)."""
        try:
            if self._is_recording_available():
                self.logger.info("🔵 Recording toggle triggered by keyboard")
                self.main_window.toggle_recording()
                self._update_status('recording_toggled')
            else:
                self._update_status('camera_required')
        except Exception as e:
            self.logger.error(f"❌ Error toggling recording: {e}")
    
    # Real-time Detection Toggle Handlers
    def _handle_age_toggle(self) -> None:
        """Handle age detection toggle shortcut (1 key)."""
        try:
            if self._is_detection_available('age_btn'):
                self.logger.info("🔵 Age detection toggle triggered by keyboard")
                self.main_window.toggle_age_detection()
                self._update_status('detection_toggled')
            else:
                self._update_status('camera_required')
        except Exception as e:
            self.logger.error(f"❌ Error toggling age detection: {e}")
    
    def _handle_object_toggle(self) -> None:
        """Handle object detection toggle shortcut (2 key)."""
        try:
            if self._is_detection_available('object_btn'):
                self.logger.info("🔵 Object detection toggle triggered by keyboard")
                self.main_window.toggle_object_detection()
                self._update_status('detection_toggled')
            else:
                self._update_status('camera_required')
        except Exception as e:
            self.logger.error(f"❌ Error toggling object detection: {e}")
    
    def _handle_anomaly_toggle(self) -> None:
        """Handle anomaly detection toggle shortcut (3 key)."""
        try:
            if self._is_detection_available('anomaly_btn'):
                self.logger.info("🔵 Anomaly detection toggle triggered by keyboard")
                self.main_window.toggle_anomaly_detection()
                self._update_status('detection_toggled')
            else:
                self._update_status('camera_required')
        except Exception as e:
            self.logger.error(f"❌ Error toggling anomaly detection: {e}")
    
    # System Control Handlers
    def _handle_camera_toggle(self) -> None:
        """Handle camera toggle shortcut (Ctrl+C)."""
        try:
            self.logger.info("🔵 Camera toggle triggered by keyboard")
            self.main_window.toggle_camera()
            self._update_status('camera_toggled')
        except Exception as e:
            self.logger.error(f"❌ Error toggling camera: {e}")
    
    def _handle_escape(self) -> None:
        """Handle escape key (close dialogs, exit fullscreen)."""
        try:
            self.logger.info("🔵 Escape key triggered")
            
            # Try to close any open dialogs first
            if hasattr(self.main_window, '_close_dialogs'):
                self.main_window._close_dialogs()
            
            # Exit fullscreen if active
            if hasattr(self.main_window, 'is_fullscreen') and self.main_window.is_fullscreen:
                self._handle_fullscreen_toggle()
                
        except Exception as e:
            self.logger.error(f"❌ Error handling escape: {e}")
    
    def _handle_fullscreen_toggle(self) -> None:
        """Handle fullscreen toggle shortcut (F11)."""
        try:
            self.logger.info("🔵 Fullscreen toggle triggered by keyboard")
            
            if hasattr(self.main_window, 'toggle_fullscreen'):
                self.main_window.toggle_fullscreen()
            else:
                # Basic fullscreen toggle
                if hasattr(self.main_window, 'is_fullscreen'):
                    if self.main_window.is_fullscreen:
                        self.main_window.root.attributes('-fullscreen', False)
                        self.main_window.is_fullscreen = False
                    else:
                        self.main_window.root.attributes('-fullscreen', True)
                        self.main_window.is_fullscreen = True
                else:
                    # Initialize fullscreen state
                    self.main_window.is_fullscreen = True
                    self.main_window.root.attributes('-fullscreen', True)
            
            self._update_status('fullscreen_toggled')
            
        except Exception as e:
            self.logger.error(f"❌ Error toggling fullscreen: {e}")
    
    def _handle_refresh(self) -> None:
        """Handle refresh shortcut (F5)."""
        try:
            self.logger.info("🔵 System refresh triggered by keyboard")
            
            if hasattr(self.main_window, 'refresh_system'):
                self.main_window.refresh_system()
            else:
                # Basic refresh - update statistics and UI
                if hasattr(self.main_window, 'update_statistics'):
                    self.main_window.update_statistics()
                if hasattr(self.main_window, 'update_time'):
                    self.main_window.update_time()
            
            self._update_status('system_refreshed')
            
        except Exception as e:
            self.logger.error(f"❌ Error refreshing system: {e}")
    
    def _handle_help(self) -> None:
        """Handle help shortcut (H key)."""
        try:
            self.logger.info("🔵 Help triggered by keyboard")
            
            if hasattr(self.main_window, 'show_help'):
                self.main_window.show_help()
            else:
                # Show basic help dialog
                self._show_basic_help()
            
            self._update_status('help_shown')
            
        except Exception as e:
            self.logger.error(f"❌ Error showing help: {e}")
    
    def _handle_dashboard(self) -> None:
        """Handle dashboard shortcut (D key)."""
        try:
            self.logger.info("🔵 Dashboard triggered by keyboard")
            
            if hasattr(self.main_window, 'open_dashboard'):
                self.main_window.open_dashboard()
            
            self._update_status('dashboard_opened')
            
        except Exception as e:
            self.logger.error(f"❌ Error opening dashboard: {e}")
    
    def _handle_minimize(self) -> None:
        """Handle minimize shortcut (Ctrl+M)."""
        try:
            self.logger.info("🔵 Window minimize triggered by keyboard")
            
            if hasattr(self.main_window, 'minimize_window'):
                self.main_window.minimize_window()
            else:
                self.main_window.root.iconify()
                
        except Exception as e:
            self.logger.error(f"❌ Error minimizing window: {e}")
    
    def _handle_quit(self) -> None:
        """Handle quit shortcut (Ctrl+Q)."""
        try:
            self.logger.info("🔵 Application quit triggered by keyboard")
            
            if hasattr(self.main_window, 'on_closing'):
                self.main_window.on_closing()
            else:
                self.main_window.root.quit()
                
        except Exception as e:
            self.logger.error(f"❌ Error quitting application: {e}")
    
    # Utility Methods
    def _is_camera_available(self) -> bool:
        """Check if camera is available for operations."""
        return (hasattr(self.main_window, 'current_frame') and 
                self.main_window.current_frame is not None)
    
    def _is_recording_available(self) -> bool:
        """Check if recording functionality is available."""
        return (hasattr(self.main_window, 'record_btn') and 
                self.main_window.record_btn['state'] != 'disabled')
    
    def _is_detection_available(self, button_name: str) -> bool:
        """Check if specific detection button is available."""
        return (hasattr(self.main_window, button_name) and 
                getattr(self.main_window, button_name)['state'] != 'disabled')
    
    def _update_status(self, message_key: str) -> None:
        """Update status message in the main window."""
        try:
            if hasattr(self.main_window, 'status_message'):
                message = self.status_messages.get(message_key, "Action completed")
                self.main_window.status_message.config(text=message)
        except Exception as e:
            self.logger.error(f"❌ Error updating status: {e}")
    
    def _show_basic_help(self) -> None:
        """Show basic help dialog with keyboard shortcuts."""
        try:
            import tkinter.messagebox as messagebox
            
            help_text = """🎹 KEYBOARD SHORTCUTS

🎯 DETECTION CONTROLS:
SPACE/C - Detect facial expression
A - Single age detection  
O - Single object detection

🎥 MEDIA CONTROLS:
S - Take snapshot
R - Toggle recording

🔄 REAL-TIME TOGGLES:
1 - Toggle age detection
2 - Toggle object detection  
3 - Toggle anomaly detection

🖥️ SYSTEM CONTROLS:
Ctrl+C - Toggle camera
F11 - Toggle fullscreen
F5 - Refresh system
H - Show help
D - Open dashboard

🪟 WINDOW CONTROLS:
Ctrl+M - Minimize window
Ctrl+Q - Quit application
ESC - Close dialogs/exit fullscreen"""
            
            messagebox.showinfo("Keyboard Shortcuts", help_text)
            
        except Exception as e:
            self.logger.error(f"❌ Error showing help dialog: {e}")
    
    # Configuration Methods
    def add_shortcut(self, key: str, handler: Callable) -> None:
        """
        Add a custom keyboard shortcut.
        
        Args:
            key: The key combination (e.g., 'ctrl+n', 'f1')
            handler: The function to call when the key is pressed
        """
        try:
            self.shortcuts[key.lower()] = handler
            self.logger.info(f"✅ Added keyboard shortcut: {key}")
        except Exception as e:
            self.logger.error(f"❌ Error adding shortcut {key}: {e}")
    
    def remove_shortcut(self, key: str) -> None:
        """
        Remove a keyboard shortcut.
        
        Args:
            key: The key combination to remove
        """
        try:
            if key.lower() in self.shortcuts:
                del self.shortcuts[key.lower()]
                self.logger.info(f"✅ Removed keyboard shortcut: {key}")
        except Exception as e:
            self.logger.error(f"❌ Error removing shortcut {key}: {e}")
    
    def get_shortcuts(self) -> Dict[str, str]:
        """
        Get a dictionary of all available shortcuts and their descriptions.
        
        Returns:
            Dictionary mapping key combinations to descriptions
        """
        return {
            'space/c': 'Detect facial expression',
            'a': 'Single age detection',
            'o': 'Single object detection', 
            's': 'Take snapshot',
            'r': 'Toggle recording',
            '1': 'Toggle age detection',
            '2': 'Toggle object detection',
            '3': 'Toggle anomaly detection',
            'ctrl+c': 'Toggle camera',
            'f11': 'Toggle fullscreen',
            'f5': 'Refresh system',
            'h': 'Show help',
            'd': 'Open dashboard',
            'ctrl+m': 'Minimize window',
            'ctrl+q': 'Quit application',
            'escape': 'Close dialogs/exit fullscreen'
        }
    
    def enable_debug_mode(self, enabled: bool = True) -> None:
        """
        Enable or disable debug mode for keyboard events.
        
        Args:
            enabled: Whether to enable debug logging
        """
        try:
            if hasattr(self.main_window, 'config'):
                self.main_window.config.DEBUG_MODE = enabled
            
            level = logging.DEBUG if enabled else logging.INFO
            self.logger.setLevel(level)
            
            self.logger.info(f"🔧 Keyboard debug mode: {'enabled' if enabled else 'disabled'}")
            
        except Exception as e:
            self.logger.error(f"❌ Error setting debug mode: {e}")
    
    def __repr__(self) -> str:
        """String representation of the keyboard handler."""
        return f"KeyboardHandler(shortcuts={len(self.shortcuts)})"