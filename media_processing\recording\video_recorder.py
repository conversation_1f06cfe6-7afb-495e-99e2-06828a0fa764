"""
Video Recording Module
"""

import cv2
import os
import time
import logging
from datetime import datetime
from utils.config import Config

class VideoRecorder:
    def __init__(self):
        self.config = Config()
        self.logger = logging.getLogger(__name__)
        self.is_recording = False
        self.video_writer = None
        self.current_folder = None
        self.start_time = None
        os.makedirs(self.config.BASE_DIR, exist_ok=True)
    
    def start_recording(self, first_frame):
        try:
            if self.is_recording:
                return False
            
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            self.current_folder = os.path.join(self.config.BASE_DIR, f"Detection_{timestamp}")
            os.makedirs(self.current_folder, exist_ok=True)
            
            height, width = first_frame.shape[:2]
            video_path = os.path.join(self.current_folder, "recording.mp4")
            
            fourcc = cv2.VideoWriter_fourcc(*'mp4v')
            self.video_writer = cv2.VideoWriter(video_path, fourcc, 30, (width, height))
            
            screenshot_path = os.path.join(self.current_folder, "screenshot.jpg")
            cv2.imwrite(screenshot_path, first_frame)
            
            self.is_recording = True
            self.start_time = time.time()
            return True
            
        except Exception as e:
            self.logger.error(f"Error starting recording: {e}")
            return False
    
    def write_frame(self, frame):
        if self.is_recording and self.video_writer:
            self.video_writer.write(frame)
    
    def stop_recording(self):
        if self.video_writer:
            self.video_writer.release()
        self.is_recording = False
        return True
    
    def get_recording_status(self):
        return {"status": "stopped" if not self.is_recording else "recording"}