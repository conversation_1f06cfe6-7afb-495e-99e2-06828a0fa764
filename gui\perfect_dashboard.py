"""
Perfect Database Alignment Dashboard
Shows exact database contents with 100% accuracy
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import os
from datetime import datetime, timedelta
import sqlite3
from collections import Counter

# Enhanced database integration
try:
    from utils.database_integration import get_database
    DATABASE_INTEGRATION_AVAILABLE = True
    print("✅ Enhanced database integration available")
except ImportError:
    DATABASE_INTEGRATION_AVAILABLE = False
    print("⚠️ Enhanced database integration not available")

class PerfectDashboard:
    """Dashboard with perfect database alignment"""
    
    def __init__(self):
        self.root = None
        self.db = None
        self.current_data = {}
        self.auto_refresh = True
        self.refresh_interval = 3000  # 3 seconds for real-time updates
        self.after_id = None
        
        # Initialize database connection
        if DATABASE_INTEGRATION_AVAILABLE:
            try:
                self.db = get_database()
                print(f"✅ Connected to database: {self.db.db_path}")
                print(f"📊 Session ID: {self.db.session_id}")
            except Exception as e:
                print(f"❌ Database connection failed: {e}")
                self.db = None
        
        # Detection types
        self.detection_types = ['age', 'object', 'expression', 'anomaly']
    
    def show_dashboard(self):
        """Show the perfect alignment dashboard"""
        try:
            self.root = tk.Toplevel()
            self.root.title("🎯 Perfect Database Alignment Dashboard")
            self.root.geometry("1400x900")
            self.root.configure(bg='#F8F9FA')
            
            # Create main interface
            self.create_header()
            self.create_database_stats()
            self.create_real_time_view()
            self.create_controls()
            
            # Start with immediate data load
            self.refresh_data()
            
            # Start auto-refresh
            if self.auto_refresh:
                self.start_auto_refresh()
            
            print("✅ Perfect dashboard opened")
            
        except Exception as e:
            print(f"❌ Error opening dashboard: {e}")
            messagebox.showerror("Dashboard Error", f"Failed to open dashboard: {e}")
    
    def create_header(self):
        """Create dashboard header"""
        header_frame = tk.Frame(self.root, bg='#2C3E50', height=80)
        header_frame.pack(fill='x')
        header_frame.pack_propagate(False)
        
        # Title
        title_label = tk.Label(header_frame, text="🎯 Perfect Database Alignment Dashboard",
                              font=('Arial', 18, 'bold'), bg='#2C3E50', fg='white')
        title_label.pack(side='left', padx=20, pady=20)
        
        # Database info
        if self.db:
            db_info = tk.Label(header_frame, text=f"📊 DB: {os.path.basename(self.db.db_path)} | Session: {self.db.session_id[:8]}...",
                              font=('Arial', 10), bg='#2C3E50', fg='#BDC3C7')
            db_info.pack(side='right', padx=20, pady=20)
        
        # Status
        self.status_label = tk.Label(header_frame, text="🟢 LIVE",
                                   font=('Arial', 12, 'bold'), bg='#2C3E50', fg='#2ECC71')
        self.status_label.pack(side='right', padx=10, pady=20)
    
    def create_database_stats(self):
        """Create real-time database statistics"""
        stats_frame = tk.Frame(self.root, bg='white', relief='solid', bd=1)
        stats_frame.pack(fill='x', padx=10, pady=5)
        
        tk.Label(stats_frame, text="📊 Live Database Statistics",
                font=('Arial', 14, 'bold'), bg='white', fg='#2C3E50').pack(pady=10)
        
        # Statistics container
        self.stats_container = tk.Frame(stats_frame, bg='white')
        self.stats_container.pack(fill='x', padx=20, pady=10)
        
        # Configure grid
        for i in range(5):
            self.stats_container.grid_columnconfigure(i, weight=1)
        
        # Create stat cards
        self.stat_labels = {}
        self.create_stat_card("👶 Age", "0", "#E67E22", 0, 0)
        self.create_stat_card("🔍 Objects", "0", "#16A085", 0, 1)
        self.create_stat_card("😊 Expressions", "0", "#8E44AD", 0, 2)
        self.create_stat_card("🚨 Anomalies", "0", "#E74C3C", 0, 3)
        self.create_stat_card("📊 Total", "0", "#3498DB", 0, 4)
    
    def create_stat_card(self, title, value, color, row, col):
        """Create individual statistic card"""
        card_frame = tk.Frame(self.stats_container, bg='#ECF0F1', relief='solid', bd=1)
        card_frame.grid(row=row, column=col, padx=5, pady=5, sticky='nsew')
        
        # Colored header
        header_frame = tk.Frame(card_frame, bg=color, height=4)
        header_frame.pack(fill='x')
        
        # Content
        content_frame = tk.Frame(card_frame, bg='#ECF0F1')
        content_frame.pack(fill='both', expand=True, padx=10, pady=10)
        
        # Title
        title_label = tk.Label(content_frame, text=title, font=('Arial', 10, 'bold'),
                              bg='#ECF0F1', fg='#2C3E50')
        title_label.pack()
        
        # Value
        value_label = tk.Label(content_frame, text=value, font=('Arial', 16, 'bold'),
                              bg='#ECF0F1', fg=color)
        value_label.pack(pady=5)
        
        # Store reference for updates
        key = title.split()[1].lower() if len(title.split()) > 1 else title.lower()
        self.stat_labels[key] = value_label
    
    def create_real_time_view(self):
        """Create real-time data view"""
        view_frame = tk.Frame(self.root, bg='white', relief='solid', bd=1)
        view_frame.pack(fill='both', expand=True, padx=10, pady=5)
        
        tk.Label(view_frame, text="🔄 Real-Time Database View",
                font=('Arial', 14, 'bold'), bg='white', fg='#2C3E50').pack(pady=10)
        
        # Create notebook for different views
        self.notebook = ttk.Notebook(view_frame)
        self.notebook.pack(fill='both', expand=True, padx=20, pady=10)
        
        # Live data tab
        self.live_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.live_frame, text="📊 Live Data")
        
        # Create treeview for live data
        columns = ('Type', 'Timestamp', 'Value', 'Confidence', 'Details')
        self.live_tree = ttk.Treeview(self.live_frame, columns=columns, show='headings', height=15)
        
        for col in columns:
            self.live_tree.heading(col, text=col)
            self.live_tree.column(col, width=150)
        
        # Add scrollbar
        live_scrollbar = ttk.Scrollbar(self.live_frame, orient='vertical', command=self.live_tree.yview)
        self.live_tree.configure(yscrollcommand=live_scrollbar.set)
        
        self.live_tree.pack(side='left', fill='both', expand=True)
        live_scrollbar.pack(side='right', fill='y')
        
        # Database viewer tab
        self.db_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.db_frame, text="🗄️ Database Tables")
        
        # Add database viewer button
        tk.Button(self.db_frame, text="🗄️ View Complete Database", 
                 command=self.show_database_viewer, bg='#3498DB', fg='white',
                 font=('Arial', 12, 'bold'), pady=10).pack(pady=20)
        
        # Database summary
        self.db_summary_text = tk.Text(self.db_frame, height=20, font=('Courier', 10),
                                      bg='#F8F9FA', fg='#2C3E50', wrap='word')
        self.db_summary_text.pack(fill='both', expand=True, padx=10, pady=10)
    
    def create_controls(self):
        """Create control panel"""
        controls_frame = tk.Frame(self.root, bg='white', relief='solid', bd=1)
        controls_frame.pack(fill='x', padx=10, pady=5)
        
        # Auto-refresh control
        refresh_frame = tk.Frame(controls_frame, bg='white')
        refresh_frame.pack(side='left', padx=20, pady=10)
        
        self.auto_refresh_var = tk.BooleanVar(value=True)
        auto_cb = tk.Checkbutton(refresh_frame, text="🔄 Auto-Refresh (3s)", 
                               variable=self.auto_refresh_var, bg='white',
                               command=self.toggle_auto_refresh, font=('Arial', 10))
        auto_cb.pack()
        
        # Manual refresh button
        tk.Button(refresh_frame, text="🔄 Refresh Now", command=self.refresh_data,
                 bg='#27AE60', fg='white', font=('Arial', 10, 'bold')).pack(pady=5)
        
        # Status display
        status_frame = tk.Frame(controls_frame, bg='white')
        status_frame.pack(side='right', padx=20, pady=10)
        
        self.last_refresh_label = tk.Label(status_frame, text="Ready", 
                                         font=('Arial', 9), bg='white', fg='#7F8C8D')
        self.last_refresh_label.pack()
    
    def refresh_data(self):
        """Refresh data with perfect database alignment"""
        try:
            print("🔄 Refreshing data with perfect database alignment...")
            
            if not self.db:
                print("❌ No database connection")
                return
            
            # Get EXACT database counts
            exact_counts = {}
            total_records = 0
            
            for det_type in self.detection_types:
                try:
                    records = self.db.get_recent_detections(det_type, limit=50000)
                    exact_counts[det_type] = len(records)
                    total_records += len(records)
                    self.current_data[det_type] = records
                    print(f"📊 {det_type.upper()}: {len(records)} records")
                except Exception as e:
                    print(f"❌ Error getting {det_type} data: {e}")
                    exact_counts[det_type] = 0
                    self.current_data[det_type] = []
            
            # Update statistics with EXACT counts
            self.update_statistics(exact_counts, total_records)
            
            # Update live view
            self.update_live_view()
            
            # Update database summary
            self.update_database_summary()
            
            # Update status
            self.last_refresh_label.config(text=f"Last: {datetime.now().strftime('%H:%M:%S')} | Total: {total_records:,}")
            
            print(f"✅ Perfect alignment: {total_records:,} total records")
            
        except Exception as e:
            print(f"❌ Error refreshing data: {e}")
    
    def update_statistics(self, counts, total):
        """Update statistics with exact database counts"""
        try:
            # Update individual counts
            if 'age' in self.stat_labels:
                self.stat_labels['age'].config(text=f"{counts.get('age', 0):,}")
            if 'objects' in self.stat_labels:
                self.stat_labels['objects'].config(text=f"{counts.get('object', 0):,}")
            if 'expressions' in self.stat_labels:
                self.stat_labels['expressions'].config(text=f"{counts.get('expression', 0):,}")
            if 'anomalies' in self.stat_labels:
                self.stat_labels['anomalies'].config(text=f"{counts.get('anomaly', 0):,}")
            if 'total' in self.stat_labels:
                self.stat_labels['total'].config(text=f"{total:,}")
            
            print(f"📊 Statistics updated: Age={counts.get('age', 0)}, Objects={counts.get('object', 0)}, Expressions={counts.get('expression', 0)}, Anomalies={counts.get('anomaly', 0)}, Total={total}")
            
        except Exception as e:
            print(f"❌ Error updating statistics: {e}")
    
    def update_live_view(self):
        """Update live data view"""
        try:
            # Clear existing items
            for item in self.live_tree.get_children():
                self.live_tree.delete(item)
            
            # Add latest records from each type
            all_records = []
            
            for det_type, records in self.current_data.items():
                for record in records[-20:]:  # Last 20 records per type
                    timestamp = str(record.get('timestamp', ''))[:19]
                    confidence = f"{record.get('confidence', 0):.3f}"
                    
                    if det_type == 'age':
                        value = f"{record.get('age', 0)} years"
                        details = f"Range: {record.get('age_range', 'Unknown')}"
                    elif det_type == 'object':
                        value = record.get('object_name', 'unknown')
                        details = f"Method: {record.get('detection_method', 'Unknown')}"
                    elif det_type == 'expression':
                        value = record.get('expression', 'unknown')
                        details = f"Model: {record.get('model_used', 'Unknown')}"
                    elif det_type == 'anomaly':
                        value = record.get('anomaly_type', 'unknown')
                        details = f"Threat: {record.get('threat_level', 'LOW')}"
                    
                    all_records.append((det_type.title(), timestamp, value, confidence, details))
            
            # Sort by timestamp (newest first)
            all_records.sort(key=lambda x: x[1], reverse=True)
            
            # Add to treeview
            for record in all_records[:100]:  # Show last 100 records
                self.live_tree.insert('', 'end', values=record)
            
        except Exception as e:
            print(f"❌ Error updating live view: {e}")
    
    def update_database_summary(self):
        """Update database summary"""
        try:
            if not self.db:
                return
            
            summary = "🗄️ DATABASE SUMMARY - LIVE VIEW\n"
            summary += "=" * 50 + "\n\n"
            
            summary += f"Database Path: {self.db.db_path}\n"
            summary += f"Session ID: {self.db.session_id}\n"
            summary += f"Last Updated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n"
            
            total_all = 0
            for det_type in self.detection_types:
                records = self.current_data.get(det_type, [])
                count = len(records)
                total_all += count
                
                summary += f"{det_type.upper()} DETECTIONS: {count:,} records\n"
                
                if records:
                    latest = records[0]
                    summary += f"  Latest: {latest.get('timestamp', 'Unknown')}\n"
                    
                    confidences = [r.get('confidence', 0) for r in records if r.get('confidence')]
                    if confidences:
                        avg_conf = sum(confidences) / len(confidences)
                        summary += f"  Avg Confidence: {avg_conf:.1%}\n"
                
                summary += "\n"
            
            summary += f"TOTAL RECORDS: {total_all:,}\n"
            summary += f"PERFECT ALIGNMENT: Dashboard = Database\n"
            
            # Human vs Person check
            object_records = self.current_data.get('object', [])
            human_count = sum(1 for r in object_records if r.get('object_name') == 'human')
            person_count = sum(1 for r in object_records if r.get('object_name') == 'person')
            
            summary += f"\nHUMAN/PERSON CONSISTENCY:\n"
            summary += f"  'human' detections: {human_count}\n"
            summary += f"  'person' detections: {person_count}\n"
            summary += f"  Status: {'✅ CONSISTENT' if person_count == 0 else '❌ INCONSISTENT'}\n"
            
            self.db_summary_text.delete('1.0', 'end')
            self.db_summary_text.insert('1.0', summary)
            
        except Exception as e:
            print(f"❌ Error updating database summary: {e}")
    
    def show_database_viewer(self):
        """Show complete database viewer"""
        try:
            from gui.enhanced_dashboard import StreamlinedDashboard
            
            # Create database viewer window
            db_window = tk.Toplevel(self.root)
            db_window.title("🗄️ Complete Database Viewer")
            db_window.geometry("1200x800")
            db_window.configure(bg='white')
            
            # Use the enhanced dashboard's database viewer
            dashboard = StreamlinedDashboard()
            
            # Create notebook for tables
            notebook = ttk.Notebook(db_window)
            notebook.pack(fill='both', expand=True, padx=10, pady=10)
            
            for det_type in self.detection_types:
                tab_frame = ttk.Frame(notebook)
                notebook.add(tab_frame, text=f"{det_type.title()} ({len(self.current_data.get(det_type, []))} records)")
                
                # Create text widget to show data
                text_widget = tk.Text(tab_frame, font=('Courier', 9), wrap='none')
                text_widget.pack(fill='both', expand=True, padx=5, pady=5)
                
                # Add data
                records = self.current_data.get(det_type, [])
                content = f"{det_type.upper()} DETECTION RECORDS\n"
                content += "=" * 80 + "\n\n"
                
                for i, record in enumerate(records[-500:], 1):  # Show last 500
                    content += f"Record {i}:\n"
                    for key, value in record.items():
                        content += f"  {key}: {value}\n"
                    content += "\n"
                
                text_widget.insert('1.0', content)
                text_widget.config(state='disabled')
            
            print("✅ Database viewer opened")
            
        except Exception as e:
            print(f"❌ Error showing database viewer: {e}")
            messagebox.showerror("Database Viewer Error", f"Failed to show database viewer: {e}")
    
    def toggle_auto_refresh(self):
        """Toggle auto-refresh"""
        self.auto_refresh = self.auto_refresh_var.get()
        if self.auto_refresh:
            self.start_auto_refresh()
        else:
            self.stop_auto_refresh()
    
    def start_auto_refresh(self):
        """Start auto-refresh"""
        if self.auto_refresh and self.root:
            self.refresh_data()
            self.after_id = self.root.after(self.refresh_interval, self.start_auto_refresh)
    
    def stop_auto_refresh(self):
        """Stop auto-refresh"""
        if self.after_id and self.root:
            self.root.after_cancel(self.after_id)
            self.after_id = None

def create_perfect_dashboard():
    """Create and show perfect dashboard"""
    try:
        dashboard = PerfectDashboard()
        dashboard.show_dashboard()
        return dashboard
    except Exception as e:
        print(f"❌ Error creating perfect dashboard: {e}")
        messagebox.showerror("Dashboard Error", f"Failed to create dashboard: {e}")
        return None

if __name__ == "__main__":
    print("🎯 PERFECT DATABASE ALIGNMENT DASHBOARD")
    print("=" * 60)
    
    # Create test window
    root = tk.Tk()
    root.withdraw()  # Hide main window
    
    # Create and show perfect dashboard
    dashboard = create_perfect_dashboard()
    
    if dashboard:
        print("✅ Perfect dashboard launched!")
        root.mainloop()
    else:
        print("❌ Failed to launch dashboard")
