#!/usr/bin/env python3
"""
Enhanced Detection Configuration for Custom YOLOv8 Emotion Detection
Provides configurable parameters for post-processing and analysis
"""

class DetectionConfig:
    """Configuration class for enhanced emotion detection parameters"""
    
    def __init__(self):
        # Confidence Threshold Settings
        self.confidence_threshold = 0.5  # Default confidence threshold
        self.min_confidence = 0.3        # Minimum allowed confidence
        self.max_confidence = 0.9        # Maximum allowed confidence
        
        # Temporal Smoothing Settings
        self.temporal_smoothing_enabled = True
        self.smoothing_window_size = 5   # Number of frames for smoothing
        self.stability_threshold = 0.7   # Minimum stability score
        
        # Face Quality Assessment
        self.face_quality_enabled = True
        self.min_face_size = 60          # Minimum face size in pixels
        self.max_face_size = 400         # Maximum face size in pixels
        self.blur_threshold = 100        # Laplacian variance threshold for blur
        self.brightness_min = 50         # Minimum brightness level
        self.brightness_max = 200        # Maximum brightness level
        
        # Non-Maximum Suppression
        self.nms_enabled = True
        self.nms_threshold = 0.4         # IoU threshold for NMS
        
        # Detection Analysis
        self.show_top_emotions = 3       # Number of top emotions to display
        self.emotion_history_size = 5    # Number of recent detections to track
        
        # Performance Settings
        self.target_fps = 3.0           # Target FPS for real-time detection
        self.max_inference_time = 1.0   # Maximum allowed inference time
        
        # Export Settings
        self.export_enabled = True
        self.export_format = 'json'     # 'json' or 'csv'
        self.export_directory = 'detection_results'
        
        # Emotion Intensity Levels
        self.intensity_levels = {
            'low': (0.3, 0.5),
            'medium': (0.5, 0.7),
            'high': (0.7, 1.0)
        }
        
        # Emotion Colors for Visualization
        self.emotion_colors = {
            'Anger': '#E74C3C',      # Red
            'Contempt': '#8E44AD',   # Purple
            'Disgust': '#E67E22',    # Orange
            'Fear': '#9B59B6',       # Violet
            'Happy': '#2ECC71',      # Green
            'Neutral': '#34495E',    # Dark Gray
            'Sad': '#3498DB',        # Blue
            'Surprise': '#F1C40F'    # Yellow
        }
        
        # Quality Assessment Weights
        self.quality_weights = {
            'size': 0.3,
            'blur': 0.3,
            'brightness': 0.2,
            'confidence': 0.2
        }
    
    def update_confidence_threshold(self, threshold: float) -> bool:
        """Update confidence threshold with validation"""
        if self.min_confidence <= threshold <= self.max_confidence:
            self.confidence_threshold = threshold
            return True
        return False
    
    def get_emotion_intensity(self, confidence: float) -> str:
        """Get emotion intensity level based on confidence"""
        for level, (min_conf, max_conf) in self.intensity_levels.items():
            if min_conf <= confidence < max_conf:
                return level
        return 'high' if confidence >= 0.7 else 'low'
    
    def get_emotion_color(self, emotion: str) -> str:
        """Get color for emotion visualization"""
        return self.emotion_colors.get(emotion, '#95A5A6')
    
    def to_dict(self) -> dict:
        """Convert configuration to dictionary"""
        return {
            'confidence_threshold': self.confidence_threshold,
            'temporal_smoothing_enabled': self.temporal_smoothing_enabled,
            'smoothing_window_size': self.smoothing_window_size,
            'face_quality_enabled': self.face_quality_enabled,
            'min_face_size': self.min_face_size,
            'max_face_size': self.max_face_size,
            'blur_threshold': self.blur_threshold,
            'nms_enabled': self.nms_enabled,
            'nms_threshold': self.nms_threshold,
            'show_top_emotions': self.show_top_emotions,
            'target_fps': self.target_fps
        }
    
    def from_dict(self, config_dict: dict):
        """Load configuration from dictionary"""
        for key, value in config_dict.items():
            if hasattr(self, key):
                setattr(self, key, value)
    
    def save_to_file(self, filepath: str):
        """Save configuration to JSON file"""
        import json
        with open(filepath, 'w') as f:
            json.dump(self.to_dict(), f, indent=4)
    
    def load_from_file(self, filepath: str):
        """Load configuration from JSON file"""
        import json
        try:
            with open(filepath, 'r') as f:
                config_dict = json.load(f)
                self.from_dict(config_dict)
            return True
        except (FileNotFoundError, json.JSONDecodeError):
            return False

# Global configuration instance
detection_config = DetectionConfig()
