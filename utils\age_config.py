#!/usr/bin/env python3
"""
Enhanced Age Detection Configuration
Optimized settings for accurate real-time age estimation using Caffe models
"""

import os
from typing import Dict, List, Tuple

class AgeDetectionConfig:
    """Configuration class for enhanced age detection parameters"""
    
    def __init__(self):
        # Model Configuration
        self.model_paths = {
            'prototxt': [
                "models/age_deploy.prototxt",
                os.path.join(os.getcwd(), "models", "age_deploy.prototxt"),
                os.path.abspath("models/age_deploy.prototxt")
            ],
            'caffemodel': [
                "models/age_net.caffemodel", 
                os.path.join(os.getcwd(), "models", "age_net.caffemodel"),
                os.path.abspath("models/age_net.caffemodel")
            ]
        }
        
        # Age Range Definitions (8 standard classes)
        self.age_ranges = [
            '(0-2)', '(4-6)', '(8-12)', '(15-20)', 
            '(25-32)', '(38-43)', '(48-53)', '(60-100)'
        ]
        
        # Enhanced Age Categories
        self.age_categories = {
            '(0-2)': {'category': 'Baby', 'emoji': '👶', 'avg_age': 1, 'color': (255, 192, 203)},
            '(4-6)': {'category': 'Child', 'emoji': '🧒', 'avg_age': 5, 'color': (0, 255, 255)},
            '(8-12)': {'category': 'Kid', 'emoji': '👦', 'avg_age': 10, 'color': (0, 255, 0)},
            '(15-20)': {'category': 'Teenager', 'emoji': '👨‍🎓', 'avg_age': 17, 'color': (255, 165, 0)},
            '(25-32)': {'category': 'Young Adult', 'emoji': '👨', 'avg_age': 28, 'color': (255, 0, 255)},
            '(38-43)': {'category': 'Adult', 'emoji': '👨‍💼', 'avg_age': 40, 'color': (0, 0, 255)},
            '(48-53)': {'category': 'Middle-aged', 'emoji': '👨‍🦳', 'avg_age': 50, 'color': (128, 0, 128)},
            '(60-100)': {'category': 'Elderly', 'emoji': '👴', 'avg_age': 70, 'color': (128, 128, 128)}
        }
        
        # Face Detection Settings
        self.face_detection = {
            'dnn_confidence_threshold': 0.5,  # Balanced threshold for detection
            'opencv_scale_factor': 1.1,
            'opencv_min_neighbors': 5,
            'min_face_size': (60, 60),  # More permissive minimum for better detection
            'max_face_size': (400, 400),
            'face_quality_threshold': 0.2  # Lower threshold for more permissive detection
        }
        
        # Age Prediction Settings
        self.prediction = {
            'confidence_threshold': 0.1,  # Lower threshold for more permissive predictions
            'input_size': (227, 227),  # Caffe model input size
            'mean_values': (78.4263377603, 87.7689143744, 114.895847746),  # ImageNet mean
            'scale_factor': 1.0,
            'swap_rb': False,  # Caffe expects BGR
            'crop': False
        }
        
        # Temporal Smoothing Settings
        self.temporal_smoothing = {
            'enabled': True,
            'window_size': 5,  # Number of frames to smooth over
            'confidence_weight': 0.7,  # Weight for confidence in smoothing
            'stability_threshold': 0.6  # Minimum stability for reliable prediction
        }
        
        # Performance Settings
        self.performance = {
            'detection_cooldown': 0.5,  # Seconds between detections
            'max_faces_per_frame': 3,  # Limit for performance
            'target_fps': 3.0,
            'max_inference_time': 0.5,  # Maximum allowed inference time
            'enable_gpu': False  # Set to True if CUDA available
        }
        
        # Quality Assessment Settings
        self.quality_assessment = {
            'enabled': True,
            'blur_threshold': 50,  # Lower threshold for more permissive blur detection
            'brightness_range': (30, 220),  # Wider brightness range
            'contrast_threshold': 15,  # Lower minimum contrast
            'face_area_threshold': 3600,  # Lower minimum face area (60x60)
            'aspect_ratio_range': (0.5, 2.0)  # Wider aspect ratio range
        }
        
        # Validation Settings
        self.validation = {
            'enabled': True,
            'age_consistency_check': True,  # Check for realistic age transitions
            'max_age_jump': 2,  # Maximum age category jump between frames
            'confidence_decay': 0.9,  # Confidence decay for inconsistent predictions
            'outlier_rejection': True  # Reject obvious outliers
        }
        
        # Display Settings
        self.display = {
            'show_confidence': True,
            'show_category': True,
            'show_emoji': True,
            'show_bounding_box': True,
            'annotation_font_scale': 0.7,
            'annotation_thickness': 2,
            'box_thickness': 3
        }
        
        # History and Tracking
        self.history = {
            'max_history_size': 20,
            'track_performance': True,
            'save_detection_logs': False,
            'log_directory': 'age_detection_logs'
        }
    
    def get_age_info(self, age_range: str) -> Dict:
        """Get comprehensive age information for a given age range"""
        return self.age_categories.get(age_range, {
            'category': 'Unknown',
            'emoji': '❓',
            'avg_age': 25,
            'color': (128, 128, 128)
        })
    
    def is_valid_age_range(self, age_range: str) -> bool:
        """Check if age range is valid"""
        return age_range in self.age_ranges
    
    def get_age_color(self, age_range: str) -> Tuple[int, int, int]:
        """Get BGR color for age range"""
        age_info = self.get_age_info(age_range)
        return age_info.get('color', (128, 128, 128))
    
    def validate_face_quality(self, face_roi, bbox) -> Tuple[bool, float, Dict]:
        """Validate face quality for age detection"""
        if not self.quality_assessment['enabled']:
            return True, 1.0, {}
        
        try:
            import cv2
            import numpy as np
            
            x, y, w, h = bbox
            quality_score = 0.0
            quality_metrics = {}
            
            # Size check
            face_area = w * h
            if face_area < self.quality_assessment['face_area_threshold']:
                return False, 0.0, {'reason': 'face_too_small', 'area': face_area}
            
            # Aspect ratio check
            aspect_ratio = w / h
            min_ratio, max_ratio = self.quality_assessment['aspect_ratio_range']
            if not (min_ratio <= aspect_ratio <= max_ratio):
                return False, 0.0, {'reason': 'invalid_aspect_ratio', 'ratio': aspect_ratio}
            
            # Convert to grayscale for quality analysis
            if len(face_roi.shape) == 3:
                gray_face = cv2.cvtColor(face_roi, cv2.COLOR_BGR2GRAY)
            else:
                gray_face = face_roi
            
            # Blur assessment
            blur_variance = cv2.Laplacian(gray_face, cv2.CV_64F).var()
            blur_quality = min(1.0, blur_variance / self.quality_assessment['blur_threshold'])
            quality_metrics['blur_variance'] = blur_variance
            quality_metrics['blur_quality'] = blur_quality
            
            # Brightness assessment
            brightness = np.mean(gray_face)
            min_bright, max_bright = self.quality_assessment['brightness_range']
            if min_bright <= brightness <= max_bright:
                brightness_quality = 1.0
            else:
                brightness_quality = max(0.0, 1.0 - abs(brightness - 127.5) / 127.5)
            quality_metrics['brightness'] = brightness
            quality_metrics['brightness_quality'] = brightness_quality
            
            # Contrast assessment
            contrast = np.std(gray_face)
            contrast_quality = min(1.0, contrast / self.quality_assessment['contrast_threshold'])
            quality_metrics['contrast'] = contrast
            quality_metrics['contrast_quality'] = contrast_quality
            
            # Overall quality score
            quality_score = (blur_quality * 0.4 + brightness_quality * 0.3 + contrast_quality * 0.3)
            quality_metrics['overall_quality'] = quality_score
            
            is_valid = quality_score >= self.face_detection['face_quality_threshold']
            
            return is_valid, quality_score, quality_metrics
            
        except Exception as e:
            return True, 0.5, {'error': str(e)}  # Default to valid on error
    
    def to_dict(self) -> Dict:
        """Convert configuration to dictionary"""
        return {
            'age_ranges': self.age_ranges,
            'face_detection': self.face_detection,
            'prediction': self.prediction,
            'temporal_smoothing': self.temporal_smoothing,
            'performance': self.performance,
            'quality_assessment': self.quality_assessment,
            'validation': self.validation,
            'display': self.display,
            'history': self.history
        }

# Global configuration instance
age_config = AgeDetectionConfig()
