#!/usr/bin/env python3
"""
AI Detection Dependencies Installation Script
Installs all required packages for the AI Video Detection application
"""

import subprocess
import sys
import os

def run_command(command):
    """Run a command and return success status"""
    try:
        print(f"🔄 Running: {command}")
        result = subprocess.run(command, shell=True, check=True, capture_output=True, text=True)
        print(f"✅ Success: {command}")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed: {command}")
        print(f"Error: {e.stderr}")
        return False

def install_dependencies():
    """Install all AI detection dependencies"""
    print("🚀 Installing AI Video Detection Dependencies...")
    print("=" * 60)
    
    # Core AI/ML packages
    core_packages = [
        "opencv-python>=4.8.0",
        "opencv-contrib-python>=4.8.0",
        "numpy>=1.21.0",
        "pillow>=9.0.0",
        "matplotlib>=3.5.0",
        "scikit-learn>=1.1.0",
        "scipy>=1.9.0"
    ]
    
    # Deep Learning packages
    dl_packages = [
        "torch>=1.12.0",
        "torchvision>=0.13.0",
        "ultralytics>=8.0.0",  # YOLOv8
        "tensorflow>=2.10.0",
        "keras>=2.10.0"
    ]
    
    # Computer Vision packages
    cv_packages = [
        "mediapipe>=0.9.0",
        "dlib>=19.24.0",
        "face-recognition>=1.3.0",
        "mtcnn>=0.1.1",
        "insightface>=0.7.0"
    ]
    
    # Additional utilities
    util_packages = [
        "requests>=2.28.0",
        "tqdm>=4.64.0",
        "psutil>=5.9.0",
        "python-dateutil>=2.8.0",
        "packaging>=21.0"
    ]
    
    all_packages = core_packages + dl_packages + cv_packages + util_packages
    
    print("📦 Installing Core AI/ML Packages...")
    for package in core_packages:
        run_command(f"pip install {package}")
    
    print("\n🧠 Installing Deep Learning Packages...")
    for package in dl_packages:
        run_command(f"pip install {package}")
    
    print("\n👁️ Installing Computer Vision Packages...")
    for package in cv_packages:
        run_command(f"pip install {package}")
    
    print("\n🔧 Installing Utility Packages...")
    for package in util_packages:
        run_command(f"pip install {package}")
    
    print("\n✅ All dependencies installed!")
    print("=" * 60)

def verify_installation():
    """Verify that all packages are properly installed"""
    print("🔍 Verifying Installation...")
    
    test_imports = [
        ("cv2", "OpenCV"),
        ("numpy", "NumPy"),
        ("PIL", "Pillow"),
        ("torch", "PyTorch"),
        ("ultralytics", "YOLOv8"),
        ("tensorflow", "TensorFlow"),
        ("mediapipe", "MediaPipe"),
        ("sklearn", "Scikit-learn")
    ]
    
    success_count = 0
    for module, name in test_imports:
        try:
            __import__(module)
            print(f"✅ {name} - OK")
            success_count += 1
        except ImportError:
            print(f"❌ {name} - FAILED")
    
    print(f"\n📊 Installation Summary: {success_count}/{len(test_imports)} packages working")
    
    if success_count == len(test_imports):
        print("🎉 All AI detection dependencies are ready!")
        return True
    else:
        print("⚠️ Some packages failed to install. Please check the errors above.")
        return False

def download_models():
    """Download required AI models"""
    print("📥 Downloading AI Models...")
    
    # Create models directory
    os.makedirs("models", exist_ok=True)
    
    # YOLOv8 models will be downloaded automatically by ultralytics
    print("✅ YOLOv8 models will be downloaded automatically on first use")
    
    # MediaPipe models will be downloaded automatically
    print("✅ MediaPipe models will be downloaded automatically on first use")
    
    print("📁 Models directory created: ./models/")

if __name__ == "__main__":
    print("🛡️ AI Video Detection - Dependency Installer")
    print("=" * 60)
    
    # Install dependencies
    install_dependencies()
    
    # Verify installation
    if verify_installation():
        # Download models
        download_models()
        
        print("\n🎯 Installation Complete!")
        print("You can now run the AI Video Detection application:")
        print("python main.py")
    else:
        print("\n❌ Installation incomplete. Please resolve the errors above.")
        sys.exit(1)
