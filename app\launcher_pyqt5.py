#!/usr/bin/env python3
"""
AI Video Detection - PyQt5 Application Launcher
Enhanced version with PyQt5 GUI framework and improved error handling
"""

import sys
import os

# Add the project root to the path
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.append(project_root)

def print_safe(text, fallback_text=None):
    """Print text with Unicode error handling"""
    try:
        print(text)
    except UnicodeEncodeError:
        if fallback_text:
            print(fallback_text)
        else:
            # Remove emojis and special characters
            safe_text = text.encode('ascii', 'ignore').decode('ascii')
            print(safe_text)

def check_pyqt5_installation():
    """Check if PyQt5 is properly installed"""
    try:
        from PyQt5.QtWidgets import QApplication
        from PyQt5.QtCore import QT_VERSION_STR
        print(f"✅ PyQt5 is installed (version: {QT_VERSION_STR})")
        return True
    except ImportError as e:
        print(f"❌ PyQt5 not found: {e}")
        print("📦 Install PyQt5 with: pip install PyQt5>=5.15.0")
        return False

def check_dependencies():
    """Check all required dependencies"""
    dependencies = {
        'PyQt5': 'PyQt5.QtWidgets',
        'opencv-python': 'cv2',
        'numpy': 'numpy',
        'pillow': 'PIL',
        'datetime': 'datetime'
    }
    
    available = []
    missing = []
    
    for package, module in dependencies.items():
        try:
            __import__(module)
            available.append(package)
        except ImportError:
            missing.append(package)
    
    print(f"✅ Available dependencies: {', '.join(available)}")
    if missing:
        print(f"⚠️ Missing dependencies: {', '.join(missing)}")
        print("📦 Install missing packages with: pip install " + " ".join(missing))
    
    return len(missing) == 0

def create_directory_structure():
    """Create necessary directories if they don't exist"""
    directories = [
        'gui_pyqt5',
        'utils', 
        'detection',
        'recording',
        'models',
        'logs',
        'reports',
        'snapshots',
        'Security_Footage'
    ]
    
    for directory in directories:
        os.makedirs(directory, exist_ok=True)
        
        # Create __init__.py files for Python packages
        if directory in ['gui_pyqt5', 'utils', 'detection', 'recording']:
            init_file = os.path.join(directory, '__init__.py')
            if not os.path.exists(init_file):
                with open(init_file, 'w') as f:
                    f.write('# Package initialization\n')

def main():
    """Main entry point for the PyQt5 application"""
    print_safe("🛡️ AI Video Detection - PyQt5 Enhanced Interface", 
               "AI Video Detection - PyQt5 Enhanced Interface")
    print("=" * 60)
    print()
    
    print_safe("🎨 PyQt5 Features:", "PyQt5 Features:")
    print_safe("   ✅ Modern PyQt5 GUI framework", 
               "   - Modern PyQt5 GUI framework")
    print_safe("   ✅ Professional desktop interface", 
               "   - Professional desktop interface")
    print_safe("   ✅ Enhanced performance and stability", 
               "   - Enhanced performance and stability")
    print_safe("   ✅ Native OS integration", 
               "   - Native OS integration")
    print_safe("   ✅ All original functionality preserved", 
               "   - All original functionality preserved")
    print_safe("   ✅ Real-time AI detection capabilities", 
               "   - Real-time AI detection capabilities")
    print()
    
    print_safe("🔧 Initializing system...", "Initializing system...")
    
    # Create directory structure
    create_directory_structure()
    print("✅ Directory structure created")
    
    # Check PyQt5 installation
    if not check_pyqt5_installation():
        print("❌ PyQt5 is required for this application")
        print("📦 Please install PyQt5: pip install PyQt5>=5.15.0")
        input("Press Enter to exit...")
        return
    
    # Check other dependencies
    deps_ok = check_dependencies()
    if not deps_ok:
        print("⚠️ Some dependencies are missing, but the app will try to run...")
    
    print()
    print_safe("🚀 Starting PyQt5 application...", "Starting PyQt5 application...")
    print()
    
    try:
        # Import PyQt5 login window
        try:
            from gui_pyqt5.login_window import LoginWindow
            print("✅ PyQt5 login window imported successfully")
        except ImportError as e:
            print(f"❌ Error importing PyQt5 login window: {e}")
            print("🔄 Trying fallback options...")
            
            # Try original Tkinter as fallback
            try:
                from gui.login_window import LoginWindow
                print("✅ Fallback to Tkinter login window")
                print("⚠️ Running in Tkinter compatibility mode")
            except ImportError:
                print("❌ No login window available")
                create_emergency_interface()
                return
        
        # Initialize QApplication first
        from PyQt5.QtWidgets import QApplication
        import sys

        qt_app = QApplication(sys.argv)

        # Start the application
        app = LoginWindow()
        app.show()

        # Run the application
        exit_code = qt_app.exec_()
        
        print()
        print_safe("👋 Application session ended", "Application session ended")
        return exit_code
        
    except Exception as e:
        print(f"❌ Critical error: {e}")
        print("\nTroubleshooting:")
        print("1. Ensure PyQt5 is installed: pip install PyQt5>=5.15.0")
        print("2. Check camera permissions")
        print("3. Close other camera applications")
        print("4. Try running: python main.py")
        
        # Try emergency interface
        create_emergency_interface()

def create_emergency_interface():
    """Create emergency interface when PyQt5 fails"""
    try:
        print("🆘 Starting emergency interface...")
        
        # Try PyQt5 emergency dialog
        try:
            from PyQt5.QtWidgets import QApplication, QMessageBox
            import sys
            
            app = QApplication(sys.argv)
            
            msg = QMessageBox()
            msg.setWindowTitle("AI Video Detection - Emergency Mode")
            msg.setIcon(QMessageBox.Critical)
            msg.setText("⚠️ AI Video Detection Tool\nEmergency Mode")
            msg.setInformativeText(
                "The application encountered a critical error.\n\n"
                "Please check:\n"
                "1. PyQt5 installation: pip install PyQt5\n"
                "2. Python installation\n"
                "3. Required packages\n"
                "4. File permissions"
            )
            msg.setStandardButtons(QMessageBox.Ok)
            msg.exec_()
            
        except ImportError:
            # Fallback to Tkinter emergency interface
            try:
                import tkinter as tk
                from tkinter import messagebox
                
                root = tk.Tk()
                root.withdraw()  # Hide main window
                
                messagebox.showerror(
                    "AI Video Detection - Emergency",
                    "⚠️ Critical Error\n\n"
                    "PyQt5 is not available.\n\n"
                    "Please install PyQt5:\n"
                    "pip install PyQt5>=5.15.0\n\n"
                    "Or run the Tkinter version:\n"
                    "python main.py"
                )
                
                root.destroy()
                
            except ImportError:
                # Last resort - console only
                print("❌ Critical error: No GUI framework available")
                print("Please install PyQt5 or Tkinter")
                input("Press Enter to exit...")
                
    except Exception as e:
        print(f"❌ Emergency interface failed: {e}")
        print("Please check your Python installation")
        input("Press Enter to exit...")

if __name__ == "__main__":
    try:
        exit_code = main()
        sys.exit(exit_code or 0)
    except KeyboardInterrupt:
        print("\n🛑 Application interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ Unexpected error: {e}")
        sys.exit(1)
