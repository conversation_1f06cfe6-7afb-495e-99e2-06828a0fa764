#!/usr/bin/env python3
"""
Enhanced popup for comprehensive emotion analysis with your 8-class YOLOv8 model
Displays detailed probability breakdown, quality metrics, and detection history
"""

import tkinter as tk
from tkinter import ttk, messagebox
from datetime import datetime
import json
import csv
import os
from typing import Dict, List, Optional

class ExpressionDetectionPopup:
    """Enhanced popup for comprehensive emotion analysis"""

    def __init__(self, parent_window=None):
        self.parent_window = parent_window
        self.popup_window = None
        self.is_showing = False

        # Enhanced emotion data with colors and descriptions
        self.emotion_data = {
            'Anger': {'emoji': '😠', 'color': '#E74C3C', 'description': 'Anger or frustration detected'},
            'Contempt': {'emoji': '😤', 'color': '#8E44AD', 'description': 'Contempt or disdain detected'},
            'Disgust': {'emoji': '🤢', 'color': '#E67E22', 'description': 'Disgust or disapproval detected'},
            'Fear': {'emoji': '😨', 'color': '#9B59B6', 'description': 'Fear or anxiety detected'},
            'Happy': {'emoji': '😊', 'color': '#2ECC71', 'description': 'Joy and happiness detected'},
            'Neutral': {'emoji': '😐', 'color': '#34495E', 'description': 'Neutral expression detected'},
            'Sad': {'emoji': '😢', 'color': '#3498DB', 'description': 'Sadness or melancholy detected'},
            'Surprise': {'emoji': '😲', 'color': '#F1C40F', 'description': 'Surprise or amazement detected'},
            'No Detection': {'emoji': '❓', 'color': '#95A5A6', 'description': 'No face detected'}
        }

        # Detection history for display
        self.detection_history = []

    def show_expression_result(self, expression, confidence, model_used="Enhanced YOLOv8 (83.6%)",
                             face_image=None, detection_data=None):
        """Show comprehensive emotion analysis popup"""
        try:
            if self.is_showing:
                self.close_popup()

            self.is_showing = True

            # Create enhanced popup window
            self.popup_window = tk.Toplevel(self.parent_window if self.parent_window else tk.Tk())
            self.popup_window.title("🎭 Enhanced YOLOv8 Emotion Analysis")
            self.popup_window.geometry("800x900")
            self.popup_window.configure(bg='#f8f9fa')
            self.popup_window.resizable(True, True)

            # Make popup modal
            self.popup_window.transient(self.parent_window)
            self.popup_window.grab_set()

            # Center the popup
            self.center_popup()

            # Get emotion data
            expr_data = self.emotion_data.get(expression, self.emotion_data['No Detection'])

            # Create comprehensive UI
            self.create_enhanced_popup_ui(expression, confidence, model_used, expr_data,
                                        face_image, detection_data)

            # Auto-close after 15 seconds
            self.popup_window.after(15000, self.auto_close)

            # Bind escape key to close
            self.popup_window.bind('<Escape>', lambda e: self.close_popup())
            self.popup_window.focus_set()

        except Exception as e:
            print(f"❌ Error showing enhanced popup: {e}")
            self.is_showing = False

    def center_popup(self):
        """Center the popup window"""
        self.popup_window.update_idletasks()

        if self.parent_window:
            # Center relative to parent window
            parent_x = self.parent_window.winfo_x()
            parent_y = self.parent_window.winfo_y()
            parent_width = self.parent_window.winfo_width()
            parent_height = self.parent_window.winfo_height()

            x = parent_x + (parent_width - 800) // 2
            y = parent_y + (parent_height - 900) // 2
        else:
            # Center on screen
            screen_width = self.popup_window.winfo_screenwidth()
            screen_height = self.popup_window.winfo_screenheight()
            x = (screen_width - 800) // 2
            y = (screen_height - 900) // 2

        self.popup_window.geometry(f"800x900+{x}+{y}")

    def close_popup(self):
        """Close the popup window"""
        try:
            if self.popup_window:
                self.popup_window.grab_release()
                self.popup_window.destroy()
                self.popup_window = None
            self.is_showing = False
        except:
            pass

    def auto_close(self):
        """Auto-close popup after timeout"""
        if self.is_showing:
            self.close_popup()

    def create_enhanced_popup_ui(self, expression, confidence, model_used, expr_data,
                               face_image, detection_data):
        """Create comprehensive emotion analysis UI"""
        # Create scrollable frame
        canvas = tk.Canvas(self.popup_window, bg='#f8f9fa', highlightthickness=0)

        # Create enhanced scrollbar with blue theme
        scrollbar = tk.Scrollbar(self.popup_window, orient="vertical", command=canvas.yview,
                                bg='#2E86AB', troughcolor='#E8F4FD', activebackground='#1F5F85',
                                width=16, relief='flat', bd=0)

        scrollable_frame = tk.Frame(canvas, bg='#f8f9fa')

        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )

        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)

        # Header section
        self.create_header_section(scrollable_frame, expression, expr_data, model_used)

        # Main emotion display
        self.create_main_emotion_display(scrollable_frame, expression, confidence, expr_data)

        # Face image section (if available)
        if face_image is not None:
            self.create_face_image_section(scrollable_frame, face_image)

        # Probability breakdown section
        if detection_data and 'all_probabilities' in detection_data:
            self.create_probability_section(scrollable_frame, detection_data['all_probabilities'])

        # Top emotions section
        if detection_data and 'top_emotions' in detection_data:
            self.create_top_emotions_section(scrollable_frame, detection_data['top_emotions'])

        # Quality and performance metrics
        if detection_data:
            self.create_metrics_section(scrollable_frame, detection_data)

        # Detection history
        self.create_history_section(scrollable_frame)

        # Action buttons
        self.create_action_buttons(scrollable_frame, detection_data)

        # Pack canvas and scrollbar
        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")

        # Enhanced scrolling functionality
        def on_mousewheel(event):
            """Handle mouse wheel scrolling with improved responsiveness"""
            try:
                # Check if content is scrollable
                if canvas.winfo_reqheight() > canvas.winfo_height():
                    canvas.yview_scroll(int(-1*(event.delta/120)), "units")
            except:
                pass

        def on_key_scroll(event):
            """Handle keyboard scrolling with improved navigation"""
            try:
                if event.keysym == 'Up':
                    canvas.yview_scroll(-1, "units")
                elif event.keysym == 'Down':
                    canvas.yview_scroll(1, "units")
                elif event.keysym == 'Page_Up':
                    canvas.yview_scroll(-1, "pages")
                elif event.keysym == 'Page_Down':
                    canvas.yview_scroll(1, "pages")
                elif event.keysym == 'Home':
                    canvas.yview_moveto(0)
                elif event.keysym == 'End':
                    canvas.yview_moveto(1)
            except:
                pass

        def bind_mousewheel_to_widget(widget):
            """Recursively bind mouse wheel to widget and all its children"""
            try:
                widget.bind("<MouseWheel>", on_mousewheel)
                widget.bind("<Button-4>", lambda e: on_mousewheel(type('obj', (object,), {'delta': 120})()))  # Linux
                widget.bind("<Button-5>", lambda e: on_mousewheel(type('obj', (object,), {'delta': -120})()))  # Linux
                for child in widget.winfo_children():
                    bind_mousewheel_to_widget(child)
            except:
                pass

        def update_scroll_region():
            """Update scroll region after content changes"""
            try:
                canvas.update_idletasks()
                canvas.configure(scrollregion=canvas.bbox("all"))
            except:
                pass

        # Bind mouse wheel to canvas and all content
        canvas.bind("<MouseWheel>", on_mousewheel)
        canvas.bind("<Button-4>", lambda e: on_mousewheel(type('obj', (object,), {'delta': 120})()))  # Linux
        canvas.bind("<Button-5>", lambda e: on_mousewheel(type('obj', (object,), {'delta': -120})()))  # Linux

        # Bind to scrollable frame and all its children
        bind_mousewheel_to_widget(scrollable_frame)

        # Bind keyboard scrolling
        self.popup_window.bind("<Key>", on_key_scroll)
        self.popup_window.focus_set()

        # Make popup focusable for keyboard navigation
        self.popup_window.bind("<Button-1>", lambda e: self.popup_window.focus_set())

        # Update scroll region after a short delay to ensure all content is loaded
        self.popup_window.after(100, update_scroll_region)
        self.popup_window.after(500, update_scroll_region)  # Second update for dynamic content

    def create_header_section(self, parent, expression, expr_data, model_used):
        """Create header with model information"""
        header_frame = tk.Frame(parent, bg='#2563eb', height=80)
        header_frame.pack(fill='x', padx=10, pady=10)
        header_frame.pack_propagate(False)

        # Close button
        close_btn = tk.Button(header_frame, text="✕", font=('Arial', 16, 'bold'),
                             bg='#2563eb', fg='white', bd=0, relief='flat',
                             command=self.close_popup, cursor='hand2')
        close_btn.pack(side='right', padx=15, pady=15)

        # Header title
        title_label = tk.Label(header_frame, text="🎭 Enhanced YOLOv8 Emotion Analysis",
                              font=('Arial', 18, 'bold'), bg='#2563eb', fg='white')
        title_label.pack(side='left', padx=20, pady=15)

        # Model info
        model_label = tk.Label(header_frame, text=f"Model: {model_used}",
                              font=('Arial', 11), bg='#2563eb', fg='#e0e7ff')
        model_label.pack(side='left', padx=(0, 20), pady=15)

    def create_main_emotion_display(self, parent, expression, confidence, expr_data):
        """Create main emotion display with large emoji and details"""
        main_frame = tk.Frame(parent, bg='#ffffff', relief='solid', bd=2)
        main_frame.pack(fill='x', padx=10, pady=10)

        # Large emoji
        emoji_label = tk.Label(main_frame, text=expr_data['emoji'],
                              font=('Arial', 80), bg='#ffffff', pady=20)
        emoji_label.pack()

        # Emotion name with colored background
        emotion_frame = tk.Frame(main_frame, bg=expr_data['color'], relief='solid', bd=2)
        emotion_frame.pack(pady=10)

        emotion_label = tk.Label(emotion_frame, text=expression.upper(),
                                font=('Arial', 24, 'bold'), bg=expr_data['color'],
                                fg='white', padx=30, pady=10)
        emotion_label.pack()

        # Confidence with visual bar
        conf_frame = tk.Frame(main_frame, bg='#ffffff')
        conf_frame.pack(fill='x', padx=20, pady=10)

        conf_label = tk.Label(conf_frame, text=f"Confidence: {confidence*100:.1f}%",
                             font=('Arial', 16, 'bold'), bg='#ffffff')
        conf_label.pack()

        # Confidence bar
        bar_frame = tk.Frame(conf_frame, bg='#e9ecef', height=20, relief='solid', bd=1)
        bar_frame.pack(fill='x', pady=5)

        bar_width = int(confidence * 100)
        if bar_width > 0:
            conf_bar = tk.Frame(bar_frame, bg=expr_data['color'], height=18)
            conf_bar.place(x=1, y=1, width=bar_width*4, height=18)

        # Description
        desc_label = tk.Label(main_frame, text=expr_data['description'],
                             font=('Arial', 12), bg='#ffffff', fg='#6c757d', pady=10)
        desc_label.pack()

    def create_face_image_section(self, parent, face_image):
        """Create face image display section"""
        try:
            import cv2
            from PIL import Image, ImageTk

            face_frame = tk.Frame(parent, bg='#ffffff', relief='solid', bd=2)
            face_frame.pack(fill='x', padx=10, pady=10)

            title_label = tk.Label(face_frame, text="👤 Detected Face Region",
                                  font=('Arial', 14, 'bold'), bg='#ffffff', fg='#495057')
            title_label.pack(pady=(10, 5))

            # Resize and display face image
            face_resized = cv2.resize(face_image, (150, 150))
            face_rgb = cv2.cvtColor(face_resized, cv2.COLOR_BGR2RGB)
            face_pil = Image.fromarray(face_rgb)
            face_photo = ImageTk.PhotoImage(face_pil)

            face_label = tk.Label(face_frame, image=face_photo, bg='#ffffff')
            face_label.image = face_photo  # Keep reference
            face_label.pack(pady=10)

        except Exception as e:
            print(f"Error displaying face image: {e}")

    def create_probability_section(self, parent, probabilities):
        """Create probability breakdown section"""
        prob_frame = tk.Frame(parent, bg='#ffffff', relief='solid', bd=2)
        prob_frame.pack(fill='x', padx=10, pady=10)

        title_label = tk.Label(prob_frame, text="📊 Emotion Probability Distribution",
                              font=('Arial', 14, 'bold'), bg='#ffffff', fg='#495057')
        title_label.pack(pady=(10, 5))

        # Create probability bars for each emotion
        for emotion, prob in sorted(probabilities.items(), key=lambda x: x[1], reverse=True):
            if prob > 0.01:  # Only show emotions with significant probability
                emotion_data = self.emotion_data.get(emotion, {'color': '#95A5A6', 'emoji': '❓'})

                row_frame = tk.Frame(prob_frame, bg='#ffffff')
                row_frame.pack(fill='x', padx=20, pady=2)

                # Emotion label
                label_frame = tk.Frame(row_frame, bg='#ffffff', width=120)
                label_frame.pack(side='left')
                label_frame.pack_propagate(False)

                emotion_label = tk.Label(label_frame,
                                       text=f"{emotion_data['emoji']} {emotion}",
                                       font=('Arial', 10), bg='#ffffff', anchor='w')
                emotion_label.pack(side='left')

                # Probability bar
                bar_container = tk.Frame(row_frame, bg='#e9ecef', height=20, relief='solid', bd=1)
                bar_container.pack(side='left', fill='x', expand=True, padx=(10, 10))

                bar_width = int(prob * 200)  # Scale to 200px max
                if bar_width > 0:
                    prob_bar = tk.Frame(bar_container, bg=emotion_data['color'], height=18)
                    prob_bar.place(x=1, y=1, width=bar_width, height=18)

                # Percentage label
                percent_label = tk.Label(row_frame, text=f"{prob*100:.1f}%",
                                       font=('Arial', 10, 'bold'), bg='#ffffff', width=8)
                percent_label.pack(side='right')

        # Add spacing
        tk.Label(prob_frame, text="", bg='#ffffff', height=1).pack()

    def create_top_emotions_section(self, parent, top_emotions):
        """Create top emotions section"""
        if not top_emotions:
            return

        top_frame = tk.Frame(parent, bg='#ffffff', relief='solid', bd=2)
        top_frame.pack(fill='x', padx=10, pady=10)

        title_label = tk.Label(top_frame, text="🏆 Top 3 Detected Emotions",
                              font=('Arial', 14, 'bold'), bg='#ffffff', fg='#495057')
        title_label.pack(pady=(10, 5))

        for i, (emotion, prob) in enumerate(top_emotions[:3]):
            emotion_data = self.emotion_data.get(emotion, {'color': '#95A5A6', 'emoji': '❓'})

            rank_frame = tk.Frame(top_frame, bg=emotion_data['color'], relief='solid', bd=1)
            rank_frame.pack(fill='x', padx=20, pady=5)

            rank_label = tk.Label(rank_frame,
                                 text=f"#{i+1} {emotion_data['emoji']} {emotion} - {prob*100:.1f}%",
                                 font=('Arial', 12, 'bold'), bg=emotion_data['color'],
                                 fg='white', pady=8)
            rank_label.pack()

        # Add spacing
        tk.Label(top_frame, text="", bg='#ffffff', height=1).pack()

    def create_metrics_section(self, parent, detection_data):
        """Create quality and performance metrics section"""
        metrics_frame = tk.Frame(parent, bg='#ffffff', relief='solid', bd=2)
        metrics_frame.pack(fill='x', padx=10, pady=10)

        title_label = tk.Label(metrics_frame, text="📈 Detection Quality & Performance",
                              font=('Arial', 14, 'bold'), bg='#ffffff', fg='#495057')
        title_label.pack(pady=(10, 5))

        # Create metrics grid
        grid_frame = tk.Frame(metrics_frame, bg='#ffffff')
        grid_frame.pack(fill='x', padx=20, pady=10)

        metrics = [
            ("Face Quality", detection_data.get('face_quality_score', 0.0), "🎯"),
            ("Detection Stability", detection_data.get('detection_stability', 0.0), "⚖️"),
            ("Emotion Intensity", self._get_intensity_score(detection_data.get('emotion_intensity', 'low')), "🔥"),
            ("Inference Time", f"{detection_data.get('inference_time', 0.0):.3f}s", "⚡"),
            ("Post-processing", f"{detection_data.get('post_processing_time', 0.0):.3f}s", "🔧"),
            ("Faces Detected", detection_data.get('faces_detected', 0), "👥")
        ]

        for i, (label, value, emoji) in enumerate(metrics):
            row = i // 2
            col = i % 2

            metric_frame = tk.Frame(grid_frame, bg='#f8f9fa', relief='solid', bd=1)
            metric_frame.grid(row=row, column=col, padx=5, pady=5, sticky='ew')

            if isinstance(value, float) and 0 <= value <= 1:
                # Show as percentage with color coding
                percentage = value * 100
                color = '#2ECC71' if value > 0.7 else '#F39C12' if value > 0.4 else '#E74C3C'
                value_text = f"{percentage:.1f}%"
            else:
                color = '#34495E'
                value_text = str(value)

            tk.Label(metric_frame, text=f"{emoji} {label}",
                    font=('Arial', 10, 'bold'), bg='#f8f9fa').pack(pady=2)
            tk.Label(metric_frame, text=value_text,
                    font=('Arial', 12, 'bold'), bg='#f8f9fa', fg=color).pack(pady=2)

        # Configure grid weights
        grid_frame.grid_columnconfigure(0, weight=1)
        grid_frame.grid_columnconfigure(1, weight=1)

        # Add spacing
        tk.Label(metrics_frame, text="", bg='#ffffff', height=1).pack()

    def create_history_section(self, parent):
        """Create detection history section"""
        if not self.detection_history:
            return

        history_frame = tk.Frame(parent, bg='#ffffff', relief='solid', bd=2)
        history_frame.pack(fill='x', padx=10, pady=10)

        title_label = tk.Label(history_frame, text="📜 Recent Detection History",
                              font=('Arial', 14, 'bold'), bg='#ffffff', fg='#495057')
        title_label.pack(pady=(10, 5))

        # Show last 5 detections
        for i, detection in enumerate(self.detection_history[-5:]):
            emotion_data = self.emotion_data.get(detection.get('emotion', 'Unknown'),
                                               {'emoji': '❓', 'color': '#95A5A6'})

            hist_row = tk.Frame(history_frame, bg='#f8f9fa', relief='solid', bd=1)
            hist_row.pack(fill='x', padx=20, pady=2)

            time_str = detection.get('timestamp', datetime.now()).strftime('%H:%M:%S')
            confidence = detection.get('confidence', 0.0)
            stability = detection.get('stability', 0.0)

            history_text = f"{emotion_data['emoji']} {detection.get('emotion', 'Unknown')} | " \
                          f"Conf: {confidence:.3f} | Stab: {stability:.3f} | {time_str}"

            tk.Label(hist_row, text=history_text,
                    font=('Arial', 10), bg='#f8f9fa', anchor='w', padx=10, pady=5).pack(fill='x')

        # Add spacing
        tk.Label(history_frame, text="", bg='#ffffff', height=1).pack()

    def create_action_buttons(self, parent, detection_data):
        """Create action buttons for export and analysis"""
        button_frame = tk.Frame(parent, bg='#f8f9fa')
        button_frame.pack(fill='x', padx=10, pady=20)

        # Export button
        export_btn = tk.Button(button_frame, text="💾 Export Results",
                              font=('Arial', 11, 'bold'), bg='#10b981', fg='white',
                              relief='flat', bd=0, pady=10, padx=20, cursor='hand2',
                              command=lambda: self.export_results(detection_data))
        export_btn.pack(side='left', padx=(0, 10))

        # Analyze again button
        analyze_btn = tk.Button(button_frame, text="🔄 Analyze Again",
                               font=('Arial', 11, 'bold'), bg='#2563eb', fg='white',
                               relief='flat', bd=0, pady=10, padx=20, cursor='hand2',
                               command=self.analyze_again)
        analyze_btn.pack(side='left', padx=(0, 10))

        # Close button
        close_btn = tk.Button(button_frame, text="❌ Close",
                             font=('Arial', 11, 'bold'), bg='#6c757d', fg='white',
                             relief='flat', bd=0, pady=10, padx=20, cursor='hand2',
                             command=self.close_popup)
        close_btn.pack(side='right')

    def _get_intensity_score(self, intensity_level):
        """Convert intensity level to numeric score"""
        intensity_map = {'low': 0.3, 'medium': 0.6, 'high': 0.9}
        return intensity_map.get(intensity_level, 0.5)

    def export_results(self, detection_data):
        """Export detection results to file"""
        try:
            if not detection_data:
                return

            # Create export directory
            export_dir = "detection_results"
            os.makedirs(export_dir, exist_ok=True)

            # Generate filename with timestamp
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"enhanced_emotion_detection_{timestamp}.json"
            filepath = os.path.join(export_dir, filename)

            # Prepare export data
            export_data = {
                'timestamp': detection_data.get('timestamp', datetime.now()).isoformat(),
                'model_used': detection_data.get('model_used', 'Enhanced YOLOv8'),
                'primary_emotion': detection_data.get('expression', 'Unknown'),
                'confidence': detection_data.get('confidence', 0.0),
                'emotion_intensity': detection_data.get('emotion_intensity', 'unknown'),
                'face_quality_score': detection_data.get('face_quality_score', 0.0),
                'detection_stability': detection_data.get('detection_stability', 0.0),
                'inference_time': detection_data.get('inference_time', 0.0),
                'post_processing_time': detection_data.get('post_processing_time', 0.0),
                'faces_detected': detection_data.get('faces_detected', 0),
                'all_probabilities': detection_data.get('all_probabilities', {}),
                'top_emotions': detection_data.get('top_emotions', []),
                'all_faces': detection_data.get('all_faces', [])
            }

            # Save to JSON file
            with open(filepath, 'w') as f:
                json.dump(export_data, f, indent=4, default=str)

            print(f"✅ Results exported to: {filepath}")

            # Show confirmation
            self._show_export_confirmation(filename)

        except Exception as e:
            print(f"❌ Error exporting results: {e}")

    def _show_export_confirmation(self, filename):
        """Show export confirmation message"""
        # Update export button text temporarily
        for widget in self.popup_window.winfo_children():
            self._update_export_button_text(widget, "✅ Exported!")

    def _update_export_button_text(self, widget, new_text):
        """Recursively update export button text"""
        if isinstance(widget, tk.Button) and "Export" in widget.cget('text'):
            original_text = widget.cget('text')
            original_bg = widget.cget('bg')
            widget.config(text=new_text, bg='#059669')
            self.popup_window.after(2500, lambda: widget.config(text=original_text, bg=original_bg))
            return True

        for child in widget.winfo_children():
            if self._update_export_button_text(child, new_text):
                return True
        return False

    def analyze_again(self):
        """Trigger another analysis"""
        self.close_popup()
        if hasattr(self.parent_window, 'detect_expression'):
            self.parent_window.after(100, self.parent_window.detect_expression)