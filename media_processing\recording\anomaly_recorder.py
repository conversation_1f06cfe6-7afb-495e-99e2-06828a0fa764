"""
Automated Anomaly Recording System
Handles video recording when anomalies are detected
"""

import cv2
import numpy as np
import threading
import time
import os
from datetime import datetime
from collections import deque
from typing import Optional, Dict, List
import logging

from utils.anomaly_config import AnomalyConfig

class AnomalyRecorder:
    """Automated video recording system for anomaly events"""
    
    def __init__(self):
        self.config = AnomalyConfig()
        self.logger = logging.getLogger(__name__)
        
        # Recording state
        self.is_recording = False
        self.current_recording = None
        self.recording_thread = None
        self.recording_start_time = None
        
        # Frame buffer for pre-recording
        self.frame_buffer = deque(maxlen=self.config.pre_recording_buffer * self.config.recording_fps)
        self.buffer_lock = threading.Lock()
        
        # Recording queue for multiple anomalies
        self.recording_queue = deque()
        self.queue_lock = threading.Lock()
        
        # Performance tracking
        self.total_recordings = 0
        self.total_recording_time = 0
        
        # Create directories
        self.config.create_directories()
        
        print("📹 Anomaly Recording System initialized")
        print(f"🎬 Recording duration: {self.config.recording_duration} seconds")
        print(f"📊 Pre-recording buffer: {self.config.pre_recording_buffer} seconds")
    
    def add_frame_to_buffer(self, frame: np.ndarray):
        """Add frame to pre-recording buffer"""
        with self.buffer_lock:
            self.frame_buffer.append({
                'frame': frame.copy(),
                'timestamp': datetime.now()
            })
    
    def start_anomaly_recording(self, anomaly_info: Dict, trigger_frame: np.ndarray) -> bool:
        """Start recording when anomaly is detected"""
        try:
            # Check if we're already recording
            if self.is_recording:
                print("⚠️ Already recording, queuing anomaly...")
                with self.queue_lock:
                    self.recording_queue.append({
                        'anomaly_info': anomaly_info,
                        'trigger_frame': trigger_frame.copy(),
                        'timestamp': datetime.now()
                    })
                return False
            
            # Start new recording
            timestamp = datetime.now().strftime("%Y-%m-%d_%H-%M-%S")
            recording_path = self.config.get_recording_path(timestamp)
            
            print(f"🎬 Starting anomaly recording: {recording_path}")
            
            # Create recording info
            self.current_recording = {
                'path': recording_path,
                'timestamp': timestamp,
                'anomaly_info': anomaly_info,
                'start_time': datetime.now(),
                'writer': None,
                'frame_count': 0
            }
            
            # Start recording thread
            self.recording_thread = threading.Thread(
                target=self._recording_worker,
                args=(trigger_frame,),
                daemon=True
            )
            self.recording_thread.start()
            
            self.is_recording = True
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to start anomaly recording: {e}")
            print(f"❌ Recording failed: {e}")
            return False
    
    def _recording_worker(self, trigger_frame: np.ndarray):
        """Background worker for video recording"""
        try:
            # Initialize video writer
            height, width = trigger_frame.shape[:2]
            fourcc = cv2.VideoWriter_fourcc(*self.config.recording_format)
            
            writer = cv2.VideoWriter(
                self.current_recording['path'],
                fourcc,
                self.config.recording_fps,
                (width, height)
            )
            
            if not writer.isOpened():
                raise Exception("Failed to open video writer")
            
            self.current_recording['writer'] = writer
            
            print(f"📹 Recording started: {self.current_recording['path']}")
            
            # Write pre-recording buffer frames
            with self.buffer_lock:
                buffer_frames = list(self.frame_buffer)
            
            for frame_data in buffer_frames:
                writer.write(frame_data['frame'])
                self.current_recording['frame_count'] += 1
            
            print(f"📼 Wrote {len(buffer_frames)} pre-recording frames")
            
            # Record for specified duration
            recording_start = time.time()
            frames_written = len(buffer_frames)
            target_frames = self.config.recording_duration * self.config.recording_fps

            print(f"🎬 Recording for {self.config.recording_duration} seconds...")
            print(f"📊 Target frames: {target_frames}, Buffer frames: {frames_written}")

            # Wait for the recording duration while monitoring frame count
            while time.time() - recording_start < self.config.recording_duration:
                current_frames = self.current_recording['frame_count']
                elapsed_time = time.time() - recording_start

                if current_frames > 0:
                    current_fps = current_frames / elapsed_time
                    print(f"📹 Recording: {elapsed_time:.1f}s, Frames: {current_frames}, FPS: {current_fps:.1f}")

                time.sleep(1.0)  # Check every second

            print(f"⏰ Recording duration completed: {self.config.recording_duration} seconds")
            print(f"📊 Final frame count: {self.current_recording['frame_count']}")

            # Finalize recording
            self._finalize_recording()
            
        except Exception as e:
            self.logger.error(f"Error in recording worker: {e}")
            print(f"❌ Recording error: {e}")
            self._cleanup_failed_recording()
    
    def write_frame(self, frame: np.ndarray):
        """Write frame to current recording"""
        if self.is_recording and self.current_recording and self.current_recording['writer']:
            try:
                self.current_recording['writer'].write(frame)
                self.current_recording['frame_count'] += 1

                # Debug output every 30 frames (1 second at 30 FPS)
                if self.current_recording['frame_count'] % 30 == 0:
                    elapsed = (datetime.now() - self.current_recording['start_time']).total_seconds()
                    print(f"📹 Recording: {self.current_recording['frame_count']} frames, {elapsed:.1f}s elapsed")

            except Exception as e:
                self.logger.error(f"Error writing frame: {e}")
                print(f"❌ Frame write error: {e}")
        else:
            # Debug why frame wasn't written
            if not self.is_recording:
                print("⚠️ Frame not written: Not recording")
            elif not self.current_recording:
                print("⚠️ Frame not written: No current recording")
            elif not self.current_recording.get('writer'):
                print("⚠️ Frame not written: No video writer")
    
    def _finalize_recording(self):
        """Finalize and save the recording"""
        try:
            if self.current_recording and self.current_recording['writer']:
                # Release video writer
                self.current_recording['writer'].release()
                
                # Calculate recording duration
                end_time = datetime.now()
                duration = (end_time - self.current_recording['start_time']).total_seconds()
                
                print(f"✅ Recording completed: {self.current_recording['path']}")
                print(f"📊 Duration: {duration:.1f}s, Frames: {self.current_recording['frame_count']}")
                
                # Update statistics
                self.total_recordings += 1
                self.total_recording_time += duration
                
                # Save recording metadata
                self._save_recording_metadata(duration)
                
                # Cleanup old recordings
                self._cleanup_old_recordings()
                
            self.is_recording = False
            self.current_recording = None
            
            # Check if there are queued recordings
            self._process_recording_queue()
            
        except Exception as e:
            self.logger.error(f"Error finalizing recording: {e}")
            self._cleanup_failed_recording()
    
    def _save_recording_metadata(self, duration: float):
        """Save metadata for the recording"""
        try:
            metadata_path = self.current_recording['path'].replace('.mp4', '_metadata.json')
            
            metadata = {
                'recording_path': self.current_recording['path'],
                'timestamp': self.current_recording['timestamp'],
                'start_time': self.current_recording['start_time'].isoformat(),
                'duration_seconds': duration,
                'frame_count': self.current_recording['frame_count'],
                'anomaly_info': self.current_recording['anomaly_info'],
                'recording_settings': {
                    'fps': self.config.recording_fps,
                    'format': self.config.recording_format,
                    'pre_buffer_seconds': self.config.pre_recording_buffer
                }
            }
            
            import json
            with open(metadata_path, 'w') as f:
                json.dump(metadata, f, indent=2, default=str)
            
            print(f"💾 Metadata saved: {metadata_path}")
            
        except Exception as e:
            self.logger.error(f"Error saving metadata: {e}")
    
    def _cleanup_failed_recording(self):
        """Clean up after a failed recording"""
        try:
            if self.current_recording:
                if self.current_recording['writer']:
                    self.current_recording['writer'].release()
                
                # Remove incomplete file
                if os.path.exists(self.current_recording['path']):
                    os.remove(self.current_recording['path'])
                    print(f"🗑️ Removed incomplete recording: {self.current_recording['path']}")
            
            self.is_recording = False
            self.current_recording = None
            
        except Exception as e:
            self.logger.error(f"Error cleaning up failed recording: {e}")
    
    def _process_recording_queue(self):
        """Process queued recording requests"""
        with self.queue_lock:
            if self.recording_queue and not self.is_recording:
                queued_item = self.recording_queue.popleft()
                print(f"📋 Processing queued recording...")
                self.start_anomaly_recording(
                    queued_item['anomaly_info'],
                    queued_item['trigger_frame']
                )
    
    def _cleanup_old_recordings(self):
        """Remove old recordings based on configuration"""
        if not self.config.auto_cleanup_enabled:
            return
        
        try:
            cutoff_date = self.config.get_cleanup_date()
            recordings_dir = self.config.base_dir
            
            if not os.path.exists(recordings_dir):
                return
            
            removed_count = 0
            for filename in os.listdir(recordings_dir):
                file_path = os.path.join(recordings_dir, filename)
                
                if os.path.isfile(file_path):
                    file_time = datetime.fromtimestamp(os.path.getctime(file_path))
                    
                    if file_time < cutoff_date:
                        os.remove(file_path)
                        removed_count += 1
            
            if removed_count > 0:
                print(f"🗑️ Cleaned up {removed_count} old recording files")
                
        except Exception as e:
            self.logger.error(f"Error during cleanup: {e}")
    
    def stop_recording(self):
        """Manually stop current recording"""
        if self.is_recording:
            print("⏹️ Manually stopping recording...")
            self._finalize_recording()
    
    def get_status(self) -> Dict:
        """Get recorder status"""
        return {
            'is_recording': self.is_recording,
            'current_recording': self.current_recording['path'] if self.current_recording else None,
            'queue_size': len(self.recording_queue),
            'total_recordings': self.total_recordings,
            'total_recording_time': self.total_recording_time,
            'buffer_size': len(self.frame_buffer)
        }
    
    def get_recent_recordings(self, limit: int = 10) -> List[str]:
        """Get list of recent recording files"""
        try:
            recordings_dir = self.config.base_dir
            if not os.path.exists(recordings_dir):
                return []
            
            # Get all MP4 files
            recordings = []
            for filename in os.listdir(recordings_dir):
                if filename.endswith('.mp4'):
                    file_path = os.path.join(recordings_dir, filename)
                    recordings.append({
                        'filename': filename,
                        'path': file_path,
                        'created': datetime.fromtimestamp(os.path.getctime(file_path))
                    })
            
            # Sort by creation time (newest first)
            recordings.sort(key=lambda x: x['created'], reverse=True)
            
            return [r['filename'] for r in recordings[:limit]]
            
        except Exception as e:
            self.logger.error(f"Error getting recent recordings: {e}")
            return []
