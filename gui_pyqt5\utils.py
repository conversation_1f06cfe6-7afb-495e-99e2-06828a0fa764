"""
PyQt5 Utility Functions

This module contains utility functions for common PyQt5 operations,
helping to bridge the gap between Tkinter and PyQt5 patterns.
"""

import sys
from PyQt5.QtWidgets import QApplication, QWidget, QMessageBox, QFileDialog
from PyQt5.QtCore import Qt, QTimer
from PyQt5.QtGui import QPixmap, QIcon, QFont


def center_window(window, width=None, height=None):
    """
    Center a window on the screen.
    PyQt5 equivalent of Tkinter's center_window functionality.
    """
    if width and height:
        window.resize(width, height)
    
    # Get screen geometry
    screen = QApplication.desktop().screenGeometry()
    
    # Get window geometry
    window_geometry = window.frameGeometry()
    
    # Calculate center point
    center_point = screen.center()
    
    # Move window to center
    window_geometry.moveCenter(center_point)
    window.move(window_geometry.topLeft())


def show_message_box(title, message, icon_type='info', parent=None):
    """
    Show a message box with specified title, message and icon.
    PyQt5 equivalent of <PERSON><PERSON><PERSON>'s messagebox functions.
    
    Args:
        title (str): Window title
        message (str): Message text
        icon_type (str): 'info', 'warning', 'error', 'question'
        parent (QWidget): Parent widget
    
    Returns:
        int: Button clicked (for question type)
    """
    msg_box = QMessageBox(parent)
    msg_box.setWindowTitle(title)
    msg_box.setText(message)
    
    # Set icon based on type
    if icon_type == 'info':
        msg_box.setIcon(QMessageBox.Information)
    elif icon_type == 'warning':
        msg_box.setIcon(QMessageBox.Warning)
    elif icon_type == 'error':
        msg_box.setIcon(QMessageBox.Critical)
    elif icon_type == 'question':
        msg_box.setIcon(QMessageBox.Question)
        msg_box.setStandardButtons(QMessageBox.Yes | QMessageBox.No)
        return msg_box.exec_()
    
    msg_box.exec_()
    return QMessageBox.Ok


def show_info_message(title, message, parent=None):
    """Show an information message box"""
    return show_message_box(title, message, 'info', parent)


def show_warning_message(title, message, parent=None):
    """Show a warning message box"""
    return show_message_box(title, message, 'warning', parent)


def show_error_message(title, message, parent=None):
    """Show an error message box"""
    return show_message_box(title, message, 'error', parent)


def show_question_message(title, message, parent=None):
    """Show a question message box and return the result"""
    result = show_message_box(title, message, 'question', parent)
    return result == QMessageBox.Yes


def open_file_dialog(title="Open File", file_filter="All Files (*)", parent=None):
    """
    Open a file dialog and return the selected file path.
    PyQt5 equivalent of Tkinter's filedialog.askopenfilename.
    """
    file_path, _ = QFileDialog.getOpenFileName(parent, title, "", file_filter)
    return file_path


def save_file_dialog(title="Save File", file_filter="All Files (*)", parent=None):
    """
    Open a save file dialog and return the selected file path.
    PyQt5 equivalent of Tkinter's filedialog.asksaveasfilename.
    """
    file_path, _ = QFileDialog.getSaveFileName(parent, title, "", file_filter)
    return file_path


def open_directory_dialog(title="Select Directory", parent=None):
    """
    Open a directory dialog and return the selected directory path.
    PyQt5 equivalent of Tkinter's filedialog.askdirectory.
    """
    directory = QFileDialog.getExistingDirectory(parent, title)
    return directory


def set_window_icon(window, icon_path):
    """Set window icon from file path"""
    try:
        icon = QIcon(icon_path)
        window.setWindowIcon(icon)
    except Exception as e:
        print(f"Warning: Could not set window icon: {e}")


def apply_dark_theme(app):
    """Apply a dark theme to the application"""
    dark_stylesheet = """
    QWidget {
        background-color: #2C3E50;
        color: #FFFFFF;
    }
    QPushButton {
        background-color: #3498DB;
        border: none;
        padding: 8px 16px;
        border-radius: 4px;
        color: white;
    }
    QPushButton:hover {
        background-color: #5DADE2;
    }
    QPushButton:pressed {
        background-color: #2980B9;
    }
    QLineEdit {
        background-color: #34495E;
        border: 1px solid #5DADE2;
        padding: 8px;
        border-radius: 4px;
    }
    QLabel {
        color: #FFFFFF;
    }
    """
    app.setStyleSheet(dark_stylesheet)


def apply_light_theme(app):
    """Apply a light theme to the application (default)"""
    light_stylesheet = """
    QWidget {
        background-color: #E8F4FD;
        color: #2C3E50;
    }
    QPushButton {
        background-color: #3498DB;
        border: none;
        padding: 8px 16px;
        border-radius: 4px;
        color: white;
    }
    QPushButton:hover {
        background-color: #5DADE2;
    }
    QPushButton:pressed {
        background-color: #2980B9;
    }
    QLineEdit {
        background-color: #FFFFFF;
        border: 1px solid #BDC3C7;
        padding: 8px;
        border-radius: 4px;
    }
    """
    app.setStyleSheet(light_stylesheet)


def create_font(family='Arial', size=12, bold=False, italic=False):
    """Create a QFont with specified properties"""
    font = QFont(family, size)
    font.setBold(bold)
    font.setItalic(italic)
    return font


def bind_key_sequence(widget, key_sequence, callback):
    """
    Bind a key sequence to a callback function.
    PyQt5 equivalent of Tkinter's bind method for key events.
    """
    from PyQt5.QtWidgets import QShortcut
    from PyQt5.QtGui import QKeySequence
    
    shortcut = QShortcut(QKeySequence(key_sequence), widget)
    shortcut.activated.connect(callback)
    return shortcut


def schedule_callback(delay_ms, callback, repeat=False):
    """
    Schedule a callback function to be called after a delay.
    PyQt5 equivalent of Tkinter's after method.
    
    Args:
        delay_ms (int): Delay in milliseconds
        callback (callable): Function to call
        repeat (bool): Whether to repeat the callback
    
    Returns:
        QTimer: Timer object for control
    """
    timer = QTimer()
    timer.timeout.connect(callback)
    
    if repeat:
        timer.start(delay_ms)
    else:
        timer.setSingleShot(True)
        timer.start(delay_ms)
    
    return timer


def get_screen_size():
    """Get the screen size"""
    screen = QApplication.desktop().screenGeometry()
    return screen.width(), screen.height()


def convert_tkinter_color(tk_color):
    """
    Convert Tkinter color format to PyQt5 format.
    Handles hex colors and named colors.
    """
    if tk_color.startswith('#'):
        return tk_color
    
    # Common Tkinter color name mappings
    color_map = {
        'red': '#FF0000',
        'green': '#00FF00',
        'blue': '#0000FF',
        'white': '#FFFFFF',
        'black': '#000000',
        'gray': '#808080',
        'grey': '#808080',
        'yellow': '#FFFF00',
        'cyan': '#00FFFF',
        'magenta': '#FF00FF',
    }
    
    return color_map.get(tk_color.lower(), '#000000')


def ensure_application():
    """
    Ensure QApplication instance exists.
    Creates one if it doesn't exist.
    """
    app = QApplication.instance()
    if app is None:
        app = QApplication(sys.argv)
    return app


class TkinterToQt5Adapter:
    """
    Adapter class to help with common Tkinter to PyQt5 conversions.
    """
    
    @staticmethod
    def pack_to_layout(widget, layout, side='top', fill=None, expand=False, padx=0, pady=0):
        """
        Convert Tkinter pack parameters to PyQt5 layout additions.
        This is a helper for manual conversion.
        """
        # Add widget to layout
        layout.addWidget(widget)
        
        # Handle padding
        if padx or pady:
            layout.setContentsMargins(padx, pady, padx, pady)
        
        # Note: PyQt5 layouts handle fill and expand differently
        # This would need to be handled case by case
    
    @staticmethod
    def grid_to_layout(widget, layout, row=0, column=0, rowspan=1, columnspan=1, 
                      sticky=None, padx=0, pady=0):
        """
        Convert Tkinter grid parameters to PyQt5 grid layout additions.
        """
        layout.addWidget(widget, row, column, rowspan, columnspan)
        
        # Handle padding
        if padx or pady:
            layout.setSpacing(max(padx, pady))
