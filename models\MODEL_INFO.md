# Downloaded Models Information

## OpenCV DNN Face Detection Models
- opencv_face_detector.pbtxt: Configuration file for OpenCV DNN face detector
- opencv_face_detector_uint8.pb: Pre-trained model weights for face detection
- Purpose: High-accuracy face detection for better expression analysis input

## Additional Haar Cascade Models
- haarcascade_eye.xml: Eye detection for facial feature validation
- haarcascade_eye_tree_eyeglasses.xml: Eye detection including glasses
- haarcascade_smile.xml: Smile detection for expression validation

## Usage
These models enhance the face detection accuracy and provide better input
for the custom YOLOv8 expression detection model (emotion_detection_83.6_percent.pt).

## Integration
The models are automatically loaded by the enhanced face detection system
when available in the models/ directory.
