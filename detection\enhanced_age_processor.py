#!/usr/bin/env python3
"""
Enhanced Age Processing Module
Optimized real-time age detection with improved accuracy and performance
"""

import cv2
import numpy as np
import time
from datetime import datetime
from collections import deque
from typing import List, Dict, Tuple, Optional
import statistics

class EnhancedAgeProcessor:
    """Enhanced age processing with temporal smoothing and quality assessment"""
    
    def __init__(self, config):
        self.config = config
        
        # Temporal smoothing buffers
        self.age_history = deque(maxlen=config.temporal_smoothing['window_size'])
        self.confidence_history = deque(maxlen=config.temporal_smoothing['window_size'])
        
        # Performance tracking
        self.performance_metrics = {
            'total_predictions': 0,
            'avg_inference_time': 0.0,
            'avg_confidence': 0.0,
            'quality_scores': deque(maxlen=100),
            'stability_scores': deque(maxlen=100)
        }
        
        # Face tracking for consistency
        self.face_tracker = {}
        self.next_face_id = 0
        
    def preprocess_face_for_age(self, face_roi: np.ndarray) -> np.ndarray:
        """Enhanced preprocessing for age detection"""
        try:
            # Resize to model input size
            input_size = self.config.prediction['input_size']
            face_resized = cv2.resize(face_roi, input_size)
            
            # Apply histogram equalization for better contrast
            if len(face_resized.shape) == 3:
                # Convert to LAB color space
                lab = cv2.cvtColor(face_resized, cv2.COLOR_BGR2LAB)
                l, a, b = cv2.split(lab)
                
                # Apply CLAHE to L channel
                clahe = cv2.createCLAHE(clipLimit=2.0, tileGridSize=(8, 8))
                l = clahe.apply(l)
                
                # Merge channels and convert back
                enhanced = cv2.merge([l, a, b])
                face_resized = cv2.cvtColor(enhanced, cv2.COLOR_LAB2BGR)
            else:
                # Apply CLAHE to grayscale
                clahe = cv2.createCLAHE(clipLimit=2.0, tileGridSize=(8, 8))
                face_resized = clahe.apply(face_resized)
                face_resized = cv2.cvtColor(face_resized, cv2.COLOR_GRAY2BGR)
            
            # Apply Gaussian blur to reduce noise
            face_resized = cv2.GaussianBlur(face_resized, (3, 3), 0)
            
            return face_resized
            
        except Exception as e:
            print(f"Error in face preprocessing: {e}")
            # Fallback to simple resize
            return cv2.resize(face_roi, self.config.prediction['input_size'])
    
    def create_age_blob(self, face_roi: np.ndarray) -> np.ndarray:
        """Create optimized blob for Caffe age model"""
        try:
            # Preprocess face
            processed_face = self.preprocess_face_for_age(face_roi)
            
            # Create blob with optimized parameters
            blob = cv2.dnn.blobFromImage(
                processed_face,
                scalefactor=self.config.prediction['scale_factor'],
                size=self.config.prediction['input_size'],
                mean=self.config.prediction['mean_values'],
                swapRB=self.config.prediction['swap_rb'],
                crop=self.config.prediction['crop']
            )
            
            return blob
            
        except Exception as e:
            print(f"Error creating age blob: {e}")
            return None
    
    def predict_age_enhanced(self, age_net, face_roi: np.ndarray, bbox: Tuple[int, int, int, int]) -> Dict:
        """Enhanced age prediction with quality assessment and validation"""
        try:
            start_time = time.time()
            print(f"🔧 Enhanced age prediction starting for face {bbox}")

            # Validate face quality
            print(f"🔍 Validating face quality for region {face_roi.shape}")
            is_valid, quality_score, quality_metrics = self.config.validate_face_quality(face_roi, bbox)

            print(f"📊 Quality assessment: valid={is_valid}, score={quality_score:.3f}")
            if quality_metrics:
                print(f"   Quality metrics: {quality_metrics}")

            if not is_valid:
                print(f"❌ Face quality validation failed: {quality_metrics.get('reason', 'poor_quality')}")
                return {
                    'age_range': None,
                    'confidence': 0.0,
                    'category': 'Invalid',
                    'emoji': '❌',
                    'quality_score': quality_score,
                    'quality_metrics': quality_metrics,
                    'inference_time': 0.0,
                    'valid': False,
                    'reason': quality_metrics.get('reason', 'poor_quality')
                }
            
            # Create optimized blob
            print(f"🔧 Creating blob for face region...")
            blob = self.create_age_blob(face_roi)
            if blob is None:
                print(f"❌ Blob creation failed")
                return self._create_invalid_result("blob_creation_failed", quality_score)

            print(f"✅ Blob created successfully: {blob.shape}")

            # Run inference
            print(f"🧠 Running Caffe model inference...")
            age_net.setInput(blob)
            predictions = age_net.forward()

            print(f"✅ Inference completed: {predictions.shape}")

            # Process predictions
            age_class_id = np.argmax(predictions[0])
            raw_confidence = float(predictions[0][age_class_id])

            print(f"📊 Raw prediction: class_id={age_class_id}, raw_confidence={raw_confidence:.3f}")

            # Apply softmax for better confidence interpretation
            softmax_probs = self._apply_softmax(predictions[0])
            confidence = float(softmax_probs[age_class_id])

            print(f"📊 Softmax confidence: {confidence:.3f}")
            print(f"📊 Confidence threshold: {self.config.prediction['confidence_threshold']}")

            # Validate prediction
            if confidence < self.config.prediction['confidence_threshold']:
                print(f"❌ Confidence too low: {confidence:.3f} < {self.config.prediction['confidence_threshold']}")
                return self._create_invalid_result("low_confidence", quality_score, confidence)
            
            # Map to age range
            if 0 <= age_class_id < len(self.config.age_ranges):
                age_range = self.config.age_ranges[age_class_id]
                age_info = self.config.get_age_info(age_range)
                
                # Calculate inference time
                inference_time = time.time() - start_time
                
                # Apply temporal smoothing if enabled
                if self.config.temporal_smoothing['enabled']:
                    smoothed_confidence, stability_score = self._apply_temporal_smoothing(
                        age_range, confidence
                    )
                else:
                    smoothed_confidence = confidence
                    stability_score = 1.0
                
                # Create comprehensive result
                result = {
                    'age_range': age_range,
                    'confidence': smoothed_confidence,
                    'raw_confidence': raw_confidence,
                    'category': age_info['category'],
                    'emoji': age_info['emoji'],
                    'avg_age': age_info['avg_age'],
                    'color': age_info['color'],
                    'quality_score': quality_score,
                    'quality_metrics': quality_metrics,
                    'stability_score': stability_score,
                    'inference_time': inference_time,
                    'valid': True,
                    'class_id': age_class_id,
                    'all_probabilities': softmax_probs.tolist(),
                    'timestamp': datetime.now()
                }
                
                # Update performance metrics
                self._update_performance_metrics(inference_time, smoothed_confidence, quality_score, stability_score)
                
                return result
            else:
                return self._create_invalid_result("invalid_class_id", quality_score)
                
        except Exception as e:
            print(f"Error in enhanced age prediction: {e}")
            return self._create_invalid_result("prediction_error", 0.0)
    
    def _apply_softmax(self, logits: np.ndarray) -> np.ndarray:
        """Apply softmax to get proper probabilities"""
        exp_logits = np.exp(logits - np.max(logits))  # Subtract max for numerical stability
        return exp_logits / np.sum(exp_logits)
    
    def _apply_temporal_smoothing(self, age_range: str, confidence: float) -> Tuple[float, float]:
        """Apply temporal smoothing to reduce jitter"""
        try:
            # Add current prediction to history
            self.age_history.append(age_range)
            self.confidence_history.append(confidence)
            
            if len(self.age_history) < 2:
                return confidence, 1.0
            
            # Calculate age consistency
            recent_ages = list(self.age_history)
            age_consistency = recent_ages.count(age_range) / len(recent_ages)
            
            # Calculate confidence stability
            recent_confidences = list(self.confidence_history)
            if len(recent_confidences) > 1:
                confidence_std = statistics.stdev(recent_confidences)
                confidence_stability = max(0.0, 1.0 - confidence_std)
            else:
                confidence_stability = 1.0
            
            # Combined stability score
            stability_score = (age_consistency * 0.7 + confidence_stability * 0.3)
            
            # Apply smoothing to confidence
            confidence_weight = self.config.temporal_smoothing['confidence_weight']
            if stability_score > self.config.temporal_smoothing['stability_threshold']:
                # High stability - use weighted average
                weights = [0.1, 0.2, 0.3, 0.4] if len(recent_confidences) >= 4 else [0.4, 0.6]
                weights = weights[-len(recent_confidences):]
                smoothed_confidence = sum(c * w for c, w in zip(recent_confidences, weights)) / sum(weights)
            else:
                # Low stability - use current confidence with slight smoothing
                smoothed_confidence = confidence * confidence_weight + (1 - confidence_weight) * np.mean(recent_confidences)
            
            return smoothed_confidence, stability_score
            
        except Exception as e:
            print(f"Error in temporal smoothing: {e}")
            return confidence, 0.5
    
    def _create_invalid_result(self, reason: str, quality_score: float, confidence: float = 0.0) -> Dict:
        """Create invalid result with reason"""
        return {
            'age_range': None,
            'confidence': confidence,
            'category': 'Invalid',
            'emoji': '❌',
            'quality_score': quality_score,
            'quality_metrics': {'reason': reason},
            'stability_score': 0.0,
            'inference_time': 0.0,
            'valid': False,
            'reason': reason,
            'timestamp': datetime.now()
        }
    
    def _update_performance_metrics(self, inference_time: float, confidence: float, 
                                  quality_score: float, stability_score: float):
        """Update performance tracking metrics"""
        try:
            self.performance_metrics['total_predictions'] += 1
            
            # Update average inference time
            total_preds = self.performance_metrics['total_predictions']
            current_avg = self.performance_metrics['avg_inference_time']
            self.performance_metrics['avg_inference_time'] = (
                (current_avg * (total_preds - 1) + inference_time) / total_preds
            )
            
            # Update average confidence
            current_conf_avg = self.performance_metrics['avg_confidence']
            self.performance_metrics['avg_confidence'] = (
                (current_conf_avg * (total_preds - 1) + confidence) / total_preds
            )
            
            # Update quality and stability scores
            self.performance_metrics['quality_scores'].append(quality_score)
            self.performance_metrics['stability_scores'].append(stability_score)
            
        except Exception as e:
            print(f"Error updating performance metrics: {e}")
    
    def get_performance_summary(self) -> Dict:
        """Get comprehensive performance summary"""
        try:
            quality_scores = list(self.performance_metrics['quality_scores'])
            stability_scores = list(self.performance_metrics['stability_scores'])
            
            return {
                'total_predictions': self.performance_metrics['total_predictions'],
                'avg_inference_time': self.performance_metrics['avg_inference_time'],
                'avg_confidence': self.performance_metrics['avg_confidence'],
                'avg_quality_score': statistics.mean(quality_scores) if quality_scores else 0.0,
                'avg_stability_score': statistics.mean(stability_scores) if stability_scores else 0.0,
                'current_fps': 1.0 / self.performance_metrics['avg_inference_time'] if self.performance_metrics['avg_inference_time'] > 0 else 0.0,
                'quality_trend': 'improving' if len(quality_scores) >= 5 and quality_scores[-1] > quality_scores[-5] else 'stable',
                'stability_trend': 'improving' if len(stability_scores) >= 5 and stability_scores[-1] > stability_scores[-5] else 'stable'
            }
            
        except Exception as e:
            print(f"Error getting performance summary: {e}")
            return {}
    
    def reset_history(self):
        """Reset temporal smoothing history"""
        self.age_history.clear()
        self.confidence_history.clear()
        print("🔄 Age detection history reset")
