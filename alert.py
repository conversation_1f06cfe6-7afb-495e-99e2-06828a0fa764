import tkinter.messagebox as messagebox
import pygame
import os

class AlertSystem:
    def __init__(self, dashboard):
        self.dashboard = dashboard
        self.alert_sound = "assets/alert.wav"  # Add alert sound file
        pygame.mixer.init()
    
    def trigger_alert(self):
        # Show visual alert
        self.dashboard.update_status("ALERT: Anomaly detected!")
        
        # Play sound alert
        try:
            if os.path.exists(self.alert_sound):
                pygame.mixer.music.load(self.alert_sound)
                pygame.mixer.music.play()
        except:
            pass
        
        # Show popup
        messagebox.showwarning("Security Alert", "An anomaly has been detected!")