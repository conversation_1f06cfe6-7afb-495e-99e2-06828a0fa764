"""
Alternative download for OpenCV DNN face detection model
Try multiple sources for the opencv_face_detector_uint8.pb file
"""

import os
import urllib.request
import ssl

def create_ssl_context():
    """Create SSL context that allows downloads"""
    context = ssl.create_default_context()
    context.check_hostname = False
    context.verify_mode = ssl.CERT_NONE
    return context

def download_file(url, destination, description="file"):
    """Download a file with progress indication"""
    try:
        print(f"📥 Downloading {description}...")
        print(f"   URL: {url}")
        print(f"   Destination: {destination}")
        
        # Create SSL context for HTTPS downloads
        context = create_ssl_context()
        
        # Create directory if it doesn't exist
        os.makedirs(os.path.dirname(destination), exist_ok=True)
        
        # Download with progress
        def progress_hook(block_num, block_size, total_size):
            if total_size > 0:
                percent = min(100, (block_num * block_size * 100) // total_size)
                print(f"\r   Progress: {percent}% ({block_num * block_size}/{total_size} bytes)", end="")
        
        urllib.request.urlretrieve(url, destination, progress_hook)
        print(f"\n✅ Successfully downloaded {description}")
        return True
        
    except Exception as e:
        print(f"\n❌ Failed to download {description}: {e}")
        return False

def verify_file_size(filepath, min_size_kb=1):
    """Verify that downloaded file has reasonable size"""
    if os.path.exists(filepath):
        size_kb = os.path.getsize(filepath) / 1024
        if size_kb >= min_size_kb:
            print(f"✅ File size verification passed: {size_kb:.1f} KB")
            return True
        else:
            print(f"❌ File too small: {size_kb:.1f} KB (minimum: {min_size_kb} KB)")
            return False
    return False

def download_opencv_dnn_model_alternative():
    """Try alternative sources for OpenCV DNN model"""
    print("🔍 TRYING ALTERNATIVE SOURCES FOR OPENCV DNN MODEL")
    print("=" * 60)
    
    models_dir = "models"
    filename = "opencv_face_detector_uint8.pb"
    filepath = os.path.join(models_dir, filename)
    
    # Check if already exists
    if os.path.exists(filepath) and verify_file_size(filepath, 2000):
        print(f"✅ {filename} already exists and verified")
        return True
    
    # Alternative URLs to try
    alternative_urls = [
        "https://github.com/opencv/opencv_3rdparty/raw/dnn_samples_face_detector_20170830/opencv_face_detector_uint8.pb",
        "https://raw.githubusercontent.com/spmallick/learnopencv/master/FaceDetectionComparison/models/opencv_face_detector_uint8.pb",
        "https://github.com/spmallick/learnopencv/raw/master/FaceDetectionComparison/models/opencv_face_detector_uint8.pb"
    ]
    
    for i, url in enumerate(alternative_urls, 1):
        print(f"\n🔄 Trying alternative source {i}/{len(alternative_urls)}...")
        
        if download_file(url, filepath, f"OpenCV DNN Model (Source {i})"):
            if verify_file_size(filepath, 2000):
                print(f"✅ Successfully downloaded from alternative source {i}")
                return True
            else:
                print(f"⚠️ Downloaded file failed verification, trying next source...")
                try:
                    os.remove(filepath)
                except:
                    pass
    
    print("❌ All alternative sources failed")
    return False

def create_manual_download_instructions():
    """Create instructions for manual download"""
    instructions = """# Manual Download Instructions for OpenCV DNN Model

If the automated download failed, you can download the model manually:

## Option 1: Direct Download
1. Go to: https://github.com/opencv/opencv_3rdparty/tree/dnn_samples_face_detector_20170830
2. Click on "opencv_face_detector_uint8.pb"
3. Click "Download" or "Raw" button
4. Save the file to: models/opencv_face_detector_uint8.pb
5. Verify file size is approximately 2.6 MB

## Option 2: Git Clone
```bash
git clone https://github.com/opencv/opencv_3rdparty.git temp_opencv
cp temp_opencv/opencv_face_detector_uint8.pb models/
rm -rf temp_opencv
```

## Option 3: Alternative Repository
1. Go to: https://github.com/spmallick/learnopencv/tree/master/FaceDetectionComparison/models
2. Download "opencv_face_detector_uint8.pb"
3. Save to: models/opencv_face_detector_uint8.pb

## Verification
The file should be approximately 2.6 MB in size.
You can verify with: `ls -la models/opencv_face_detector_uint8.pb`
"""
    
    with open("MANUAL_DOWNLOAD_OPENCV_DNN.md", "w") as f:
        f.write(instructions)
    print("📝 Created MANUAL_DOWNLOAD_OPENCV_DNN.md with manual instructions")

def main():
    """Main function"""
    print("🚀 ALTERNATIVE OPENCV DNN MODEL DOWNLOADER")
    print("=" * 60)
    
    success = download_opencv_dnn_model_alternative()
    
    if not success:
        print("\n⚠️ Automated download failed. Creating manual instructions...")
        create_manual_download_instructions()
        
        print("\n📋 MANUAL DOWNLOAD REQUIRED")
        print("=" * 40)
        print("The OpenCV DNN model could not be downloaded automatically.")
        print("Please follow the instructions in MANUAL_DOWNLOAD_OPENCV_DNN.md")
        print("\nAlternatively, the system will work with the enhanced Haar cascades")
        print("that were successfully downloaded, providing improved accuracy.")
    else:
        print("\n🎉 SUCCESS!")
        print("OpenCV DNN model downloaded successfully!")
        print("Your face detection accuracy should be significantly improved!")
    
    print(f"\n🎯 CURRENT STATUS:")
    models_dir = "models"
    required_files = [
        ("opencv_face_detector.pbtxt", "OpenCV DNN Config"),
        ("opencv_face_detector_uint8.pb", "OpenCV DNN Model"),
        ("haarcascade_eye.xml", "Eye Detection"),
        ("haarcascade_eye_tree_eyeglasses.xml", "Eye with Glasses"),
        ("haarcascade_smile.xml", "Smile Detection")
    ]
    
    for filename, description in required_files:
        filepath = os.path.join(models_dir, filename)
        if os.path.exists(filepath):
            size_kb = os.path.getsize(filepath) / 1024
            print(f"✅ {description}: {size_kb:.1f} KB")
        else:
            print(f"❌ {description}: Missing")

if __name__ == "__main__":
    main()
