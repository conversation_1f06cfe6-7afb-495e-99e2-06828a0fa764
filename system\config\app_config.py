import os
from dataclasses import dataclass
from typing import List, Tu<PERSON>

@dataclass
class Config:
    """Application configuration settings"""
    
    # Directories
    BASE_DIR: str = "Security_Footage"
    MODELS_DIR: str = "models"
    LOGS_DIR: str = "logs"
    
    # Recording settings
    MAX_FOLDERS: int = 10
    RECORDING_DURATION: int = 10  # seconds
    FPS: int = 30
    
    # Detection settings
    CONFIDENCE_THRESHOLD: float = 0.5
    NMS_THRESHOLD: float = 0.4
    YOLO_INPUT_SIZE: Tuple[int, int] = (416, 416)
    
    # Facial expression settings
    FACE_SIZE: Tuple[int, int] = (48, 48)
    EXPRESSION_LABELS: dict = None
    
    # Anomaly objects to detect
    ANOMALIES: List[str] = None
    
    # Authentication
    DEFAULT_USERNAME: str = "admin"
    DEFAULT_PASSWORD: str = "password123"
    
    # GUI settings
    WINDOW_SIZE: Tuple[int, int] = (1200, 800)
    VIDEO_DISPLAY_SIZE: Tuple[int, int] = (640, 480)
    
    # Colors (BGR format for OpenCV)
    COLOR_GREEN: Tuple[int, int, int] = (0, 255, 0)
    COLOR_RED: Tuple[int, int, int] = (0, 0, 255)
    COLOR_YELLOW: Tuple[int, int, int] = (0, 255, 255)
    COLOR_BLUE: Tuple[int, int, int] = (255, 0, 0)
    
    def __post_init__(self):
        """Initialize configuration after object creation"""
        self.EXPRESSION_LABELS = {
            0: "Angry",
            1: "Disgust", 
            2: "Fear",
            3: "Happy",
            4: "Sad",
            5: "Surprise",
            6: "Neutral"
        }
        
        self.ANOMALIES = ["gun", "knife", "scissors", "weapon"]
        
        # Create directories if they don't exist
        for directory in [self.BASE_DIR, self.MODELS_DIR, self.LOGS_DIR]:
            os.makedirs(directory, exist_ok=True)
    
    def get_model_path(self, model_name: str) -> str:
        """Get full path to model file"""
        return os.path.join(self.MODELS_DIR, model_name)
    
    def get_log_path(self, log_name: str) -> str:
        """Get full path to log file"""
        return os.path.join(self.LOGS_DIR, log_name)
