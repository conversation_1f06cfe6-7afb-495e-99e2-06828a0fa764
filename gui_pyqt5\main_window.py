"""
PyQt5 Main Window

This module contains the PyQt5 implementation of the main application window,
migrated from the original Tkinter version while preserving all functionality.
"""

import sys
import logging
import threading
import time
from datetime import datetime
from PyQt5.QtWidgets import (
    QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
    QLabel, QPushButton, QFrame, QScrollArea, QSplitter, QStatusBar,
    QMessageBox, QSizePolicy
)
from PyQt5.QtCore import Qt, QTimer, pyqtSignal, QThread, Q_ARG
from PyQt5.QtGui import QFont, QPixmap, QIcon

# Import utilities and base components
from .utils import (
    center_window, show_question_message, show_info_message, show_error_message,
    ensure_application, create_font, schedule_callback
)
from .base_components import StyledFrame, StyledLabel, Colors, RoundedButton

# Import configuration and dependencies
try:
    from utils.config import Config
    from recording.video_recorder import VideoRecorder
except ImportError:
    # Fallback configuration
    class Config:
        DEFAULT_USERNAME = "admin"
        DEFAULT_PASSWORD = "password123"
        WINDOW_SIZE = (1400, 900)
    
    class VideoRecorder:
        def __init__(self):
            pass


class EnhancedMainWindow(QMainWindow):
    """
    PyQt5 Enhanced main window with comprehensive real-time AI detection.
    Migrated from Tkinter while preserving all functionality.
    """
    
    # Signals for communication
    frame_processed = pyqtSignal(object)
    detection_completed = pyqtSignal(dict)
    
    def __init__(self):
        super().__init__()
        
        # Initialize configuration and logging
        self.config = Config()
        self.logger = logging.getLogger(__name__)
        
        # Initialize variables
        self.video_cap = None
        self.video_thread = None
        self.is_recording = False
        self.current_frame = None
        self.is_running = True
        
        # Initialize video recorder
        try:
            self.video_recorder = VideoRecorder()
        except:
            self.video_recorder = None
            print("⚠️ Video recorder not available")
        
        # Initialize AI detectors
        self.face_detector = None
        self.object_detector = None
        self.age_detector = None
        self.anomaly_system = None
        
        # Real-time detection settings
        self.real_time_age = False
        self.real_time_objects = False
        self.real_time_anomaly = False
        self.detection_interval = 15  # Process every 15 frames for better performance
        self.frame_counter = 0
        
        # Detection results storage
        self.last_age_result = None
        self.last_object_results = []
        self.last_anomaly_detected = False
        self.last_anomaly_info = None
        self.detection_history = []
        
        # Statistics
        self.stats = {
            'frames_processed': 0,
            'faces_detected': 0,
            'objects_detected': 0,
            'anomalies_detected': 0,
            'age_detections': 0,
            'recording_time': 0
        }
        
        # UI components references
        self.stats_labels = {}
        self.header_status_labels = {}
        self.video_indicators = {}
        
        # Timers
        self.time_update_timer = QTimer()
        self.time_update_timer.timeout.connect(self.update_time)
        self.time_update_timer.start(1000)  # Update every second
        
        # Setup window
        self.setup_window()
        
        # Initialize AI detectors
        self.initialize_ai_detectors()
        
        # Create interface
        self.create_interface()
        
        print("🎬 PyQt5 Main window initialized - ready to start camera")
    
    def setup_window(self):
        """Setup main window properties"""
        self.setWindowTitle("🛡️ AI Video Detection")
        self.setGeometry(100, 100, 1400, 900)
        
        # Set window background color
        self.setStyleSheet(f"QMainWindow {{ background-color: {Colors.BACKGROUND}; }}")
        
        # Center window on screen
        center_window(self, 1400, 900)
        
        # Handle window close
        self.closeEvent = self.on_closing
        
        # Window state tracking
        self.is_maximized = False
    
    def initialize_ai_detectors(self):
        """Initialize AI detection modules"""
        print("🤖 Initializing AI detection modules...")
        
        # Initialize facial expression detector
        try:
            from detection.custom_yolo_expression import CustomYOLOv8ExpressionDetector
            self.face_detector = CustomYOLOv8ExpressionDetector()
            if hasattr(self.face_detector, 'set_main_window'):
                self.face_detector.set_main_window(self)
            print("✅ Custom YOLOv8 expression detector loaded")
        except Exception as e:
            print(f"⚠️ Could not load expression detector: {e}")
            self.face_detector = None
        
        # Initialize age detection
        try:
            from detection.age_detection import AgeDetector
            self.age_detector = AgeDetector()
            print("✅ Age detector loaded")
        except Exception as e:
            print(f"⚠️ Could not load age detector: {e}")
            self.age_detector = None
        
        # Initialize object detection
        try:
            from detection.object_detection import ObjectDetector
            self.object_detector = ObjectDetector()
            print("✅ Object detector loaded")
        except Exception as e:
            print(f"⚠️ Could not load object detector: {e}")
            self.object_detector = None
        
        # Initialize anomaly detection
        try:
            from detection.anomaly_system import AnomalyDetectionSystem
            self.anomaly_system = AnomalyDetectionSystem()
            print("✅ Anomaly detection system loaded")
        except Exception as e:
            print(f"⚠️ Could not load anomaly system: {e}")
            self.anomaly_system = None
        
        # Initialize database integration
        try:
            from utils.database_integration import get_database
            self.database = get_database()
            print("✅ Database integration initialized")
        except Exception as e:
            print(f"⚠️ Could not initialize database: {e}")
            self.database = None
    
    def create_interface(self):
        """Create the main interface"""
        print("🎨 Creating PyQt5 enhanced interface...")
        
        # Create central widget
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # Create main layout
        main_layout = QVBoxLayout(central_widget)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(0)
        
        # Create sections
        self.create_header(main_layout)
        self.create_main_content(main_layout)
        self.create_status_bar()
        
        print("✅ PyQt5 enhanced interface created successfully!")
    
    def create_header(self, parent_layout):
        """Create header section with window controls"""
        # Header frame
        header_frame = StyledFrame(bg_color=Colors.DARK)
        header_frame.setFixedHeight(60)
        parent_layout.addWidget(header_frame)
        
        # Header layout
        header_layout = QHBoxLayout(header_frame)
        header_layout.setContentsMargins(20, 10, 20, 10)
        
        # Left side - Title section
        title_section = QWidget()
        title_layout = QHBoxLayout(title_section)
        title_layout.setContentsMargins(0, 0, 0, 0)
        
        # Logo
        logo_label = QLabel("🛡️")
        logo_label.setFont(create_font('Arial', 24))
        logo_label.setStyleSheet(f"color: {Colors.INFO}; background: transparent;")
        title_layout.addWidget(logo_label)
        
        # Title
        title_label = QLabel("AI Video Detection")
        title_label.setFont(create_font('Arial', 16, bold=True))
        title_label.setStyleSheet(f"color: white; background: transparent;")
        title_layout.addWidget(title_label)
        
        header_layout.addWidget(title_section)
        
        # Center - AI Detection status indicators
        center_section = QWidget()
        center_layout = QHBoxLayout(center_section)
        center_layout.setContentsMargins(0, 0, 0, 0)
        
        # Age detection status
        self.age_header_status = QLabel("👶 Age: OFF")
        self.age_header_status.setFont(create_font('Arial', 10, bold=True))
        self.age_header_status.setStyleSheet(f"color: {Colors.SECONDARY}; background: transparent;")
        center_layout.addWidget(self.age_header_status)
        
        # Object detection status
        self.object_header_status = QLabel("🔍 Objects: OFF")
        self.object_header_status.setFont(create_font('Arial', 10, bold=True))
        self.object_header_status.setStyleSheet(f"color: {Colors.SECONDARY}; background: transparent;")
        center_layout.addWidget(self.object_header_status)
        
        # Anomaly detection status
        self.anomaly_header_status = QLabel("🚨 Anomaly: OFF")
        self.anomaly_header_status.setFont(create_font('Arial', 10, bold=True))
        self.anomaly_header_status.setStyleSheet(f"color: {Colors.SECONDARY}; background: transparent;")
        center_layout.addWidget(self.anomaly_header_status)
        
        header_layout.addWidget(center_section)
        
        # Right side - System status and time
        right_section = QWidget()
        right_layout = QHBoxLayout(right_section)
        right_layout.setContentsMargins(0, 0, 0, 0)
        
        # System status
        self.header_status = QLabel("🟢 System Online")
        self.header_status.setFont(create_font('Arial', 10, bold=True))
        self.header_status.setStyleSheet(f"color: {Colors.SUCCESS}; background: transparent;")
        right_layout.addWidget(self.header_status)
        
        # Time display
        self.time_label = QLabel("")
        self.time_label.setFont(create_font('Arial', 12))
        self.time_label.setStyleSheet(f"color: {Colors.INFO}; background: transparent;")
        right_layout.addWidget(self.time_label)
        
        header_layout.addWidget(right_section)
        
        # Store references for updates
        self.header_status_labels = {
            'age': self.age_header_status,
            'object': self.object_header_status,
            'anomaly': self.anomaly_header_status,
            'system': self.header_status
        }
    
    def create_main_content(self, parent_layout):
        """Create main content area"""
        # Main content frame
        main_frame = StyledFrame(bg_color=Colors.BACKGROUND)
        parent_layout.addWidget(main_frame, 1)  # Expand to fill space
        
        # Main content layout
        main_layout = QHBoxLayout(main_frame)
        main_layout.setContentsMargins(10, 10, 10, 10)
        main_layout.setSpacing(10)
        
        # Create splitter for resizable sections
        splitter = QSplitter(Qt.Horizontal)
        main_layout.addWidget(splitter)
        
        # Left side - Video area
        self.create_video_section(splitter)
        
        # Right side - Enhanced control panel
        self.create_enhanced_control_panel(splitter)
        
        # Set initial splitter sizes (70% video, 30% controls)
        splitter.setSizes([980, 420])
    
    def update_time(self):
        """Update time display"""
        current_time = datetime.now().strftime("%H:%M:%S")
        self.time_label.setText(current_time)

    def create_video_section(self, parent_splitter):
        """Create video display section"""
        # Video frame
        video_frame = StyledFrame(bg_color=Colors.WHITE, border_color='#E0E0E0', border_width=2)
        parent_splitter.addWidget(video_frame)

        # Video layout
        video_layout = QVBoxLayout(video_frame)
        video_layout.setContentsMargins(0, 0, 0, 0)
        video_layout.setSpacing(0)

        # Video header
        self.create_video_header(video_layout)

        # Video display area
        self.create_video_display(video_layout)

        # Video controls
        self.create_video_controls(video_layout)

    def create_video_header(self, parent_layout):
        """Create video header with real-time indicators"""
        header_frame = StyledFrame(bg_color=Colors.INFO)
        header_frame.setFixedHeight(50)
        parent_layout.addWidget(header_frame)

        header_layout = QHBoxLayout(header_frame)
        header_layout.setContentsMargins(10, 5, 10, 5)

        # Video title
        video_title = QLabel("Live Video Feed with Real-Time AI Detection")
        video_title.setFont(create_font('Arial', 14, bold=True))
        video_title.setStyleSheet("color: white; background: transparent;")
        header_layout.addWidget(video_title)

        # Real-time detection indicators
        indicators_widget = QWidget()
        indicators_layout = QHBoxLayout(indicators_widget)
        indicators_layout.setContentsMargins(0, 0, 0, 0)

        # Detection indicators
        self.video_age_indicator = QLabel("👶")
        self.video_age_indicator.setFont(create_font('Arial', 16))
        self.video_age_indicator.setStyleSheet(f"color: {Colors.GRAY_LIGHT}; background: transparent;")
        indicators_layout.addWidget(self.video_age_indicator)

        self.video_object_indicator = QLabel("🔍")
        self.video_object_indicator.setFont(create_font('Arial', 16))
        self.video_object_indicator.setStyleSheet(f"color: {Colors.GRAY_LIGHT}; background: transparent;")
        indicators_layout.addWidget(self.video_object_indicator)

        self.video_anomaly_indicator = QLabel("🚨")
        self.video_anomaly_indicator.setFont(create_font('Arial', 16))
        self.video_anomaly_indicator.setStyleSheet(f"color: {Colors.GRAY_LIGHT}; background: transparent;")
        indicators_layout.addWidget(self.video_anomaly_indicator)

        header_layout.addWidget(indicators_widget)

        # Store references
        self.video_indicators = {
            'age': self.video_age_indicator,
            'object': self.video_object_indicator,
            'anomaly': self.video_anomaly_indicator
        }

    def create_video_display(self, parent_layout):
        """Create video display area"""
        self.video_label = QLabel()
        self.video_label.setMinimumSize(640, 480)
        self.video_label.setStyleSheet(f"""
            QLabel {{
                background-color: black;
                color: white;
                border: 1px solid {Colors.GRAY_LIGHT};
                font-size: 16px;
                text-align: center;
            }}
        """)
        self.video_label.setAlignment(Qt.AlignCenter)
        self.video_label.setText("🎥 Initializing Camera...\nPress 'Start Camera' to begin\n\nReal-Time Features:\n👶 Age Detection\n🔍 Object Detection\n🚨 Anomaly Detection")
        self.video_label.setWordWrap(True)
        parent_layout.addWidget(self.video_label, 1)  # Expand to fill space

    def create_video_controls(self, parent_layout):
        """Create video controls section"""
        controls_frame = StyledFrame(bg_color=Colors.WHITE)
        parent_layout.addWidget(controls_frame)

        controls_layout = QVBoxLayout(controls_frame)
        controls_layout.setContentsMargins(10, 10, 10, 10)
        controls_layout.setSpacing(10)

        # Primary controls
        self.create_primary_controls(controls_layout)

        # AI Detection controls
        self.create_ai_controls(controls_layout)

    def create_primary_controls(self, parent_layout):
        """Create primary video controls"""
        primary_frame = QWidget()
        primary_layout = QHBoxLayout(primary_frame)
        primary_layout.setContentsMargins(0, 0, 0, 0)
        primary_layout.setSpacing(10)

        # Camera button
        self.camera_btn = QPushButton("📷 Start Camera")
        self.camera_btn.setFont(create_font('Arial', 12, bold=True))
        self.camera_btn.setFixedSize(150, 40)
        self.camera_btn.setCursor(Qt.PointingHandCursor)
        self.camera_btn.setStyleSheet(f"""
            QPushButton {{
                background-color: {Colors.SUCCESS};
                color: white;
                border: none;
                border-radius: 6px;
                padding: 8px 15px;
            }}
            QPushButton:hover {{
                background-color: #2ECC71;
            }}
            QPushButton:pressed {{
                background-color: #27AE60;
            }}
        """)
        self.camera_btn.clicked.connect(self.toggle_camera)
        primary_layout.addWidget(self.camera_btn)

        # Record button
        self.record_btn = QPushButton("🔴 Start Recording")
        self.record_btn.setFont(create_font('Arial', 12, bold=True))
        self.record_btn.setFixedSize(170, 40)
        self.record_btn.setCursor(Qt.PointingHandCursor)
        self.record_btn.setStyleSheet(f"""
            QPushButton {{
                background-color: {Colors.DANGER};
                color: white;
                border: none;
                border-radius: 6px;
                padding: 8px 15px;
            }}
            QPushButton:hover {{
                background-color: #E74C3C;
            }}
            QPushButton:pressed {{
                background-color: #C0392B;
            }}
            QPushButton:disabled {{
                background-color: {Colors.SECONDARY};
                color: {Colors.GRAY_DARK};
            }}
        """)
        self.record_btn.clicked.connect(self.toggle_recording)
        self.record_btn.setEnabled(False)
        primary_layout.addWidget(self.record_btn)

        # Snapshot button
        snapshot_btn = QPushButton("📸 Snapshot")
        snapshot_btn.setFont(create_font('Arial', 11, bold=True))
        snapshot_btn.setFixedSize(130, 40)
        snapshot_btn.setCursor(Qt.PointingHandCursor)
        snapshot_btn.setStyleSheet(f"""
            QPushButton {{
                background-color: {Colors.WARNING};
                color: white;
                border: none;
                border-radius: 6px;
                padding: 8px 12px;
            }}
            QPushButton:hover {{
                background-color: #F39C12;
            }}
            QPushButton:pressed {{
                background-color: #E67E22;
            }}
        """)
        snapshot_btn.clicked.connect(self.take_snapshot)
        primary_layout.addWidget(snapshot_btn)

        # Performance display
        perf_widget = QWidget()
        perf_layout = QVBoxLayout(perf_widget)
        perf_layout.setContentsMargins(0, 0, 0, 0)
        perf_layout.setSpacing(2)

        self.fps_label = QLabel("FPS: 0")
        self.fps_label.setFont(create_font('Arial', 11, bold=True))
        self.fps_label.setStyleSheet(f"color: {Colors.DARK}; background: transparent;")
        perf_layout.addWidget(self.fps_label)

        self.detection_fps_label = QLabel("AI: 0 FPS")
        self.detection_fps_label.setFont(create_font('Arial', 9))
        self.detection_fps_label.setStyleSheet(f"color: {Colors.GRAY_DARK}; background: transparent;")
        perf_layout.addWidget(self.detection_fps_label)

        primary_layout.addWidget(perf_widget)
        primary_layout.addStretch()  # Push everything to the left

        parent_layout.addWidget(primary_frame)

    def create_ai_controls(self, parent_layout):
        """Create AI detection controls"""
        ai_frame = QWidget()
        ai_layout = QHBoxLayout(ai_frame)
        ai_layout.setContentsMargins(0, 0, 0, 0)
        ai_layout.setSpacing(10)

        # Manual detection group
        manual_group = StyledFrame(bg_color='#F8F9FA', border_color='#E0E0E0', border_width=1)
        manual_layout = QVBoxLayout(manual_group)
        manual_layout.setContentsMargins(10, 5, 10, 5)

        manual_label = QLabel("Manual Detection")
        manual_label.setFont(create_font('Arial', 10, bold=True))
        manual_label.setStyleSheet(f"color: {Colors.DARK}; background: transparent;")
        manual_layout.addWidget(manual_label)

        self.expression_btn = QPushButton("😊 Expression")
        self.expression_btn.setFont(create_font('Arial', 10, bold=True))
        self.expression_btn.setFixedSize(120, 35)
        self.expression_btn.setCursor(Qt.PointingHandCursor)
        self.expression_btn.setStyleSheet(f"""
            QPushButton {{
                background-color: #9B59B6;
                color: white;
                border: none;
                border-radius: 4px;
                padding: 8px 10px;
            }}
            QPushButton:hover {{
                background-color: #8E44AD;
            }}
            QPushButton:disabled {{
                background-color: {Colors.SECONDARY};
                color: {Colors.GRAY_DARK};
            }}
        """)
        self.expression_btn.clicked.connect(self.detect_expression)
        self.expression_btn.setEnabled(False)
        manual_layout.addWidget(self.expression_btn)

        ai_layout.addWidget(manual_group)

        # Real-time detection group
        realtime_group = StyledFrame(bg_color='#F8F9FA', border_color='#E0E0E0', border_width=1)
        realtime_layout = QVBoxLayout(realtime_group)
        realtime_layout.setContentsMargins(10, 5, 10, 5)

        realtime_label = QLabel("Real-Time Detection")
        realtime_label.setFont(create_font('Arial', 10, bold=True))
        realtime_label.setStyleSheet(f"color: {Colors.DARK}; background: transparent;")
        realtime_layout.addWidget(realtime_label)

        # Real-time buttons container
        rt_buttons_widget = QWidget()
        rt_buttons_layout = QHBoxLayout(rt_buttons_widget)
        rt_buttons_layout.setContentsMargins(0, 0, 0, 0)
        rt_buttons_layout.setSpacing(5)

        # Age detection button
        self.age_btn = QPushButton("👶 Age")
        self.age_btn.setFont(create_font('Arial', 10, bold=True))
        self.age_btn.setFixedSize(80, 35)
        self.age_btn.setCursor(Qt.PointingHandCursor)
        self.age_btn.setStyleSheet(f"""
            QPushButton {{
                background-color: {Colors.SECONDARY};
                color: white;
                border: none;
                border-radius: 4px;
                padding: 8px;
            }}
            QPushButton:hover {{
                background-color: #7F8C8D;
            }}
            QPushButton:disabled {{
                background-color: {Colors.GRAY_LIGHT};
                color: {Colors.GRAY_DARK};
            }}
        """)
        self.age_btn.clicked.connect(self.toggle_age_detection)
        self.age_btn.setEnabled(False)
        rt_buttons_layout.addWidget(self.age_btn)

        # Object detection button
        self.object_btn = QPushButton("🔍 Objects")
        self.object_btn.setFont(create_font('Arial', 10, bold=True))
        self.object_btn.setFixedSize(100, 35)
        self.object_btn.setCursor(Qt.PointingHandCursor)
        self.object_btn.setStyleSheet(f"""
            QPushButton {{
                background-color: {Colors.SECONDARY};
                color: white;
                border: none;
                border-radius: 4px;
                padding: 8px;
            }}
            QPushButton:hover {{
                background-color: #7F8C8D;
            }}
            QPushButton:disabled {{
                background-color: {Colors.GRAY_LIGHT};
                color: {Colors.GRAY_DARK};
            }}
        """)
        self.object_btn.clicked.connect(self.toggle_object_detection)
        self.object_btn.setEnabled(False)
        rt_buttons_layout.addWidget(self.object_btn)

        # Anomaly detection button
        self.anomaly_btn = QPushButton("🚨 Anomaly")
        self.anomaly_btn.setFont(create_font('Arial', 10, bold=True))
        self.anomaly_btn.setFixedSize(100, 35)
        self.anomaly_btn.setCursor(Qt.PointingHandCursor)
        self.anomaly_btn.setStyleSheet(f"""
            QPushButton {{
                background-color: {Colors.DANGER};
                color: white;
                border: none;
                border-radius: 4px;
                padding: 8px;
            }}
            QPushButton:hover {{
                background-color: #E74C3C;
            }}
            QPushButton:disabled {{
                background-color: {Colors.GRAY_LIGHT};
                color: {Colors.GRAY_DARK};
            }}
        """)
        self.anomaly_btn.clicked.connect(self.toggle_anomaly_detection)
        self.anomaly_btn.setEnabled(False)
        rt_buttons_layout.addWidget(self.anomaly_btn)

        realtime_layout.addWidget(rt_buttons_widget)
        ai_layout.addWidget(realtime_group)

        ai_layout.addStretch()  # Push groups to the left

        parent_layout.addWidget(ai_frame)

    def create_enhanced_control_panel(self, parent_splitter):
        """Create enhanced control panel"""
        # Control panel frame
        control_frame = StyledFrame(bg_color=Colors.WHITE, border_color='#E0E0E0', border_width=2)
        control_frame.setFixedWidth(400)
        parent_splitter.addWidget(control_frame)

        # Control panel layout
        control_layout = QVBoxLayout(control_frame)
        control_layout.setContentsMargins(0, 0, 0, 0)
        control_layout.setSpacing(0)

        # Control panel header
        self.create_control_header(control_layout)

        # Scrollable content area
        self.create_scrollable_content(control_layout)

    def create_control_header(self, parent_layout):
        """Create control panel header"""
        header_frame = StyledFrame(bg_color=Colors.PRIMARY)
        header_frame.setFixedHeight(50)
        parent_layout.addWidget(header_frame)

        header_layout = QVBoxLayout(header_frame)
        header_layout.setAlignment(Qt.AlignCenter)
        header_layout.setContentsMargins(10, 12, 10, 12)

        control_title = QLabel("🎛️ AI Detection Control Panel")
        control_title.setFont(create_font('Arial', 14, bold=True))
        control_title.setStyleSheet("color: white; background: transparent;")
        control_title.setAlignment(Qt.AlignCenter)
        header_layout.addWidget(control_title)

    def create_scrollable_content(self, parent_layout):
        """Create scrollable content area for control sections"""
        # Create scroll area
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarAlwaysOff)
        scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        scroll_area.setStyleSheet("""
            QScrollArea {
                border: none;
                background-color: white;
            }
            QScrollBar:vertical {
                background-color: #F0F0F0;
                width: 12px;
                border-radius: 6px;
            }
            QScrollBar::handle:vertical {
                background-color: #C0C0C0;
                border-radius: 6px;
                min-height: 20px;
            }
            QScrollBar::handle:vertical:hover {
                background-color: #A0A0A0;
            }
        """)
        parent_layout.addWidget(scroll_area)

        # Scrollable content widget
        scrollable_widget = QWidget()
        scroll_area.setWidget(scrollable_widget)

        # Scrollable content layout
        scrollable_layout = QVBoxLayout(scrollable_widget)
        scrollable_layout.setContentsMargins(10, 10, 10, 10)
        scrollable_layout.setSpacing(10)

        # Create control sections
        self.create_statistics_section(scrollable_layout)
        self.create_detection_status_section(scrollable_layout)
        self.create_age_detection_section(scrollable_layout)
        self.create_object_detection_section(scrollable_layout)
        self.create_anomaly_detection_section(scrollable_layout)
        self.create_expression_section(scrollable_layout)
        self.create_action_buttons(scrollable_layout)

        # Add stretch to push content to top
        scrollable_layout.addStretch()

    def create_statistics_section(self, parent_layout):
        """Create enhanced statistics section"""
        stats_frame = StyledFrame(bg_color='#ECF0F1', border_color='#BDC3C7', border_width=1)
        parent_layout.addWidget(stats_frame)

        stats_layout = QVBoxLayout(stats_frame)
        stats_layout.setContentsMargins(10, 10, 10, 10)
        stats_layout.setSpacing(5)

        # Statistics title
        stats_title = QLabel("📊 Session Statistics")
        stats_title.setFont(create_font('Arial', 12, bold=True))
        stats_title.setStyleSheet(f"color: {Colors.DARK}; background: transparent;")
        stats_layout.addWidget(stats_title)

        # Statistics data
        stats_data = [
            ('frames_processed', 'Frames Processed', '📹'),
            ('faces_detected', 'Faces Detected', '👤'),
            ('objects_detected', 'Objects Detected', '🔍'),
            ('anomalies_detected', 'Anomalies Detected', '🚨'),
            ('age_detections', 'Age Detections', '👶'),
            ('recording_time', 'Recording Time (s)', '⏱️')
        ]

        for key, label, icon in stats_data:
            stat_widget = QWidget()
            stat_layout = QHBoxLayout(stat_widget)
            stat_layout.setContentsMargins(0, 0, 0, 0)

            stat_label = QLabel(f"{icon} {label}:")
            stat_label.setFont(create_font('Arial', 10))
            stat_label.setStyleSheet(f"color: {Colors.DARK}; background: transparent;")
            stat_layout.addWidget(stat_label)

            stat_layout.addStretch()

            stat_value = QLabel("0")
            stat_value.setFont(create_font('Arial', 10, bold=True))
            stat_value.setStyleSheet(f"color: {Colors.DANGER}; background: transparent;")
            stat_layout.addWidget(stat_value)

            stats_layout.addWidget(stat_widget)

            # Store reference for updates
            self.stats_labels[key] = stat_value

    def create_detection_status_section(self, parent_layout):
        """Create detection status section"""
        detection_frame = StyledFrame(bg_color='#D5F4E6', border_color='#27AE60', border_width=1)
        parent_layout.addWidget(detection_frame)

        detection_layout = QVBoxLayout(detection_frame)
        detection_layout.setContentsMargins(10, 10, 10, 10)
        detection_layout.setSpacing(5)

        # Detection title
        detection_title = QLabel("🔍 Real-Time Detection Status")
        detection_title.setFont(create_font('Arial', 12, bold=True))
        detection_title.setStyleSheet(f"color: {Colors.DARK}; background: transparent;")
        detection_layout.addWidget(detection_title)

        # Overall detection status
        self.detection_status = QLabel("🟡 Camera Offline")
        self.detection_status.setFont(create_font('Arial', 11, bold=True))
        self.detection_status.setStyleSheet(f"color: {Colors.WARNING}; background: transparent;")
        detection_layout.addWidget(self.detection_status)

        # Detection activity log
        history_label = QLabel("Recent Activity:")
        history_label.setFont(create_font('Arial', 10, bold=True))
        history_label.setStyleSheet(f"color: {Colors.DARK}; background: transparent;")
        detection_layout.addWidget(history_label)

        # Create a simple text area for activity log
        from PyQt5.QtWidgets import QTextEdit
        self.detection_log = QTextEdit()
        self.detection_log.setMaximumHeight(120)
        self.detection_log.setStyleSheet("""
            QTextEdit {
                background-color: white;
                border: 1px solid #BDC3C7;
                border-radius: 4px;
                padding: 5px;
                font-family: Arial;
                font-size: 9px;
            }
        """)
        self.detection_log.setReadOnly(True)
        self.detection_log.append("System ready for real-time detection...")
        detection_layout.addWidget(self.detection_log)

    def create_status_bar(self):
        """Create status bar"""
        status_bar = QStatusBar()
        self.setStatusBar(status_bar)

        # Status message
        status_bar.showMessage("🟢 System Online - Ready for AI Detection")

        # Add permanent widgets to status bar
        ai_status_text = "AI Modules: "
        if self.face_detector: ai_status_text += "Expression✓ "
        if self.object_detector: ai_status_text += "Objects✓ "
        if self.age_detector: ai_status_text += "Age✓"

        ai_status_label = QLabel(ai_status_text)
        ai_status_label.setStyleSheet(f"color: {Colors.INFO}; padding: 5px;")
        status_bar.addPermanentWidget(ai_status_label)

        version_label = QLabel("AI Video Detection v1.0")
        version_label.setStyleSheet(f"color: {Colors.GRAY_DARK}; padding: 5px;")
        status_bar.addPermanentWidget(version_label)

    # Video Control Methods
    def toggle_camera(self):
        """Toggle camera on/off with enhanced error handling"""
        if self.video_cap is None:
            print("🔄 Attempting to start camera...")
            success = self.start_video_capture()
            if success:
                self.camera_btn.setText("📷 Stop Camera")
                self.camera_btn.setStyleSheet(f"""
                    QPushButton {{
                        background-color: {Colors.DANGER};
                        color: white;
                        border: none;
                        border-radius: 6px;
                        padding: 8px 15px;
                    }}
                    QPushButton:hover {{
                        background-color: #E74C3C;
                    }}
                """)
                self.record_btn.setEnabled(True)
                self.expression_btn.setEnabled(True)
                self.age_btn.setEnabled(True)
                self.object_btn.setEnabled(True)
                self.anomaly_btn.setEnabled(True)
                self.detection_status.setText("🟢 Camera Active")
                self.detection_status.setStyleSheet(f"color: {Colors.SUCCESS}; background: transparent;")
                self.statusBar().showMessage("🟢 Camera started - Real-time AI ready")
                print("✅ Camera started - all AI detection enabled")
            else:
                self.statusBar().showMessage("❌ Failed to start camera")
        else:
            print("🔄 Stopping camera...")
            self.stop_video_capture()
            self.camera_btn.setText("📷 Start Camera")
            self.camera_btn.setStyleSheet(f"""
                QPushButton {{
                    background-color: {Colors.SUCCESS};
                    color: white;
                    border: none;
                    border-radius: 6px;
                    padding: 8px 15px;
                }}
                QPushButton:hover {{
                    background-color: #2ECC71;
                }}
            """)
            self.record_btn.setEnabled(False)
            self.expression_btn.setEnabled(False)
            self.age_btn.setEnabled(False)
            self.object_btn.setEnabled(False)
            self.anomaly_btn.setEnabled(False)
            self.detection_status.setText("🟡 Camera Offline")
            self.detection_status.setStyleSheet(f"color: {Colors.WARNING}; background: transparent;")
            self.statusBar().showMessage("🟡 Camera stopped")
            print("✅ Camera stopped")

    def start_video_capture(self):
        """Start video capture with enhanced error handling"""
        try:
            print("📷 Initializing camera for real-time AI detection...")

            # Import OpenCV
            import cv2

            # Try multiple camera indices
            camera_indices = [0, 1, 2]

            for idx in camera_indices:
                print(f"🔍 Trying camera index {idx}...")
                self.video_cap = cv2.VideoCapture(idx)

                if self.video_cap.isOpened():
                    ret, test_frame = self.video_cap.read()
                    if ret and test_frame is not None:
                        print(f"✅ Camera {idx} is working!")
                        break
                    else:
                        print(f"❌ Camera {idx} opened but can't read frames")
                        self.video_cap.release()
                        self.video_cap = None
                else:
                    print(f"❌ Camera {idx} failed to open")
                    if self.video_cap:
                        self.video_cap.release()
                    self.video_cap = None

            if self.video_cap is None:
                print("❌ No working camera found")
                self.video_label.setText("❌ No Camera Found\n\nPlease check:\n1. Camera is connected\n2. Camera permissions\n3. Camera not used by other apps")
                return False

            # Configure camera settings for optimal performance
            self.video_cap.set(cv2.CAP_PROP_FRAME_WIDTH, 640)
            self.video_cap.set(cv2.CAP_PROP_FRAME_HEIGHT, 480)
            self.video_cap.set(cv2.CAP_PROP_FPS, 30)
            self.video_cap.set(cv2.CAP_PROP_BUFFERSIZE, 1)  # Reduce buffer for real-time

            self.is_running = True
            self.frame_counter = 0

            # Start video processing thread
            self.video_thread = threading.Thread(target=self.enhanced_video_loop, daemon=True)
            self.video_thread.start()

            print("✅ Enhanced video capture started successfully")
            return True

        except ImportError:
            print("❌ OpenCV not available")
            self.video_label.setText("❌ OpenCV Not Available\n\nPlease install:\npip install opencv-python")
            return False
        except Exception as e:
            print(f"❌ Error starting camera: {e}")
            self.video_label.setText(f"❌ Camera Error:\n{str(e)}\n\nTry:\n1. Restart application\n2. Check camera permissions")
            return False

    def stop_video_capture(self):
        """Stop video capture"""
        try:
            self.is_running = False

            if self.video_cap:
                self.video_cap.release()
                self.video_cap = None

            if self.video_thread and self.video_thread.is_alive():
                self.video_thread.join(timeout=2.0)

            # Reset video display
            self.video_label.setText("🎥 Camera Stopped\nPress 'Start Camera' to begin\n\nReal-Time Features:\n👶 Age Detection\n🔍 Object Detection\n🚨 Anomaly Detection")

            print("✅ Video capture stopped")

        except Exception as e:
            print(f"❌ Error stopping camera: {e}")

    def toggle_recording(self):
        """Toggle video recording with proper file saving"""
        try:
            if self.is_recording:
                # Stop recording
                if self.video_recorder:
                    success = self.video_recorder.stop_recording()
                    if success:
                        self.is_recording = False
                        self.record_btn.setText("🔴 Start Recording")
                        self.record_btn.setStyleSheet(f"""
                            QPushButton {{
                                background-color: {Colors.DANGER};
                                color: white;
                                border: none;
                                border-radius: 6px;
                                padding: 8px 15px;
                            }}
                            QPushButton:hover {{
                                background-color: #E74C3C;
                            }}
                        """)
                        self.statusBar().showMessage("🟢 Recording saved successfully")
                        print("✅ Recording stopped and saved")
                    else:
                        self.statusBar().showMessage("⚠️ Error stopping recording")
                        print("❌ Error stopping recording")
                else:
                    self.is_recording = False
                    self.record_btn.setText("🔴 Start Recording")
                    print("⚠️ No video recorder available")
            else:
                # Start recording
                if self.current_frame is not None and self.video_recorder:
                    success = self.video_recorder.start_recording(self.current_frame)
                    if success:
                        self.is_recording = True
                        self.record_btn.setText("⏹️ Stop Recording")
                        self.record_btn.setStyleSheet(f"""
                            QPushButton {{
                                background-color: #2E86AB;
                                color: white;
                                border: none;
                                border-radius: 6px;
                                padding: 8px 15px;
                            }}
                            QPushButton:hover {{
                                background-color: #3498DB;
                            }}
                        """)
                        self.statusBar().showMessage("🔴 Recording started")
                        print("✅ Recording started")
                    else:
                        self.statusBar().showMessage("❌ Failed to start recording")
                        print("❌ Failed to start recording")
                else:
                    self.statusBar().showMessage("❌ No video frame available for recording")
                    print("❌ No video frame available for recording")

        except Exception as e:
            print(f"❌ Error toggling recording: {e}")
            self.statusBar().showMessage(f"❌ Recording error: {str(e)}")

    def take_snapshot(self):
        """Take a snapshot of current frame"""
        try:
            if self.current_frame is not None:
                import cv2
                from datetime import datetime
                import os

                # Create snapshots directory if it doesn't exist
                snapshots_dir = "snapshots"
                os.makedirs(snapshots_dir, exist_ok=True)

                # Generate filename with timestamp
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                filename = f"snapshot_{timestamp}.jpg"
                filepath = os.path.join(snapshots_dir, filename)

                # Save the snapshot
                success = cv2.imwrite(filepath, self.current_frame)

                if success:
                    self.statusBar().showMessage(f"📸 Snapshot saved: {filename}")
                    print(f"✅ Snapshot saved: {filepath}")
                else:
                    self.statusBar().showMessage("❌ Failed to save snapshot")
                    print("❌ Failed to save snapshot")
            else:
                self.statusBar().showMessage("❌ No video frame available for snapshot")
                print("❌ No video frame available for snapshot")

        except ImportError:
            self.statusBar().showMessage("❌ OpenCV not available for snapshot")
            print("❌ OpenCV not available for snapshot")
        except Exception as e:
            print(f"❌ Error taking snapshot: {e}")
            self.statusBar().showMessage(f"❌ Snapshot error: {str(e)}")

    def enhanced_video_loop(self):
        """Enhanced video processing loop with real-time AI detection"""
        import cv2
        import numpy as np
        from PyQt5.QtGui import QImage, QPixmap
        from PyQt5.QtCore import QMetaObject, Qt

        frame_count = 0
        detection_count = 0
        fps_start_time = time.time()
        fps_frame_count = 0

        print("🎬 Starting enhanced video loop with real-time AI detection...")

        while self.is_running and self.video_cap and self.video_cap.isOpened():
            try:
                ret, frame = self.video_cap.read()
                if not ret:
                    print("❌ Failed to read frame")
                    break

                frame_count += 1
                fps_frame_count += 1
                self.frame_counter += 1
                self.stats['frames_processed'] = frame_count

                # Store current frame
                self.current_frame = frame.copy()

                # Write frame to video recorder if recording
                if self.is_recording and self.video_recorder and hasattr(self.video_recorder, 'is_recording'):
                    if self.video_recorder.is_recording:
                        self.video_recorder.write_frame(frame)

                # Process real-time AI detections based on interval
                detection_processed = False
                if self.frame_counter % self.detection_interval == 0:
                    detection_count += 1
                    detection_processed = True
                    # Schedule AI processing in main thread
                    QMetaObject.invokeMethod(self, "process_real_time_ai_detection",
                                           Qt.QueuedConnection,
                                           Q_ARG(object, frame.copy()))

                # Calculate FPS
                current_time = time.time()
                if current_time - fps_start_time >= 1.0:
                    fps = fps_frame_count / (current_time - fps_start_time)
                    detection_fps = detection_count / (current_time - fps_start_time) if detection_processed else 0

                    # Update FPS display in main thread
                    QMetaObject.invokeMethod(self, "update_fps_display",
                                           Qt.QueuedConnection,
                                           Q_ARG(float, fps),
                                           Q_ARG(float, detection_fps))

                    fps_start_time = current_time
                    fps_frame_count = 0
                    detection_count = 0

                # Update video display in main thread
                QMetaObject.invokeMethod(self, "update_video_display",
                                       Qt.QueuedConnection,
                                       Q_ARG(object, frame.copy()))

                # Update statistics in main thread
                QMetaObject.invokeMethod(self, "update_statistics_display",
                                       Qt.QueuedConnection)

                # Small delay to prevent overwhelming the system
                time.sleep(0.01)

            except Exception as e:
                print(f"❌ Error in video loop: {e}")
                import traceback
                traceback.print_exc()
                break

        print("🛑 Enhanced video loop stopped")

    def update_video_display(self, frame):
        """Update video display with current frame"""
        try:
            import cv2
            from PyQt5.QtGui import QImage, QPixmap

            # Get display dimensions
            label_size = self.video_label.size()
            display_width = label_size.width() - 20  # Account for padding
            display_height = label_size.height() - 20

            if display_width <= 0 or display_height <= 0:
                display_width, display_height = 640, 480

            # Resize frame to fit display
            frame_resized = cv2.resize(frame, (display_width, display_height))

            # Convert BGR to RGB
            frame_rgb = cv2.cvtColor(frame_resized, cv2.COLOR_BGR2RGB)

            # Convert to QImage
            height, width, channel = frame_rgb.shape
            bytes_per_line = 3 * width
            q_image = QImage(frame_rgb.data, width, height, bytes_per_line, QImage.Format_RGB888)

            # Convert to QPixmap and display
            pixmap = QPixmap.fromImage(q_image)
            self.video_label.setPixmap(pixmap)
            self.video_label.setText("")  # Clear text when showing video

        except Exception as e:
            print(f"❌ Error updating video display: {e}")
            import traceback
            traceback.print_exc()

    def update_fps_display(self, fps, detection_fps):
        """Update FPS display"""
        try:
            self.fps_label.setText(f"FPS: {fps:.1f}")
            self.detection_fps_label.setText(f"AI: {detection_fps:.1f} FPS")
        except Exception as e:
            print(f"❌ Error updating FPS display: {e}")

    def update_statistics_display(self):
        """Update statistics display"""
        try:
            for key, value in self.stats.items():
                if key in self.stats_labels:
                    self.stats_labels[key].setText(str(value))
        except Exception as e:
            print(f"❌ Error updating statistics display: {e}")

    def process_real_time_ai_detection(self, frame):
        """Process real-time AI detection on frame"""
        try:
            detection_results = []

            # Age detection
            if self.real_time_age and self.age_detector:
                try:
                    age_result = self.age_detector.detect_age(frame)
                    if age_result:
                        age = age_result.get('age', 'Unknown')
                        confidence = age_result.get('confidence', 0.0)
                        self.current_age.setText(f"Age: {age}")
                        self.age_confidence.setText(f"Confidence: {confidence:.1f}%")
                        self.stats['age_detections'] += 1
                        detection_results.append(f"Age: {age}")
                except Exception as e:
                    print(f"⚠️ Age detection error: {e}")

            # Object detection
            if self.real_time_objects and self.object_detector:
                try:
                    object_results = self.object_detector.detect_objects(frame)
                    if object_results:
                        objects = [obj.get('class_name', 'Unknown') for obj in object_results]
                        object_count = len(objects)
                        self.objects_detected.setText(f"Objects: {object_count}")
                        self.last_objects.setText(f"Last: {', '.join(objects[:3])}")
                        self.stats['objects_detected'] += object_count
                        detection_results.append(f"Objects: {object_count}")
                except Exception as e:
                    print(f"⚠️ Object detection error: {e}")

            # Anomaly detection
            if self.real_time_anomaly and self.anomaly_system:
                try:
                    anomaly_result = self.anomaly_system.process_frame(frame)
                    if anomaly_result and anomaly_result.get('anomaly_detected', False):
                        anomaly_type = anomaly_result.get('anomaly_type', 'Unknown')
                        confidence = anomaly_result.get('confidence', 0.0)

                        # Update security level
                        self.security_level.setText("🔴 ALERT!")
                        self.security_level.setStyleSheet(f"color: {Colors.DANGER}; background: transparent;")
                        self.threat_level.setText("Threat Level: HIGH")
                        self.last_anomaly.setText(f"Last: {anomaly_type} ({confidence:.1f}%)")

                        # Update indicators
                        self.video_anomaly_indicator.setStyleSheet(f"color: {Colors.DANGER}; background: transparent;")

                        self.stats['anomalies_detected'] += 1
                        detection_results.append(f"ANOMALY: {anomaly_type}")

                        # Schedule reset of security level after 5 seconds
                        QTimer.singleShot(5000, self.reset_security_level)
                    else:
                        # Normal state
                        self.security_level.setText("🟢 SECURE")
                        self.security_level.setStyleSheet(f"color: {Colors.SUCCESS}; background: transparent;")
                        self.threat_level.setText("Threat Level: LOW")

                except Exception as e:
                    print(f"⚠️ Anomaly detection error: {e}")

            # Log detection results
            if detection_results:
                timestamp = datetime.now().strftime("%H:%M:%S")
                result_text = ", ".join(detection_results)
                self.detection_log.append(f"[{timestamp}] Detected: {result_text}")

                # Scroll to bottom
                scrollbar = self.detection_log.verticalScrollBar()
                scrollbar.setValue(scrollbar.maximum())

        except Exception as e:
            print(f"❌ Error in real-time AI detection: {e}")

    def reset_security_level(self):
        """Reset security level to normal after anomaly alert"""
        try:
            if not self.real_time_anomaly:
                return

            self.security_level.setText("🟢 SECURE")
            self.security_level.setStyleSheet(f"color: {Colors.SUCCESS}; background: transparent;")
            self.threat_level.setText("Threat Level: LOW")
            self.video_anomaly_indicator.setStyleSheet(f"color: {Colors.GRAY_LIGHT}; background: transparent;")

        except Exception as e:
            print(f"❌ Error resetting security level: {e}")

    def create_age_detection_section(self, parent_layout):
        """Create age detection results section"""
        age_frame = StyledFrame(bg_color='#FFF3E0', border_color='#F39C12', border_width=1)
        parent_layout.addWidget(age_frame)

        age_layout = QVBoxLayout(age_frame)
        age_layout.setContentsMargins(10, 10, 10, 10)
        age_layout.setSpacing(5)

        # Age detection title
        age_title = QLabel("👶 Real-Time Age Detection")
        age_title.setFont(create_font('Arial', 12, bold=True))
        age_title.setStyleSheet(f"color: {Colors.DARK}; background: transparent;")
        age_layout.addWidget(age_title)

        # Age detection status
        self.age_status = QLabel("🟡 Real-time: OFF")
        self.age_status.setFont(create_font('Arial', 10, bold=True))
        self.age_status.setStyleSheet(f"color: {Colors.WARNING}; background: transparent;")
        age_layout.addWidget(self.age_status)

        # Current age result
        self.current_age = QLabel("No detection yet")
        self.current_age.setFont(create_font('Arial', 14, bold=True))
        self.current_age.setStyleSheet(f"color: #E67E22; background: transparent;")
        age_layout.addWidget(self.current_age)

        # Age range and confidence
        self.age_range = QLabel("Range: Unknown")
        self.age_range.setFont(create_font('Arial', 10))
        self.age_range.setStyleSheet(f"color: {Colors.DARK}; background: transparent;")
        age_layout.addWidget(self.age_range)

        self.age_confidence = QLabel("Confidence: 0%")
        self.age_confidence.setFont(create_font('Arial', 9))
        self.age_confidence.setStyleSheet(f"color: {Colors.GRAY_DARK}; background: transparent;")
        age_layout.addWidget(self.age_confidence)

    def create_object_detection_section(self, parent_layout):
        """Create object detection section"""
        object_frame = StyledFrame(bg_color='#E8F6F3', border_color='#27AE60', border_width=1)
        parent_layout.addWidget(object_frame)

        object_layout = QVBoxLayout(object_frame)
        object_layout.setContentsMargins(10, 10, 10, 10)
        object_layout.setSpacing(5)

        # Object detection title
        object_title = QLabel("🔍 Real-Time Object Detection")
        object_title.setFont(create_font('Arial', 12, bold=True))
        object_title.setStyleSheet(f"color: {Colors.DARK}; background: transparent;")
        object_layout.addWidget(object_title)

        # Object detection status
        self.object_status = QLabel("🟡 Real-time: OFF")
        self.object_status.setFont(create_font('Arial', 10, bold=True))
        self.object_status.setStyleSheet(f"color: {Colors.WARNING}; background: transparent;")
        object_layout.addWidget(self.object_status)

        # Objects detected
        self.objects_detected = QLabel("Objects: None")
        self.objects_detected.setFont(create_font('Arial', 11, bold=True))
        self.objects_detected.setStyleSheet(f"color: {Colors.SUCCESS}; background: transparent;")
        object_layout.addWidget(self.objects_detected)

        # Last detection
        self.last_objects = QLabel("Last: No detection")
        self.last_objects.setFont(create_font('Arial', 9))
        self.last_objects.setStyleSheet(f"color: {Colors.GRAY_DARK}; background: transparent;")
        object_layout.addWidget(self.last_objects)

    def create_anomaly_detection_section(self, parent_layout):
        """Create anomaly detection section"""
        anomaly_frame = StyledFrame(bg_color='#FDEDEC', border_color='#E74C3C', border_width=1)
        parent_layout.addWidget(anomaly_frame)

        anomaly_layout = QVBoxLayout(anomaly_frame)
        anomaly_layout.setContentsMargins(10, 10, 10, 10)
        anomaly_layout.setSpacing(5)

        # Anomaly detection title
        anomaly_title = QLabel("🚨 Real-Time Anomaly Detection")
        anomaly_title.setFont(create_font('Arial', 12, bold=True))
        anomaly_title.setStyleSheet(f"color: {Colors.DARK}; background: transparent;")
        anomaly_layout.addWidget(anomaly_title)

        # Anomaly detection status
        self.anomaly_status = QLabel("🟡 Real-time: OFF")
        self.anomaly_status.setFont(create_font('Arial', 11, bold=True))
        self.anomaly_status.setStyleSheet(f"color: {Colors.WARNING}; background: transparent;")
        anomaly_layout.addWidget(self.anomaly_status)

        # Security level indicator
        self.security_level = QLabel("🟢 SECURE")
        self.security_level.setFont(create_font('Arial', 14, bold=True))
        self.security_level.setStyleSheet(f"color: {Colors.SUCCESS}; background: transparent;")
        anomaly_layout.addWidget(self.security_level)

        # Threat level
        self.threat_level = QLabel("Threat Level: LOW")
        self.threat_level.setFont(create_font('Arial', 10))
        self.threat_level.setStyleSheet(f"color: {Colors.DARK}; background: transparent;")
        anomaly_layout.addWidget(self.threat_level)

        # Last anomaly
        self.last_anomaly = QLabel("Last: No anomalies detected")
        self.last_anomaly.setFont(create_font('Arial', 9))
        self.last_anomaly.setStyleSheet(f"color: {Colors.GRAY_DARK}; background: transparent;")
        anomaly_layout.addWidget(self.last_anomaly)

    def create_expression_section(self, parent_layout):
        """Create facial expression section"""
        expression_frame = StyledFrame(bg_color='#F4E6FF', border_color='#9B59B6', border_width=1)
        parent_layout.addWidget(expression_frame)

        expression_layout = QVBoxLayout(expression_frame)
        expression_layout.setContentsMargins(10, 10, 10, 10)
        expression_layout.setSpacing(5)

        # Expression title
        expression_title = QLabel("😊 Facial Expression Detection")
        expression_title.setFont(create_font('Arial', 12, bold=True))
        expression_title.setStyleSheet(f"color: {Colors.DARK}; background: transparent;")
        expression_layout.addWidget(expression_title)

        # Expression status
        self.expression_status = QLabel("🟡 Manual Mode")
        self.expression_status.setFont(create_font('Arial', 10, bold=True))
        self.expression_status.setStyleSheet(f"color: {Colors.WARNING}; background: transparent;")
        expression_layout.addWidget(self.expression_status)

        # Current expression
        self.current_expression = QLabel("Expression: None")
        self.current_expression.setFont(create_font('Arial', 11, bold=True))
        self.current_expression.setStyleSheet(f"color: #9B59B6; background: transparent;")
        expression_layout.addWidget(self.current_expression)

        # Expression confidence
        self.expression_confidence = QLabel("Confidence: 0%")
        self.expression_confidence.setFont(create_font('Arial', 9))
        self.expression_confidence.setStyleSheet(f"color: {Colors.GRAY_DARK}; background: transparent;")
        expression_layout.addWidget(self.expression_confidence)

    def create_action_buttons(self, parent_layout):
        """Create action buttons section"""
        action_frame = StyledFrame(bg_color='#F8F9FA', border_color='#E0E0E0', border_width=1)
        parent_layout.addWidget(action_frame)

        action_layout = QVBoxLayout(action_frame)
        action_layout.setContentsMargins(10, 10, 10, 10)
        action_layout.setSpacing(10)

        # Action buttons title
        action_title = QLabel("🎛️ Quick Actions")
        action_title.setFont(create_font('Arial', 12, bold=True))
        action_title.setStyleSheet(f"color: {Colors.DARK}; background: transparent;")
        action_layout.addWidget(action_title)

        # Dashboard button
        dashboard_btn = QPushButton("📊 Open Dashboard")
        dashboard_btn.setFont(create_font('Arial', 10, bold=True))
        dashboard_btn.setFixedHeight(35)
        dashboard_btn.setCursor(Qt.PointingHandCursor)
        dashboard_btn.setStyleSheet(f"""
            QPushButton {{
                background-color: {Colors.PRIMARY};
                color: white;
                border: none;
                border-radius: 4px;
                padding: 8px;
            }}
            QPushButton:hover {{
                background-color: {Colors.INFO};
            }}
        """)
        dashboard_btn.clicked.connect(self.open_dashboard)
        action_layout.addWidget(dashboard_btn)

        # Settings button
        settings_btn = QPushButton("⚙️ Settings")
        settings_btn.setFont(create_font('Arial', 10, bold=True))
        settings_btn.setFixedHeight(35)
        settings_btn.setCursor(Qt.PointingHandCursor)
        settings_btn.setStyleSheet(f"""
            QPushButton {{
                background-color: {Colors.SECONDARY};
                color: white;
                border: none;
                border-radius: 4px;
                padding: 8px;
            }}
            QPushButton:hover {{
                background-color: #7F8C8D;
            }}
        """)
        settings_btn.clicked.connect(self.open_settings)
        action_layout.addWidget(settings_btn)

        # Help button
        help_btn = QPushButton("❓ Help")
        help_btn.setFont(create_font('Arial', 10, bold=True))
        help_btn.setFixedHeight(35)
        help_btn.setCursor(Qt.PointingHandCursor)
        help_btn.setStyleSheet(f"""
            QPushButton {{
                background-color: {Colors.WARNING};
                color: white;
                border: none;
                border-radius: 4px;
                padding: 8px;
            }}
            QPushButton:hover {{
                background-color: #F39C12;
            }}
        """)
        help_btn.clicked.connect(self.show_help)
        action_layout.addWidget(help_btn)

    # Action button methods
    def open_dashboard(self):
        """Open analytics dashboard"""
        try:
            print("📊 Opening dashboard...")
            # Try to import and open dashboard
            try:
                from gui.dashboard_window import DashboardWindow
                dashboard = DashboardWindow()
                dashboard.show_dashboard()
                print("✅ Dashboard opened successfully")
            except ImportError:
                show_info_message("Dashboard",
                                "📊 Dashboard feature!\n\n"
                                "This will include:\n"
                                "• Real-time analytics\n"
                                "• Detection reports\n"
                                "• Performance metrics\n"
                                "• Export options",
                                self)
        except Exception as e:
            print(f"❌ Error opening dashboard: {e}")
            show_error_message("Dashboard Error",
                             f"Error opening dashboard:\n{str(e)}",
                             self)

    def open_settings(self):
        """Open settings dialog"""
        try:
            print("⚙️ Opening settings...")
            show_info_message("Settings",
                            "⚙️ Tool Settings\n\n"
                            "This will include:\n"
                            "• Detection parameters\n"
                            "• Camera configuration\n"
                            "• Alert thresholds\n"
                            "• Export preferences",
                            self)
        except Exception as e:
            print(f"❌ Error opening settings: {e}")

    def show_help(self):
        """Show help information"""
        try:
            print("❓ Showing help...")
            help_text = """🛡️ AI Video Detection Tool - Help

🎥 Camera Controls:
• Click 'Start Camera' to begin video capture
• Use 'Start Recording' to save video
• Take snapshots with 'Snapshot' button

🤖 AI Detection:
• Expression: Manual detection with SPACE key
• Age: Real-time age estimation
• Objects: Real-time object detection
• Anomaly: Security anomaly monitoring

⌨️ Keyboard Shortcuts:
• SPACE: Detect facial expression
• ESC: Exit application
• F11: Toggle fullscreen

📊 Dashboard:
• View real-time analytics
• Generate reports
• Export detection data

For more help, check the documentation folder."""

            show_info_message("Help - AI Video Detection Tool", help_text, self)
        except Exception as e:
            print(f"❌ Error showing help: {e}")

    def detect_expression(self):
        """Detect facial expression using custom YOLOv8 model"""
        try:
            if self.current_frame is not None and self.face_detector:
                print("😊 Processing facial expression detection...")

                # Use the custom YOLOv8 expression detector
                result = self.face_detector.detect_expression(self.current_frame)

                if result and 'expression' in result:
                    expression = result['expression']
                    confidence = result.get('confidence', 0.0)

                    # Update UI
                    self.current_expression.setText(f"Expression: {expression}")
                    self.expression_confidence.setText(f"Confidence: {confidence:.1f}%")

                    # Update statistics
                    self.stats['faces_detected'] += 1

                    # Add to activity log
                    timestamp = datetime.now().strftime("%H:%M:%S")
                    self.detection_log.append(f"[{timestamp}] Expression: {expression} ({confidence:.1f}%)")

                    # Scroll to bottom
                    scrollbar = self.detection_log.verticalScrollBar()
                    scrollbar.setValue(scrollbar.maximum())

                    print(f"✅ Expression detected: {expression} ({confidence:.1f}%)")
                else:
                    print("⚠️ No expression detected")
                    self.detection_log.append(f"[{datetime.now().strftime('%H:%M:%S')}] No expression detected")
            else:
                if self.current_frame is None:
                    print("❌ No video frame available")
                    self.statusBar().showMessage("❌ No video frame available for expression detection")
                else:
                    print("❌ Expression detector not available")
                    self.statusBar().showMessage("❌ Expression detector not loaded")

        except Exception as e:
            print(f"❌ Error in expression detection: {e}")
            self.statusBar().showMessage(f"❌ Expression detection error: {str(e)}")

    def toggle_age_detection(self):
        """Toggle real-time age detection"""
        try:
            self.real_time_age = not self.real_time_age

            if self.real_time_age:
                if self.age_detector:
                    self.age_btn.setText("👶 Age ON")
                    self.age_btn.setStyleSheet(f"""
                        QPushButton {{
                            background-color: {Colors.SUCCESS};
                            color: white;
                            border: none;
                            border-radius: 4px;
                            padding: 8px;
                        }}
                        QPushButton:hover {{
                            background-color: #2ECC71;
                        }}
                    """)
                    self.age_status.setText("🟢 Real-time: ON")
                    self.age_status.setStyleSheet(f"color: {Colors.SUCCESS}; background: transparent;")
                    self.age_header_status.setText("👶 Age: ON")
                    self.age_header_status.setStyleSheet(f"color: {Colors.SUCCESS}; background: transparent;")
                    print("✅ Real-time age detection enabled")
                else:
                    self.real_time_age = False
                    print("❌ Age detector not available")
                    self.statusBar().showMessage("❌ Age detector not loaded")
            else:
                self.age_btn.setText("👶 Age")
                self.age_btn.setStyleSheet(f"""
                    QPushButton {{
                        background-color: {Colors.SECONDARY};
                        color: white;
                        border: none;
                        border-radius: 4px;
                        padding: 8px;
                    }}
                    QPushButton:hover {{
                        background-color: #7F8C8D;
                    }}
                """)
                self.age_status.setText("🟡 Real-time: OFF")
                self.age_status.setStyleSheet(f"color: {Colors.WARNING}; background: transparent;")
                self.age_header_status.setText("👶 Age: OFF")
                self.age_header_status.setStyleSheet(f"color: {Colors.SECONDARY}; background: transparent;")
                print("✅ Real-time age detection disabled")

        except Exception as e:
            print(f"❌ Error toggling age detection: {e}")

    def toggle_object_detection(self):
        """Toggle real-time object detection"""
        try:
            self.real_time_objects = not self.real_time_objects

            if self.real_time_objects:
                if self.object_detector:
                    self.object_btn.setText("🔍 Objects ON")
                    self.object_btn.setStyleSheet(f"""
                        QPushButton {{
                            background-color: {Colors.SUCCESS};
                            color: white;
                            border: none;
                            border-radius: 4px;
                            padding: 8px;
                        }}
                        QPushButton:hover {{
                            background-color: #2ECC71;
                        }}
                    """)
                    self.object_status.setText("🟢 Real-time: ON")
                    self.object_status.setStyleSheet(f"color: {Colors.SUCCESS}; background: transparent;")
                    self.object_header_status.setText("🔍 Objects: ON")
                    self.object_header_status.setStyleSheet(f"color: {Colors.SUCCESS}; background: transparent;")
                    print("✅ Real-time object detection enabled")
                else:
                    self.real_time_objects = False
                    print("❌ Object detector not available")
                    self.statusBar().showMessage("❌ Object detector not loaded")
            else:
                self.object_btn.setText("🔍 Objects")
                self.object_btn.setStyleSheet(f"""
                    QPushButton {{
                        background-color: {Colors.SECONDARY};
                        color: white;
                        border: none;
                        border-radius: 4px;
                        padding: 8px;
                    }}
                    QPushButton:hover {{
                        background-color: #7F8C8D;
                    }}
                """)
                self.object_status.setText("🟡 Real-time: OFF")
                self.object_status.setStyleSheet(f"color: {Colors.WARNING}; background: transparent;")
                self.object_header_status.setText("🔍 Objects: OFF")
                self.object_header_status.setStyleSheet(f"color: {Colors.SECONDARY}; background: transparent;")
                print("✅ Real-time object detection disabled")

        except Exception as e:
            print(f"❌ Error toggling object detection: {e}")

    def toggle_anomaly_detection(self):
        """Toggle real-time anomaly detection"""
        try:
            self.real_time_anomaly = not self.real_time_anomaly

            if self.real_time_anomaly:
                if self.anomaly_system:
                    self.anomaly_btn.setText("🚨 Anomaly ON")
                    self.anomaly_btn.setStyleSheet(f"""
                        QPushButton {{
                            background-color: {Colors.SUCCESS};
                            color: white;
                            border: none;
                            border-radius: 4px;
                            padding: 8px;
                        }}
                        QPushButton:hover {{
                            background-color: #2ECC71;
                        }}
                    """)
                    self.anomaly_status.setText("🟢 Real-time: ON")
                    self.anomaly_status.setStyleSheet(f"color: {Colors.SUCCESS}; background: transparent;")
                    self.anomaly_header_status.setText("🚨 Anomaly: ON")
                    self.anomaly_header_status.setStyleSheet(f"color: {Colors.SUCCESS}; background: transparent;")
                    print("✅ Real-time anomaly detection enabled")
                else:
                    self.real_time_anomaly = False
                    print("❌ Anomaly system not available")
                    self.statusBar().showMessage("❌ Anomaly system not loaded")
            else:
                self.anomaly_btn.setText("🚨 Anomaly")
                self.anomaly_btn.setStyleSheet(f"""
                    QPushButton {{
                        background-color: {Colors.DANGER};
                        color: white;
                        border: none;
                        border-radius: 4px;
                        padding: 8px;
                    }}
                    QPushButton:hover {{
                        background-color: #E74C3C;
                    }}
                """)
                self.anomaly_status.setText("🟡 Real-time: OFF")
                self.anomaly_status.setStyleSheet(f"color: {Colors.WARNING}; background: transparent;")
                self.anomaly_header_status.setText("🚨 Anomaly: OFF")
                self.anomaly_header_status.setStyleSheet(f"color: {Colors.SECONDARY}; background: transparent;")
                print("✅ Real-time anomaly detection disabled")

        except Exception as e:
            print(f"❌ Error toggling anomaly detection: {e}")

    def on_closing(self, event):
        """Handle window closing"""
        reply = show_question_message("Exit",
                                    "Are you sure you want to exit the Enhanced AI Video Detection?",
                                    self)
        if reply:
            print("🛑 Shutting down PyQt5 enhanced application...")
            self.is_running = False
            if self.video_cap:
                self.video_cap.release()
            event.accept()
        else:
            event.ignore()

    def run(self):
        """Start the enhanced main application"""
        print("🚀 Starting PyQt5 enhanced main window with real-time AI detection...")

        # Ensure application instance exists first
        app = ensure_application()
        if not app:
            app = QApplication(sys.argv)

        # Now show the window
        self.show()

        return app.exec_()


# Create the enhanced main window class for compatibility
class MainWindow(EnhancedMainWindow):
    """Main window class for compatibility with original Tkinter version"""
    pass


# Standalone execution for testing
if __name__ == "__main__":
    app = ensure_application()
    if not app:
        app = QApplication(sys.argv)

    print("🔧 Testing PyQt5 enhanced main window with real-time AI detection...")
    main_window = MainWindow()
    main_window.show()

    sys.exit(app.exec_())
