import logging
import os
from datetime import datetime

def setup_logger(name="AIVideoDetection", level=logging.INFO):
    """Setup and configure logger"""
    try:
        from utils.config import Config
        config = Config()
    except:
        # Fallback if config import fails
        class FallbackConfig:
            def get_log_path(self, filename):
                os.makedirs("logs", exist_ok=True)
                return os.path.join("logs", filename)
        config = FallbackConfig()
    
    # Create logger
    logger = logging.getLogger(name)
    logger.setLevel(level)
    
    # Clear existing handlers
    logger.handlers.clear()
    
    # Create formatter
    formatter = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    # Console handler
    console_handler = logging.StreamHandler()
    console_handler.setLevel(level)
    console_handler.setFormatter(formatter)
    logger.addHandler(console_handler)
    
    # File handler
    try:
        log_file = config.get_log_path(f"app_{datetime.now().strftime('%Y%m%d')}.log")
        file_handler = logging.FileHandler(log_file)
        file_handler.setLevel(level)
        file_handler.setFormatter(formatter)
        logger.addHandler(file_handler)
    except Exception as e:
        print(f"Warning: Could not create file logger: {e}")
    
    return logger