#!/usr/bin/env python3
"""
Test Script for Restructured AI Video Detection Application
This script tests the new organized structure and import paths
"""

import sys
import os

# Add project root to path
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

def test_imports():
    """Test all critical imports in the new structure"""
    print("🧪 Testing Restructured Application Imports...")
    print("=" * 60)
    
    tests = []
    
    # Test app modules
    try:
        from app.main import main
        tests.append(("✅", "app.main", "Main application entry point"))
    except ImportError as e:
        tests.append(("❌", "app.main", f"Error: {e}"))
    
    try:
        from app.launcher import main as launcher_main
        tests.append(("✅", "app.launcher", "Enhanced launcher"))
    except ImportError as e:
        tests.append(("❌", "app.launcher", f"Error: {e}"))
    
    # Test interface modules
    try:
        from interface.login.login_window import LoginWindow
        tests.append(("✅", "interface.login.login_window", "Login window"))
    except ImportError as e:
        tests.append(("❌", "interface.login.login_window", f"Error: {e}"))
    
    try:
        from interface.main_window.main_window import MainWindow
        tests.append(("✅", "interface.main_window.main_window", "Main window"))
    except ImportError as e:
        tests.append(("❌", "interface.main_window.main_window", f"Error: {e}"))
    
    # Test AI detection modules
    try:
        import ai_detection.facial.expression_detector
        tests.append(("✅", "ai_detection.facial.expression_detector", "Facial expression detection"))
    except ImportError as e:
        tests.append(("❌", "ai_detection.facial.expression_detector", f"Error: {e}"))

    try:
        import ai_detection.facial.age_detector
        tests.append(("✅", "ai_detection.facial.age_detector", "Age detection"))
    except ImportError as e:
        tests.append(("❌", "ai_detection.facial.age_detector", f"Error: {e}"))

    try:
        import ai_detection.object.object_detector
        tests.append(("✅", "ai_detection.object.object_detector", "Object detection"))
    except ImportError as e:
        tests.append(("❌", "ai_detection.object.object_detector", f"Error: {e}"))

    try:
        import ai_detection.anomaly.anomaly_system
        tests.append(("✅", "ai_detection.anomaly.anomaly_system", "Anomaly detection system"))
    except ImportError as e:
        tests.append(("❌", "ai_detection.anomaly.anomaly_system", f"Error: {e}"))

    # Test media processing modules
    try:
        import media_processing.recording.video_recorder
        tests.append(("✅", "media_processing.recording.video_recorder", "Video recording"))
    except ImportError as e:
        tests.append(("❌", "media_processing.recording.video_recorder", f"Error: {e}"))

    # Test data management modules
    try:
        import data_management.database.database_integration
        tests.append(("✅", "data_management.database.database_integration", "Database integration"))
    except ImportError as e:
        tests.append(("❌", "data_management.database.database_integration", f"Error: {e}"))

    # Test system modules
    try:
        import system.config.app_config
        tests.append(("✅", "system.config.app_config", "Application configuration"))
    except ImportError as e:
        tests.append(("❌", "system.config.app_config", f"Error: {e}"))

    try:
        import system.utils.logger
        tests.append(("✅", "system.utils.logger", "Logging utilities"))
    except ImportError as e:
        tests.append(("❌", "system.utils.logger", f"Error: {e}"))
    
    # Print results
    print("\n📊 Import Test Results:")
    print("-" * 60)
    
    success_count = 0
    for status, module, description in tests:
        print(f"{status} {module:<40} {description}")
        if status == "✅":
            success_count += 1
    
    print("-" * 60)
    print(f"✅ Successful imports: {success_count}/{len(tests)}")
    print(f"❌ Failed imports: {len(tests) - success_count}/{len(tests)}")
    
    if success_count == len(tests):
        print("\n🎉 ALL IMPORTS SUCCESSFUL! Restructuring completed successfully!")
        return True
    else:
        print(f"\n⚠️ {len(tests) - success_count} imports failed. Some modules need attention.")
        return False

def test_fallback_imports():
    """Test fallback to old structure"""
    print("\n🔄 Testing Fallback to Legacy Structure...")
    print("=" * 60)
    
    fallback_tests = []
    
    # Test legacy imports
    try:
        from gui.login_window import LoginWindow
        fallback_tests.append(("✅", "gui.login_window", "Legacy login window"))
    except ImportError as e:
        fallback_tests.append(("❌", "gui.login_window", f"Error: {e}"))
    
    try:
        from gui.main_window import MainWindow
        fallback_tests.append(("✅", "gui.main_window", "Legacy main window"))
    except ImportError as e:
        fallback_tests.append(("❌", "gui.main_window", f"Error: {e}"))
    
    try:
        import detection.facial_expression
        fallback_tests.append(("✅", "detection.facial_expression", "Legacy facial expression"))
    except ImportError as e:
        fallback_tests.append(("❌", "detection.facial_expression", f"Error: {e}"))
    
    try:
        from utils.config import Config
        fallback_tests.append(("✅", "utils.config", "Legacy configuration"))
    except ImportError as e:
        fallback_tests.append(("❌", "utils.config", f"Error: {e}"))
    
    # Print fallback results
    print("\n📊 Fallback Test Results:")
    print("-" * 60)
    
    fallback_success = 0
    for status, module, description in fallback_tests:
        print(f"{status} {module:<40} {description}")
        if status == "✅":
            fallback_success += 1
    
    print("-" * 60)
    print(f"✅ Working fallbacks: {fallback_success}/{len(fallback_tests)}")
    
    return fallback_success > 0

def test_basic_functionality():
    """Test basic application functionality"""
    print("\n🔧 Testing Basic Functionality...")
    print("=" * 60)
    
    try:
        # Test configuration loading
        try:
            from system.config.app_config import Config
        except ImportError:
            from utils.config import Config
        
        config = Config()
        print("✅ Configuration loading works")
        
        # Test basic imports
        import cv2
        print("✅ OpenCV available")
        
        import numpy as np
        print("✅ NumPy available")
        
        from PIL import Image
        print("✅ PIL available")
        
        print("\n🎉 Basic functionality test passed!")
        return True
        
    except Exception as e:
        print(f"❌ Basic functionality test failed: {e}")
        return False

def main():
    """Main test function"""
    print("🚀 AI Video Detection - Restructured Application Test")
    print("=" * 80)
    print("Testing the new organized code structure...")
    print()
    
    # Run tests
    imports_ok = test_imports()
    fallback_ok = test_fallback_imports()
    basic_ok = test_basic_functionality()
    
    # Final summary
    print("\n" + "=" * 80)
    print("📋 FINAL TEST SUMMARY")
    print("=" * 80)
    
    if imports_ok:
        print("✅ New structure imports: PASSED")
    else:
        print("⚠️ New structure imports: SOME ISSUES")
    
    if fallback_ok:
        print("✅ Legacy fallback imports: AVAILABLE")
    else:
        print("❌ Legacy fallback imports: NOT AVAILABLE")
    
    if basic_ok:
        print("✅ Basic functionality: WORKING")
    else:
        print("❌ Basic functionality: ISSUES DETECTED")
    
    print()
    if imports_ok or fallback_ok:
        print("🎉 APPLICATION IS FUNCTIONAL!")
        print("💡 You can run the application using:")
        if imports_ok:
            print("   python app/main.py")
            print("   python app/launcher.py")
        print("   python main.py (legacy)")
        print("   python run_enhanced_app.py (legacy)")
    else:
        print("❌ APPLICATION NEEDS ATTENTION")
        print("💡 Check import paths and missing modules")
    
    print("\n🏁 Test completed!")

if __name__ == "__main__":
    main()
