import tkinter as tk
from tkinter import ttk
import math

class iOSTheme:
    """iOS-inspired theme colors and constants"""
    
    # Primary Colors
    PRIMARY_BLUE = "#007AFF"
    SECONDARY_BLUE = "#5AC8FA"
    SUCCESS_GREEN = "#34C759"
    WARNING_ORANGE = "#FF9500"
    DANGER_RED = "#FF3B30"
    
    # Background Colors
    BACKGROUND = "#F2F2F7"
    SURFACE = "#FFFFFF"
    CARD_BACKGROUND = "#FFFFFF"
    
    # Text Colors
    TEXT_PRIMARY = "#000000"
    TEXT_SECONDARY = "#8E8E93"
    TEXT_ON_PRIMARY = "#FFFFFF"
    
    # System Colors
    SEPARATOR = "#C6C6C8"
    SHADOW = "#00000020"
    
    # Font Settings
    FONT_FAMILY = "SF Pro Display"
    FONT_FAMILY_FALLBACK = "Segoe UI"
    
    # Dimensions
    CORNER_RADIUS = 12
    BUTTON_HEIGHT = 44
    CARD_PADDING = 16

class iOSButton:
    """iOS-style button with rounded corners and hover effects"""
    
    def __init__(self, parent, text="Button", command=None, style='primary', 
                 width=120, height=44, corner_radius=12):
        self.parent = parent
        self.text = text
        self.command = command
        self.style = style
        self.width = width
        self.height = height
        self.corner_radius = corner_radius
        self.enabled = True
        
        # Style colors
        self.style_colors = {
            'primary': {
                'bg': iOSTheme.PRIMARY_BLUE,
                'fg': iOSTheme.TEXT_ON_PRIMARY,
                'hover_bg': '#0056CC',
                'disabled_bg': '#C6C6C8',
                'disabled_fg': '#8E8E93'
            },
            'secondary': {
                'bg': iOSTheme.BACKGROUND,
                'fg': iOSTheme.PRIMARY_BLUE,
                'hover_bg': '#E5E5EA',
                'disabled_bg': '#F2F2F7',
                'disabled_fg': '#C6C6C8'
            },
            'success': {
                'bg': iOSTheme.SUCCESS_GREEN,
                'fg': iOSTheme.TEXT_ON_PRIMARY,
                'hover_bg': '#28A745',
                'disabled_bg': '#C6C6C8',
                'disabled_fg': '#8E8E93'
            },
            'warning': {
                'bg': iOSTheme.WARNING_ORANGE,
                'fg': iOSTheme.TEXT_ON_PRIMARY,
                'hover_bg': '#E6851F',
                'disabled_bg': '#C6C6C8',
                'disabled_fg': '#8E8E93'
            },
            'danger': {
                'bg': iOSTheme.DANGER_RED,
                'fg': iOSTheme.TEXT_ON_PRIMARY,
                'hover_bg': '#D70015',
                'disabled_bg': '#C6C6C8',
                'disabled_fg': '#8E8E93'
            }
        }
        
        # Create canvas
        self.canvas = tk.Canvas(parent, width=width, height=height, 
                               highlightthickness=0, bd=0, bg=parent.cget('bg'))
        
        # Draw button
        self.draw_button()
        
        # Bind events
        self.canvas.bind("<Button-1>", self.on_click)
        self.canvas.bind("<Enter>", self.on_enter)
        self.canvas.bind("<Leave>", self.on_leave)
    
    def draw_button(self):
        """Draw the button"""
        self.canvas.delete("all")
        
        colors = self.style_colors.get(self.style, self.style_colors['primary'])
        
        if self.enabled:
            bg_color = colors['bg']
            fg_color = colors['fg']
        else:
            bg_color = colors['disabled_bg']
            fg_color = colors['disabled_fg']
        
        # Draw rounded rectangle
        self.create_rounded_rectangle(2, 2, self.width-2, self.height-2, 
                                     self.corner_radius, fill=bg_color, outline="")
        
        # Draw text
        self.canvas.create_text(self.width//2, self.height//2, text=self.text,
                               fill=fg_color, font=(iOSTheme.FONT_FAMILY_FALLBACK, 12, 'bold'))
    
    def create_rounded_rectangle(self, x1, y1, x2, y2, radius, **kwargs):
        """Create a rounded rectangle"""
        points = []
        
        # Top edge
        points.extend([x1 + radius, y1])
        points.extend([x2 - radius, y1])
        
        # Top-right corner
        for i in range(0, 90, 10):
            angle = math.radians(i)
            x = x2 - radius + radius * math.cos(angle)
            y = y1 + radius - radius * math.sin(angle)
            points.extend([x, y])
        
        # Right edge
        points.extend([x2, y1 + radius])
        points.extend([x2, y2 - radius])
        
        # Bottom-right corner
        for i in range(90, 180, 10):
            angle = math.radians(i)
            x = x2 - radius + radius * math.cos(angle)
            y = y2 - radius - radius * math.sin(angle)
            points.extend([x, y])
        
        # Bottom edge
        points.extend([x2 - radius, y2])
        points.extend([x1 + radius, y2])
        
        # Bottom-left corner
        for i in range(180, 270, 10):
            angle = math.radians(i)
            x = x1 + radius + radius * math.cos(angle)
            y = y2 - radius - radius * math.sin(angle)
            points.extend([x, y])
        
        # Left edge
        points.extend([x1, y2 - radius])
        points.extend([x1, y1 + radius])
        
        # Top-left corner
        for i in range(270, 360, 10):
            angle = math.radians(i)
            x = x1 + radius + radius * math.cos(angle)
            y = y1 + radius - radius * math.sin(angle)
            points.extend([x, y])
        
        return self.canvas.create_polygon(points, smooth=True, **kwargs)
    
    def on_click(self, event):
        """Handle button click"""
        if self.enabled and self.command:
            self.command()
    
    def on_enter(self, event):
        """Handle mouse enter"""
        if self.enabled:
            colors = self.style_colors.get(self.style, self.style_colors['primary'])
            self.canvas.delete("all")
            self.create_rounded_rectangle(2, 2, self.width-2, self.height-2, 
                                         self.corner_radius, fill=colors['hover_bg'], outline="")
            self.canvas.create_text(self.width//2, self.height//2, text=self.text,
                                   fill=colors['fg'], font=(iOSTheme.FONT_FAMILY_FALLBACK, 12, 'bold'))
    
    def on_leave(self, event):
        """Handle mouse leave"""
        if self.enabled:
            self.draw_button()
    
    def config(self, **kwargs):
        """Configure button properties"""
        if 'text' in kwargs:
            self.text = kwargs['text']
        if 'command' in kwargs:
            self.command = kwargs['command']
        self.draw_button()
    
    def set_enabled(self, enabled):
        """Set button enabled state"""
        self.enabled = enabled
        self.draw_button()
    
    def pack(self, **kwargs):
        """Pack the canvas"""
        return self.canvas.pack(**kwargs)
    
    def grid(self, **kwargs):
        """Grid the canvas"""
        return self.canvas.grid(**kwargs)

class iOSCard:
    """iOS-style card container with shadow and rounded corners"""
    
    def __init__(self, parent, corner_radius=12, shadow=True, bg_color=None):
        self.parent = parent
        self.corner_radius = corner_radius
        self.shadow = shadow
        self.bg_color = bg_color or iOSTheme.CARD_BACKGROUND
        
        # Create main frame
        self.frame = tk.Frame(parent, bg=parent.cget('bg'))
        
        # Create content frame
        self.content_frame = tk.Frame(self.frame, bg=self.bg_color, relief='flat', bd=0)
        self.content_frame.pack(fill='both', expand=True, padx=2, pady=2)
    
    def get_content_frame(self):
        """Get the content frame for adding widgets"""
        return self.content_frame
    
    def pack(self, **kwargs):
        """Pack the card frame"""
        return self.frame.pack(**kwargs)
    
    def grid(self, **kwargs):
        """Grid the card frame"""
        return self.frame.grid(**kwargs)

class iOSStatusIndicator:
    """iOS-style status indicator with colored dot and text"""
    
    def __init__(self, parent, text="Status", status="inactive"):
        self.parent = parent
        self.text = text
        self.status = status
        
        # Status colors
        self.status_colors = {
            'active': iOSTheme.SUCCESS_GREEN,
            'inactive': iOSTheme.TEXT_SECONDARY,
            'warning': iOSTheme.WARNING_ORANGE,
            'danger': iOSTheme.DANGER_RED
        }
        
        # Create container frame
        self.frame = tk.Frame(parent, bg=parent.cget('bg'))
        
        # Create status dot
        self.dot_canvas = tk.Canvas(self.frame, width=12, height=12, 
                                   highlightthickness=0, bd=0, bg=parent.cget('bg'))
        self.dot_canvas.pack(side='left', padx=(0, 6))
        
        # Create text label
        self.label = tk.Label(self.frame, text=text, 
                             font=(iOSTheme.FONT_FAMILY_FALLBACK, 11),
                             bg=parent.cget('bg'), fg=iOSTheme.TEXT_SECONDARY)
        self.label.pack(side='left')
        
        # Draw initial status
        self.update_status()
    
    def update_status(self):
        """Update the status indicator"""
        self.dot_canvas.delete("all")
        color = self.status_colors.get(self.status, self.status_colors['inactive'])
        self.dot_canvas.create_oval(2, 2, 10, 10, fill=color, outline="")
    
    def set_status(self, status):
        """Set the status"""
        self.status = status
        self.update_status()
    
    def set_text(self, text):
        """Set the text"""
        self.text = text
        self.label.config(text=text)
    
    def pack(self, **kwargs):
        """Pack the status indicator"""
        return self.frame.pack(**kwargs)
    
    def grid(self, **kwargs):
        """Grid the status indicator"""
        return self.frame.grid(**kwargs)
