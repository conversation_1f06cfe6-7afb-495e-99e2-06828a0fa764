@echo off
title AI Video Detection - PyQt5 Enhanced Edition
color 0B

echo.
echo ===============================================
echo    AI Video Detection - PyQt5 Enhanced
echo ===============================================
echo.

:: Check Python installation
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python not found in PATH
    echo Please install Python 3.7+ and add to PATH
    pause
    exit /b 1
)

echo ✅ Python installation found
echo.

:: Check PyQt5 installation
echo 🔍 Checking PyQt5 installation...
python -c "import PyQt5; print('✅ PyQt5 version:', PyQt5.QtCore.QT_VERSION_STR)" 2>nul
if errorlevel 1 (
    echo ❌ PyQt5 not found
    echo.
    echo 📦 Installing PyQt5...
    pip install PyQt5>=5.15.0
    if errorlevel 1 (
        echo ❌ Failed to install PyQt5
        echo Please install manually: pip install PyQt5
        pause
        exit /b 1
    )
    echo ✅ PyQt5 installed successfully
) else (
    echo ✅ PyQt5 is available
)

echo.

:: Check other dependencies
echo 🔍 Checking other dependencies...
python -c "import cv2, numpy, PIL" 2>nul
if errorlevel 1 (
    echo ⚠️ Some dependencies missing, installing...
    pip install opencv-python numpy pillow
)

echo.

:: Launch PyQt5 app
echo 🚀 Starting AI Video Detection (PyQt5 Enhanced)...
echo.
echo Features:
echo • Modern PyQt5 interface
echo • Enhanced performance
echo • Professional desktop experience
echo • All original AI detection capabilities
echo.

:: Try PyQt5 launcher first
python app/launcher_pyqt5.py
if errorlevel 1 (
    echo.
    echo ⚠️ PyQt5 launcher failed, trying main PyQt5 entry point...
    python main_pyqt5.py
    if errorlevel 1 (
        echo.
        echo ⚠️ PyQt5 version failed, falling back to enhanced launcher...
        python app/launcher.py
        if errorlevel 1 (
            echo.
            echo ⚠️ Enhanced launcher failed, trying original...
            python main.py
        )
    )
)

:: Handle exit
if errorlevel 1 (
    echo.
    echo ❌ Error occurred - check camera permissions and dependencies
    echo.
    echo Troubleshooting:
    echo 1. Ensure camera is connected and not used by other apps
    echo 2. Check PyQt5 installation: pip install PyQt5
    echo 3. Install dependencies: pip install opencv-python numpy pillow
    echo 4. Run as administrator if needed
    pause
)

echo.
echo 👋 PyQt5 session ended
pause
