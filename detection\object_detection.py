"""
YOLO Object Detection Module for Anomaly Detection
Updated to use "human" instead of "person"
"""

import cv2
import numpy as np
import logging
from typing import List, Tuple, Dict
from utils.config import Config

class ObjectDetector:
    """YOLO-based object detection for anomaly detection with human detection"""
    
    def __init__(self):
        self.config = Config()
        self.logger = logging.getLogger(__name__)
        
        # Initialize YOLO
        self.net = None
        self.output_layers = []
        self.classes = []
        self.human_detected = False
        self.last_human_count = 0
        
        self._load_yolo_model()
        self._load_class_names()
    
    def _load_yolo_model(self):
        """Load YOLO model"""
        try:
            weights_path = self.config.get_model_path("yolov3.weights")
            config_path = self.config.get_model_path("yolov3.cfg")
            
            self.net = cv2.dnn.readNet(weights_path, config_path)
            
            # Get output layer names
            layer_names = self.net.getLayerNames()
            unconnected_layers = self.net.getUnconnectedOutLayers()
            
            # Handle different OpenCV versions
            if isinstance(unconnected_layers[0], (list, tuple)):
                self.output_layers = [layer_names[i[0] - 1] for i in unconnected_layers]
            else:
                self.output_layers = [layer_names[i - 1] for i in unconnected_layers]
            
            self.logger.info("YOLO model loaded successfully")
            
        except Exception as e:
            self.logger.error(f"Error loading YOLO model: {e}")
            self.net = None
    
    def _load_class_names(self):
        """Load COCO class names and replace 'person' with 'human'"""
        try:
            coco_path = self.config.get_model_path("coco.names")
            with open(coco_path, "r") as f:
                self.classes = [line.strip() for line in f.readlines()]
            
            # Replace 'person' with 'human' in class names
            for i, class_name in enumerate(self.classes):
                if class_name.lower() == 'person':
                    self.classes[i] = 'human'
                    print(f"✅ Changed 'person' to 'human' in class {i}")
            
            self.logger.info(f"Loaded {len(self.classes)} class names with human detection")
            
        except Exception as e:
            self.logger.error(f"Error loading class names: {e}")
            self.classes = []
    
    def detect_objects(self, frame: np.ndarray) -> Tuple[List[Dict], np.ndarray, bool]:
        if self.net is None:
            return [], frame, False
        
        annotated_frame = frame.copy()
        height, width = frame.shape[:2]
        
        # Prepare input blob
        blob = cv2.dnn.blobFromImage(
            frame, 0.00392, self.config.YOLO_INPUT_SIZE, 
            (0, 0, 0), True, crop=False
        )
        
        # Run detection
        self.net.setInput(blob)
        outputs = self.net.forward(self.output_layers)
        
        # Parse detections
        detections = self._parse_detections(outputs, width, height)
        
        # Apply non-maximum suppression
        filtered_detections = self._apply_nms(detections)
        
        # Check for humans and anomalies
        anomaly_detected = False
        human_count = 0
        
        for detection in filtered_detections:
            if detection['class_name'] == 'human':
                human_count += 1
                self._draw_human_box(annotated_frame, detection)
            elif detection['class_name'] in self.config.ANOMALIES:
                anomaly_detected = True
                self._draw_anomaly_box(annotated_frame, detection)
            else:
                self._draw_normal_box(annotated_frame, detection)
        
        # Update human detection status
        self.human_detected = human_count > 0
        self.last_human_count = human_count

        # Log human detection
        if self.human_detected:
            print(f"👤 Detected {human_count} human(s) in frame")

        # Log detections to database if database is available
        self._log_detections_to_database(filtered_detections)

        return filtered_detections, annotated_frame, anomaly_detected

    def _log_detections_to_database(self, detections):
        """Log object detections to database for analytics"""
        try:
            # Import database integration
            from utils.database_integration import get_database

            database = get_database()
            if database:
                for detection in detections:
                    # Extract detection information with enhanced data
                    object_name = detection.get('class_name', 'unknown')
                    confidence = detection.get('confidence', 0.0)
                    bbox = detection.get('bbox', (0, 0, 0, 0))

                    # Check if object is considered anomalous
                    anomaly_objects = getattr(self.config, 'ANOMALIES', ['weapon', 'knife', 'gun', 'suspicious_object'])
                    is_anomaly = object_name.lower() in [obj.lower() for obj in anomaly_objects]

                    # Use YOLOv8 as detection method
                    detection_method = 'YOLOv8'

                    # Log to unified database immediately
                    success = database.log_object_detection(
                        object_name=object_name,
                        confidence=confidence,
                        bbox=bbox,
                        detection_method=detection_method,
                        is_anomaly=is_anomaly
                    )

                    if success:
                        anomaly_flag = " [ANOMALY]" if is_anomaly else ""
                        print(f"📊 Object detection logged to unified DB: {object_name} ({confidence:.1%}){anomaly_flag} - Bbox: {bbox}")
                    else:
                        print(f"❌ Failed to log object detection: {object_name}")

        except Exception as e:
            print(f"⚠️ Error logging object detection to database: {e}")
            # Continue processing even if database logging fails
            pass

    def _parse_detections(self, outputs: List, width: int, height: int) -> List[Dict]:
        """Parse YOLO detection outputs"""
        detections = []
        
        for output in outputs:
            for detection in output:
                scores = detection[5:]
                class_id = np.argmax(scores)
                confidence = scores[class_id]
                
                if confidence > self.config.CONFIDENCE_THRESHOLD:
                    # Get bounding box coordinates
                    center_x = int(detection[0] * width)
                    center_y = int(detection[1] * height)
                    w = int(detection[2] * width)
                    h = int(detection[3] * height)
                    x = int(center_x - w / 2)
                    y = int(center_y - h / 2)
                    
                    # Ensure coordinates are within frame bounds
                    x = max(0, min(x, width))
                    y = max(0, min(y, height))
                    w = min(w, width - x)
                    h = min(h, height - y)
                    
                    detection_dict = {
                        'bbox': [x, y, w, h],
                        'confidence': float(confidence),
                        'class_id': int(class_id),
                        'class_name': self.classes[class_id] if class_id < len(self.classes) else "unknown"
                    }
                    detections.append(detection_dict)
        
        return detections
    
    def _apply_nms(self, detections: List[Dict]) -> List[Dict]:
        """Apply Non-Maximum Suppression to remove duplicate detections"""
        if not detections:
            return []
        
        boxes = [det['bbox'] for det in detections]
        confidences = [det['confidence'] for det in detections]
        
        # Apply NMS
        indices = cv2.dnn.NMSBoxes(
            boxes, confidences, 
            self.config.CONFIDENCE_THRESHOLD, 
            self.config.NMS_THRESHOLD
        )
        
        # Filter detections based on NMS results
        filtered_detections = []
        if len(indices) > 0:
            for i in indices.flatten():
                filtered_detections.append(detections[i])
        
        return filtered_detections
    
    def _draw_human_box(self, frame: np.ndarray, detection: Dict):
        """Draw bounding box for detected humans"""
        x, y, w, h = detection['bbox']
        confidence = detection['confidence']
        
        # Draw blue rectangle for humans
        cv2.rectangle(frame, (x, y), (x + w, y + h), (255, 0, 0), 3)  # Blue
        
        # Draw human label
        label = f"HUMAN: {confidence:.2f}"
        self._draw_label(frame, label, (x, y), (255, 0, 0))
        
        # Add human icon or indicator
        cv2.putText(frame, "👤", (x + w - 30, y + 25), 
                   cv2.FONT_HERSHEY_SIMPLEX, 0.8, (255, 255, 255), 2)
    
    def _draw_normal_box(self, frame: np.ndarray, detection: Dict):
        """Draw bounding box for normal objects"""
        x, y, w, h = detection['bbox']
        class_name = detection['class_name']
        confidence = detection['confidence']
        
        # Draw green rectangle
        cv2.rectangle(frame, (x, y), (x + w, y + h), self.config.COLOR_GREEN, 2)
        
        # Draw label
        label = f"{class_name}: {confidence:.2f}"
        self._draw_label(frame, label, (x, y), self.config.COLOR_GREEN)
    
    def _draw_anomaly_box(self, frame: np.ndarray, detection: Dict):
        """Draw bounding box for anomaly objects"""
        x, y, w, h = detection['bbox']
        class_name = detection['class_name']
        confidence = detection['confidence']
        
        # Draw red rectangle
        cv2.rectangle(frame, (x, y), (x + w, y + h), self.config.COLOR_RED, 3)
        
        # Draw warning label
        label = f"WARNING: {class_name.upper()}"
        self._draw_label(frame, label, (x, y), self.config.COLOR_RED)
        
        # Add confidence
        conf_label = f"Conf: {confidence:.2f}"
        self._draw_label(frame, conf_label, (x, y + 30), self.config.COLOR_RED)
    
    def _draw_label(self, frame: np.ndarray, text: str, position: Tuple[int, int], color: Tuple[int, int, int]):
        """Draw label with background"""
        x, y = position
        
        # Get text size
        font = cv2.FONT_HERSHEY_SIMPLEX
        font_scale = 0.6
        thickness = 2
        text_size = cv2.getTextSize(text, font, font_scale, thickness)[0]
        
        # Draw background rectangle
        cv2.rectangle(frame, (x, y - text_size[1] - 10), 
                     (x + text_size[0], y), color, -1)
        
        # Draw text
        cv2.putText(frame, text, (x, y - 5), font, font_scale, (255, 255, 255), thickness)
    
    def get_anomaly_count(self, detections: List[Dict]) -> int:
        """Get count of detected anomalies"""
        return sum(1 for det in detections if det['class_name'] in self.config.ANOMALIES)
    
    def get_human_count(self, detections: List[Dict]) -> int:
        """Get count of detected humans"""
        return sum(1 for det in detections if det['class_name'] == 'human')
    
    def is_human_detected(self) -> bool:
        """Check if humans are currently detected"""
        return self.human_detected
    
    def get_last_human_count(self) -> int:
        """Get the count of humans from last detection"""
        return self.last_human_count
    
    def get_human_detections(self, detections: List[Dict]) -> List[Dict]:
        """Get only human detections from detection list"""
        return [det for det in detections if det['class_name'] == 'human']